version: '3.8'

services:
  # MinIO S3-compatible storage
  minio:
    image: minio/minio:latest
    container_name: datalake-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Create MinIO bucket
  minio-setup:
    image: minio/mc:latest
    container_name: datalake-minio-setup
    depends_on:
      minio:
        condition: service_healthy
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc alias set myminio http://minio:9000 minioadmin minioadmin;
      /usr/bin/mc mb myminio/warehouse --ignore-existing;
      /usr/bin/mc mb myminio/test-bucket --ignore-existing;
      /usr/bin/mc policy set public myminio/warehouse;
      /usr/bin/mc policy set public myminio/test-bucket;
      echo 'MinIO setup complete';
      "

  # Apache Iceberg REST Catalog
  iceberg-rest:
    image: tabulario/iceberg-rest:latest
    container_name: datalake-iceberg-rest
    ports:
      - "8181:8181"
    environment:
      CATALOG_WAREHOUSE: s3://warehouse/
      CATALOG_IO__IMPL: org.apache.iceberg.aws.s3.S3FileIO
      CATALOG_S3_ENDPOINT: http://minio:9000
      CATALOG_S3_ACCESS_KEY: minioadmin
      CATALOG_S3_SECRET_KEY: minioadmin
      CATALOG_S3_PATH_STYLE_ACCESS: "true"
    depends_on:
      minio-setup:
        condition: service_completed_successfully
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8181/v1/config"]
      interval: 30s
      timeout: 10s
      retries: 5

  # PostgreSQL for Iceberg metadata (optional, for more advanced setups)
  postgres:
    image: postgres:15
    container_name: datalake-postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: iceberg
      POSTGRES_USER: iceberg
      POSTGRES_PASSWORD: iceberg
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U iceberg"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Spark for advanced testing (optional)
  spark:
    image: apache/spark:3.5.0
    container_name: datalake-spark
    ports:
      - "4040:4040"
      - "8080:8080"
    environment:
      SPARK_MODE: master
      SPARK_MASTER_HOST: spark
    depends_on:
      iceberg-rest:
        condition: service_healthy
    volumes:
      - ./test-data:/opt/spark/test-data
    command: >
      /bin/bash -c "
      /opt/spark/bin/spark-shell --packages org.apache.iceberg:iceberg-spark-runtime-3.5_2.12:1.4.2,org.apache.iceberg:iceberg-aws:1.4.2 --conf spark.sql.extensions=org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions --conf spark.sql.catalog.iceberg=org.apache.iceberg.spark.SparkCatalog --conf spark.sql.catalog.iceberg.type=rest --conf spark.sql.catalog.iceberg.uri=http://iceberg-rest:8181 --conf spark.sql.catalog.iceberg.io-impl=org.apache.iceberg.aws.s3.S3FileIO --conf spark.sql.catalog.iceberg.s3.endpoint=http://minio:9000 --conf spark.sql.catalog.iceberg.s3.access-key-id=minioadmin --conf spark.sql.catalog.iceberg.s3.secret-access-key=minioadmin --conf spark.sql.catalog.iceberg.s3.path-style-access=true
      "

volumes:
  minio_data:
  postgres_data:

networks:
  default:
    name: datalake-test-network

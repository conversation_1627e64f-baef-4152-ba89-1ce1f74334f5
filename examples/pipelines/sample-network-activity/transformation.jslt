{
  "class_uid": 4001,
  "activity_id": 1,
  "activity_name": "Network Activity",
  "category_name": "Network Activity",
  "category_uid": 4,
  "class_name": "Network Activity",
  "severity_id": 1,
  "severity": "Informational",
  "type_name": "Network Activity: Unknown",
  "type_uid": 400100,
  "time": .timestamp,
  "timestamp": .timestamp,
  "metadata": {
    "version": "1.3.0",
    "product": {
      "name": "Core GoLLMSlake",
      "vendor_name": "IBM",
      "version": "1.0.0"
    },
    "profiles": ["network"]
  },
  "src_endpoint": {
    "ip": .source_ip,
    "port": .source_port,
    "hostname": .source_hostname
  },
  "dst_endpoint": {
    "ip": .destination_ip,
    "port": .destination_port,
    "hostname": .destination_hostname
  },
  "traffic": {
    "bytes": .bytes_transferred,
    "bytes_in": .bytes_in,
    "bytes_out": .bytes_out,
    "packets": .packet_count
  },
  "connection_info": {
    "protocol_name": .protocol,
    "protocol_num": (if .protocol == "TCP" then 6 elif .protocol == "UDP" then 17 else null end),
    "direction": .direction,
    "direction_id": (if .direction == "Inbound" then 1 elif .direction == "Outbound" then 2 else 0 end)
  },
  "status": .status,
  "status_id": (if .status == "Success" then 1 elif .status == "Failure" then 2 else 0 end),
  "duration": .duration,
  "start_time": .start_time,
  "end_time": .end_time,
  "enrichments": {
    "processed_time": $processedTime,
    "source_owner_id": $sourceOwnerId,
    "event_uuid": $eventUuid,
    "dataset": $dataset,
    "namespace": $namespace
  },
  "raw_data": $logString
}

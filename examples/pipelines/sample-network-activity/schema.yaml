type: object
properties:
  class_uid:
    type: integer
    const: 4001
  activity_id:
    type: integer
    minimum: 1
    maximum: 99
  activity_name:
    type: string
  category_name:
    type: string
  category_uid:
    type: integer
  class_name:
    type: string
  severity_id:
    type: integer
    minimum: 0
    maximum: 6
  severity:
    type: string
    enum: ["Unknown", "Informational", "Low", "Medium", "High", "Critical", "Fatal"]
  type_name:
    type: string
  type_uid:
    type: integer
  time:
    type: integer
    minimum: 0
  timestamp:
    type: integer
    minimum: 0
  metadata:
    type: object
    properties:
      version:
        type: string
      product:
        type: object
        properties:
          name:
            type: string
          vendor_name:
            type: string
          version:
            type: string
        required: ["name", "vendor_name"]
      profiles:
        type: array
        items:
          type: string
    required: ["version", "product"]
  src_endpoint:
    type: object
    properties:
      ip:
        type: string
        format: ipv4
      port:
        type: integer
        minimum: 1
        maximum: 65535
      hostname:
        type: string
    required: ["ip"]
  dst_endpoint:
    type: object
    properties:
      ip:
        type: string
        format: ipv4
      port:
        type: integer
        minimum: 1
        maximum: 65535
      hostname:
        type: string
    required: ["ip"]
  traffic:
    type: object
    properties:
      bytes:
        type: integer
        minimum: 0
      bytes_in:
        type: integer
        minimum: 0
      bytes_out:
        type: integer
        minimum: 0
      packets:
        type: integer
        minimum: 0
  connection_info:
    type: object
    properties:
      protocol_name:
        type: string
        enum: ["TCP", "UDP", "ICMP"]
      protocol_num:
        type: integer
        minimum: 0
        maximum: 255
      direction:
        type: string
        enum: ["Inbound", "Outbound", "Lateral", "Unknown"]
      direction_id:
        type: integer
        minimum: 0
        maximum: 99
  status:
    type: string
  status_id:
    type: integer
    minimum: 0
    maximum: 99
  duration:
    type: integer
    minimum: 0
  start_time:
    type: integer
    minimum: 0
  end_time:
    type: integer
    minimum: 0
  enrichments:
    type: object
    properties:
      processed_time:
        type: string
        format: date-time
      source_owner_id:
        type: string
        format: uuid
      event_uuid:
        type: string
        format: uuid
      dataset:
        type: string
      namespace:
        type: string
    required: ["processed_time"]
  raw_data:
    type: string
required:
  - class_uid
  - activity_id
  - category_uid
  - severity_id
  - time
  - timestamp
  - metadata
  - src_endpoint
  - dst_endpoint

apiVersion: v1
kind: ConfigMap
metadata:
  name: iks-ca-configmap
  namespace: kube-system
data:
  coresTotal: 0:320000
  expander: random
  expendablePodsPriorityCutoff: "-10"
  extractor: ""
  enforceNodeGroupMinSize: "true"
  ignoreDaemonsetsUtilization: "false"
  imagePullPolicy: Always
  livenessProbeFailureThreshold: "3"
  livenessProbePeriodSeconds: "10"
  livenessProbeTimeoutSeconds: "10"
  logLevel: info
  maxBulkSoftTaintCount: "0"
  maxBulkSoftTaintTime: 10m
  maxDrainParallelism: "3"
  maxEmptyBulkDelete: "10"
  maxFailingTime: 15m
  maxGracefulTerminationSec: "600"
  maxInactivity: 10m
  maxNodeProvisionTime: 120m
  maxRetryGap: "60"
  maxTotalUnreadyPercentage: "45"
  memoryTotal: 0:6400000
  minReplicaCount: "0"
  newPodScaleUpDelay: 0s
  nodeDeleteDelayAfterTaint: 10m
  okTotalUnreadyCount: "3"
  resourcesLimitsCPU: 800m
  resourcesLimitsMemory: 1000Mi
  resourcesRequestsCPU: 200m
  resourcesRequestsMemory: 400Mi
  retryAttempts: "64"
  scaleDownCandidatesPoolMinCount: "50"
  scaleDownCandidatesPoolRatio: "0.1"
  scaleDownDelayAfterAdd: 2m
  scaleDownDelayAfterDelete: 1m
  scaleDownDelayAfterFailure: 3m
  scaleDownEnabled: "true"
  scaleDownNonEmptyCandidatesCount: "30"
  scaleDownUnneededTime: 10m
  scaleDownUnreadyTime: 20m
  scaleDownUtilizationThreshold: "0.8"
  scanInterval: 1m
  skipNodesWithLocalStorage: "false"
  skipNodesWithSystemPods: "false"
  unremovableNodeRecheckTimeout: 5m
  workerPoolsConfig.json: |
    [
      {"name": "default","minSize": 1,"maxSize": 1,"enabled": true},
      {"name": "cassandra-nov06","minSize": 1,"maxSize": 4,"enabled": true},
      {"name": "kafka-oct29","minSize": 1,"maxSize": 7,"enabled": true},
      {"name": "opensearch-hot-oct04","minSize": 1,"maxSize": 9,"enabled": true},
      {"name": "opensearch-hot-oct27","minSize": 1,"maxSize": 8,"enabled": true},
      {"name": "opensearch-snapshot-oct24","minSize": 1,"maxSize": 5,"enabled": true},
      {"name": "trino-oct13","minSize": 1,"maxSize": 5,"enabled": true},
      {"name": "alerts-workload-oct30", "minSize": 1, "maxSize": 4, "enabled": true},
      {"name": "unified-workload-oct28", "minSize": 1, "maxSize": 1, "enabled": true},
      {"name": "unified-workload-nov10", "minSize": 1, "maxSize": 18, "enabled": true},
      {"name": "unified-workload-mem-oct06", "minSize": 1, "maxSize": 9, "enabled": true}
    ]

---
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base/cloud
patches:
  - target:
      kind: ConfigMap
      name: iks-ca-configmap
    patch: |
      - op: replace
        path: /data/workerPoolsConfig.json
        value: |
          [
            {"name": "cassandra-nov06","minSize": 1,"maxSize": 1,"enabled": true},
            {"name": "default","minSize": 1,"maxSize": 1,"enabled": true},
            {"name": "kafka-oct29","minSize": 1,"maxSize": 1,"enabled": true},
            {"name": "opensearch-hot-oct04","minSize": 1,"maxSize": 2,"enabled": true},
            {"name": "opensearch-hot-oct27","minSize": 1,"maxSize": 2,"enabled": true},
            {"name": "opensearch-snapshot-oct24","minSize": 1,"maxSize": 2,"enabled": true},
            {"name": "trino-oct13","minSize": 1,"maxSize": 1,"enabled": true},
            {"name": "alerts-workload-oct30", "minSize": 1, "maxSize": 2, "enabled": true},
            {"name": "unified-workload-oct28", "minSize": 1, "maxSize": 1, "enabled": true},
            {"name": "unified-workload-nov10", "minSize": 1, "maxSize": 2, "enabled": true},
            {"name": "unified-workload-mem-oct06", "minSize": 1, "maxSize": 2, "enabled": true}
          ]

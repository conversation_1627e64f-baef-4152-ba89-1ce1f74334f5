---
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: trino
  namespace: flux-system
  annotations:
    "magna.ciso.ibm.com/component-domain": "core"
spec:
  interval: 10m
  timeout: 120m
  chart:
    spec:
      chart: trino
      version: '1.39.1'
      interval: 5m
      sourceRef:
        kind: HelmRepository
        name: trinodb
  releaseName: trino
  targetNamespace: trino
  values:
    serviceAccount:
      name: trino
    imagePullSecrets:
      - name: magna-pull-secret
    securityContext:
      runAsUser: 1000
      runAsGroup: 1000
      fsGroup: 1000
    server:
      workers: 4
      config:
        authenticationType: "oauth2,jwt"
        query:
          maxMemory: "32GB"
      coordinatorExtraConfig: |
        web-ui.authentication.type=oauth2
        http-server.authentication.oauth2.issuer=${ENV:OAUTH_ISSUER}
        http-server.authentication.oauth2.client-id=${ENV:OAUTH_CLIENT_ID}
        http-server.authentication.oauth2.client-secret=${ENV:OAUTH_CLIENT_SECRET}
        http-server.authentication.jwt.key-file=/var/run/userdata/trino-jwt-cert/jwt.key
        http-server.authentication.jwt.required-issuer=trino-jwt-issuer
        http-server.authentication.jwt.user-mapping.pattern=(.+@(.*\\.)?ibm\\.com)
      exchangeManager:
        name: filesystem
        baseDir:
          - s3://prod-state-jsukzd
      autoscaling:
        enabled: true
        maxReplicas: 12
        targetCPUUtilizationPercentage: 10
        targetMemoryUtilizationPercentage: ""
        behavior:
          scaleDown:
            stabilizationWindowSeconds: 1800
            policies:
            - type: Percent
              value: 50
              periodSeconds: 300
          scaleUp:
            stabilizationWindowSeconds: 0
            policies:
            - type: Pods
              value: 3
              periodSeconds: 300
    envFrom:
      - secretRef:
          name: trino-config
    env:
      - name: JAVA_TOOL_OPTIONS
        value: "-Djavax.net.ssl.trustStore=/var/run/userdata/ca-certificates/bundle.jks -Djavax.net.ssl.trustStorePassword=changeit"
      - name: CASSANDRA_USERNAME
        valueFrom:
          secretKeyRef:
            key: "username"
            name: "cassandra-admin-creds"
      - name: CASSANDRA_PASSWORD
        valueFrom:
          secretKeyRef:
            key: "password"
            name: "cassandra-admin-creds"
      - name: S3_ACCESS_KEY
        valueFrom:
          secretKeyRef:
            key: access-key
            name: ibm-cos-sc-config
      - name: S3_SECRET_KEY
        valueFrom:
          secretKeyRef:
            key: secret-key
            name: ibm-cos-sc-config
      - name: TENANTS_DB_USERNAME
        valueFrom:
          secretKeyRef:
            key: "username"
            name: "tenants-db-config"
      - name: TENANTS_DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: "password"
            name: "tenants-db-config"
    accessControl:
      type: properties
      properties: |
        access-control.name=opa
        opa.policy.uri=http://opa:8181/v1/data/trino/allow
        opa.policy.batched-uri=http://opa:8181/v1/data/trino/batch
        opa.policy.row-filters-uri=http://opa:8181/v1/data/trino/row_filters
    coordinator:
      labels:
        magna.ciso.ibm.com/requires-s3-access: "true"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: "/metrics"
        prometheus.io/port: "5556"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: ibm-cloud.kubernetes.io/worker-pool-name
                    operator: In
                    values:
                      - trino-oct13
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                topologyKey: kubernetes.io/hostname
                labelSelector:
                  matchLabels:
                    app.kubernetes.io/name: trino
      tolerations:
        - key: for-database-workloads
          operator: Exists
          effect: NoSchedule
      config:
        memory:
          heapHeadroomPerNode: 33GB # ceil(maxHeapSize * 0.35)
        query:
          maxMemoryPerNode: 60GB # maxHeapSize - heapHeadroomPerNode - 1
      jvm:
        maxHeapSize: 94g # floor(container_limit * 0.85)
      additionalJVMConfig:
        - -javaagent:/opt/jmx/jmx_prometheus_javaagent-1.0.1.jar=0.0.0.0:5556:/var/run/userdata/jmx-exporter-config/config.yaml
        - -XX:ActiveProcessorCount=32
      resources:
        limits:
          memory: 110Gi
        requests:
          cpu: 28
          memory: 110Gi
      additionalConfigFiles:
        kafka-event-listener.properties: |
          event-listener.name=kafka
          kafka-event-listener.broker-endpoints=magna2-kafka-bootstrap.persistence.svc.cluster.local:9093
          kafka-event-listener.created-event.topic=trino-audit-logs
          kafka-event-listener.completed-event.topic=trino-audit-logs
          kafka-event-listener.excluded-fields=payload,taskStatistics
          kafka-event-listener.client-id=trino
          kafka-event-listener.config.resources=/var/run/userdata/kafka-cfg/properties
      additionalVolumes:
        - name: cluster-trust-bundle
          configMap:
            name: "cluster-trust-bundle"
        - name: jmx-exporter-config
          configMap:
            name: trino-jmx-exporter-config
        - name: trino-jwt-cert
          secret:
            secretName: trino-jwt-cert-jwks
        - name: trino-cache
          ephemeral:
            volumeClaimTemplate:
              spec:
                accessModes:
                  - ReadWriteOnce
                resources:
                  requests:
                    storage: 400Gi
                storageClassName: ibmc-vpc-block-metro-10iops-tier
                volumeMode: Filesystem
        - name: trino-kafka-user
          secret:
            secretName: trino-kafka-user-new
        - name: trino-kafka-config
          secret:
            secretName: trino-kafka-config
      additionalVolumeMounts:
        - mountPath: /var/run/userdata/ca-certificates
          name: cluster-trust-bundle
        - mountPath: /var/run/userdata/jmx-exporter-config
          name: jmx-exporter-config
        - mountPath: /var/run/userdata/trino-jwt-cert
          name: trino-jwt-cert
        - mountPath: /mnt/trino-cache
          name: trino-cache
        - mountPath: /var/run/userdata/kafka
          name: trino-kafka-user
        - mountPath: /var/run/userdata/kafka-cfg
          name: trino-kafka-config
    worker:
      labels:
        magna.ciso.ibm.com/requires-s3-access: "true"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: "/metrics"
        prometheus.io/port: "5556"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: ibm-cloud.kubernetes.io/worker-pool-name
                    operator: In
                    values:
                      - trino-oct13
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                topologyKey: kubernetes.io/hostname
                labelSelector:
                  matchLabels:
                    app.kubernetes.io/name: trino
      tolerations:
        - key: for-database-workloads
          operator: Exists
          effect: NoSchedule
      config:
        memory:
          heapHeadroomPerNode: 33GB # ceil(maxHeapSize * 0.35)
        query:
          maxMemoryPerNode: 60GB # maxHeapSize - heapHeadroomPerNode - 1
      jvm:
        maxHeapSize: 94g # floor(container_limit * 0.85)
      additionalJVMConfig:
        - -javaagent:/opt/jmx/jmx_prometheus_javaagent-1.0.1.jar=0.0.0.0:5556:/var/run/userdata/jmx-exporter-config/config.yaml
        - -XX:ActiveProcessorCount=32
      resources:
        limits:
          memory: 110Gi
        requests:
          cpu: 28
          memory: 110Gi
      additionalConfigFiles:
        kafka-event-listener.properties: |
          event-listener.name=kafka
          kafka-event-listener.broker-endpoints=magna2-kafka-bootstrap.persistence.svc.cluster.local:9093
          kafka-event-listener.created-event.topic=trino-audit-logs
          kafka-event-listener.completed-event.topic=trino-audit-logs
          kafka-event-listener.excluded-fields=payload,taskStatistics
          kafka-event-listener.client-id=trino
          kafka-event-listener.config.resources=/var/run/userdata/kafka-cfg/properties
      additionalVolumes:
        - name: cluster-trust-bundle
          configMap:
            name: cluster-trust-bundle
        - name: jmx-exporter-config
          configMap:
            name: trino-jmx-exporter-config
        - name: trino-cache
          ephemeral:
            volumeClaimTemplate:
              spec:
                accessModes:
                  - ReadWriteOnce
                resources:
                  requests:
                    storage: 200Gi
                storageClassName: ibmc-vpc-block-metro-10iops-tier
                volumeMode: Filesystem
        - name: trino-kafka-user
          secret:
            secretName: trino-kafka-user-new
        - name: trino-kafka-config
          secret:
            secretName: trino-kafka-config
      additionalVolumeMounts:
        - mountPath: /var/run/userdata/ca-certificates
          name: cluster-trust-bundle
        - mountPath: /var/run/userdata/jmx-exporter-config
          name: jmx-exporter-config
        - mountPath: /mnt/trino-cache
          name: trino-cache
        - mountPath: /var/run/userdata/kafka
          name: trino-kafka-user
        - mountPath: /var/run/userdata/kafka-cfg
          name: trino-kafka-config
      gracefulShutdown:
        enabled: true
        gracePeriodSeconds: 60
      terminationGracePeriodSeconds: 120
    catalogs:
      cassandra: |
        connector.name=cassandra
        cassandra.contact-points=magna-cass-magna-service.persistence.svc
        cassandra.tls.enabled=true
        cassandra.tls.truststore-path=/var/run/userdata/ca-certificates/bundle.jks
        cassandra.tls.truststore-password=changeit
        cassandra.load-policy.dc-aware.local-dc=magna
        cassandra.security=password
        cassandra.username=${ENV:CASSANDRA_USERNAME}
        cassandra.password=${ENV:CASSANDRA_PASSWORD}
      iceberg: |
        connector.name=iceberg
        iceberg.catalog.type=rest
        iceberg.metadata.parallelism=32
        iceberg.rest-catalog.uri=http://nessie.persistence.svc:19120/iceberg
        iceberg.register-table-procedure.enabled=false
        iceberg.query-partition-filter-required=true
        iceberg.query-partition-filter-required-schemas=ocsf,ocsf_summary
        fs.native-s3.enabled=true
        s3.endpoint=https://s3.direct.us.cloud-object-storage.appdomain.cloud
        s3.region=us-east
        s3.path-style-access=true
        fs.cache.enabled=true
        fs.cache.directories=/mnt/trino-cache/iceberg
        fs.cache.max-disk-usage-percentages=90
        fs.cache.ttl=7d
        fs.cache.preferred-hosts-count=2
        fs.cache.page-size=1MB
      tenants: |
        connector.name=postgresql
        connection-url=********************************************************************************************************************************************
        connection-user=${ENV:TENANTS_DB_USERNAME}
        connection-password=${ENV:TENANTS_DB_PASSWORD}

    additionalConfigProperties:
      - "http-server.process-forwarded=true"
      - "internal-communication.shared-secret=${ENV:CLUSTER_SHARED_SECRET}"
      - retry-policy=TASK
      - task.concurrency=32
      - task.max-worker-threads=128
      - node-scheduler.min-pending-splits-per-task=128
      - event-listener.config-files=/etc/trino/kafka-event-listener.properties
    additionalExchangeManagerProperties:
      - exchange.sink-buffer-pool-min-size=10
      - exchange.sink-buffers-per-partition=2
      - exchange.sink-max-file-size=1GB
      - exchange.source-concurrent-readers=32
      - exchange.s3.endpoint=https://s3.direct.us-east.cloud-object-storage.appdomain.cloud
      - exchange.s3.region=us-east
      - exchange.s3.path-style-access=true
      - exchange.s3.aws-access-key=${ENV:S3_ACCESS_KEY}
      - exchange.s3.aws-secret-key=${ENV:S3_SECRET_KEY}
      - exchange.s3.upload.part-size=10MB
  # The trino helm chart does not allow setting the priority class. This is worked around here.
  postRenderers:
    - kustomize:
        patches:
          - target:
              version: v1
              kind: Deployment
            patch: |
              - op: add
                path: /spec/template/spec/priorityClassName
                value: magna-trino
  install:
    createNamespace: false
    remediation:
      retries: 3
  upgrade:
    remediation:
      retries: 3
  test:
    enable: false

{"externalControllerServices": {}, "flowContents": {"comments": "", "componentType": "PROCESS_GROUP", "connections": [{"backPressureDataSizeThreshold": "1 GB", "backPressureObjectThreshold": 500, "bends": [], "componentType": "CONNECTION", "destination": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "c4ff7111-544b-3107-b8e4-be5ccf0cb82c", "instanceIdentifier": "c4ff7111-544b-3107-b8e4-be5ccf0cb82c", "name": "Funnel", "type": "FUNNEL"}, "flowFileExpiration": "0 sec", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "0a7bb40c-69fb-37fa-96b9-723e2ab1f941", "instanceIdentifier": "0a7bb40c-69fb-37fa-96b9-723e2ab1f941", "labelIndex": 1, "loadBalanceCompression": "DO_NOT_COMPRESS", "loadBalanceStrategy": "DO_NOT_LOAD_BALANCE", "name": "", "partitioningAttribute": "", "prioritizers": [], "selectedRelationships": ["success"], "source": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "9caaac90-d822-327d-b784-08236dbe78e4", "instanceIdentifier": "9caaac90-d822-327d-b784-08236dbe78e4", "name": "UpdateAttribute", "type": "PROCESSOR"}, "zIndex": 0}, {"backPressureDataSizeThreshold": "1 GB", "backPressureObjectThreshold": 500, "bends": [], "componentType": "CONNECTION", "destination": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "20b107f6-0d0a-3ff6-b306-f926a2978a9c", "instanceIdentifier": "20b107f6-0d0a-3ff6-b306-f926a2978a9c", "name": "PublishKafka_2_6", "type": "PROCESSOR"}, "flowFileExpiration": "0 sec", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "211f800c-5a67-3c95-91c0-b07a7cad1dbd", "instanceIdentifier": "211f800c-5a67-3c95-91c0-b07a7cad1dbd", "labelIndex": 1, "loadBalanceCompression": "DO_NOT_COMPRESS", "loadBalanceStrategy": "DO_NOT_LOAD_BALANCE", "name": "", "partitioningAttribute": "", "prioritizers": [], "selectedRelationships": [""], "source": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "c4ff7111-544b-3107-b8e4-be5ccf0cb82c", "instanceIdentifier": "c4ff7111-544b-3107-b8e4-be5ccf0cb82c", "name": "Funnel", "type": "FUNNEL"}, "zIndex": 0}, {"backPressureDataSizeThreshold": "1 GB", "backPressureObjectThreshold": 500, "bends": [], "componentType": "CONNECTION", "destination": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "77dcbfb9-2956-3cfd-944a-b3bd93808ff5", "instanceIdentifier": "77dcbfb9-2956-3cfd-944a-b3bd93808ff5", "name": "UpdateAttribute", "type": "PROCESSOR"}, "flowFileExpiration": "0 sec", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "32503e38-5b0a-3ffa-be42-21c5ddde4e57", "instanceIdentifier": "32503e38-5b0a-3ffa-be42-21c5ddde4e57", "labelIndex": 1, "loadBalanceCompression": "DO_NOT_COMPRESS", "loadBalanceStrategy": "DO_NOT_LOAD_BALANCE", "name": "", "partitioningAttribute": "", "prioritizers": [], "selectedRelationships": ["success"], "source": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "5e02d58e-6180-33a0-9118-3ff86642a934", "instanceIdentifier": "5e02d58e-6180-33a0-9118-3ff86642a934", "name": "CrowdStrike FDR", "type": "PROCESSOR"}, "zIndex": 0}, {"backPressureDataSizeThreshold": "1 GB", "backPressureObjectThreshold": 500, "bends": [], "componentType": "CONNECTION", "destination": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "0742a8ec-9503-3c6d-8d3b-9f29d5399b05", "instanceIdentifier": "0742a8ec-9503-3c6d-8d3b-9f29d5399b05", "name": "UpdateAttribute", "type": "PROCESSOR"}, "flowFileExpiration": "0 sec", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "44677633-5822-3330-8d12-282aec465d61", "instanceIdentifier": "44677633-5822-3330-8d12-282aec465d61", "labelIndex": 1, "loadBalanceCompression": "DO_NOT_COMPRESS", "loadBalanceStrategy": "DO_NOT_LOAD_BALANCE", "name": "", "partitioningAttribute": "", "prioritizers": [], "selectedRelationships": ["success"], "source": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "f1004c5e-ea74-32c4-9f42-bb500d9aee68", "instanceIdentifier": "f1004c5e-ea74-32c4-9f42-bb500d9aee68", "name": "M365 <PERSON><PERSON>s", "type": "PROCESSOR"}, "zIndex": 0}, {"backPressureDataSizeThreshold": "1 GB", "backPressureObjectThreshold": 500, "bends": [], "componentType": "CONNECTION", "destination": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "968be8cc-e3a6-319d-b6d6-62f135be966f", "instanceIdentifier": "968be8cc-e3a6-319d-b6d6-62f135be966f", "name": "PutTCP", "type": "PROCESSOR"}, "flowFileExpiration": "0 sec", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "5c5d04b0-d410-3c58-8c94-c7a91362c6d3", "instanceIdentifier": "5c5d04b0-d410-3c58-8c94-c7a91362c6d3", "labelIndex": 1, "loadBalanceCompression": "DO_NOT_COMPRESS", "loadBalanceStrategy": "DO_NOT_LOAD_BALANCE", "name": "", "partitioningAttribute": "", "prioritizers": [], "selectedRelationships": ["splits"], "source": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "abc56ad4-fbbd-3dd7-a83b-14cbab94a470", "instanceIdentifier": "abc56ad4-fbbd-3dd7-a83b-14cbab94a470", "name": "SplitContent", "type": "PROCESSOR"}, "zIndex": 0}, {"backPressureDataSizeThreshold": "1 GB", "backPressureObjectThreshold": 500, "bends": [], "componentType": "CONNECTION", "destination": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "9caaac90-d822-327d-b784-08236dbe78e4", "instanceIdentifier": "9caaac90-d822-327d-b784-08236dbe78e4", "name": "UpdateAttribute", "type": "PROCESSOR"}, "flowFileExpiration": "0 sec", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "63d1305d-c37a-314b-8670-47cd83602cd3", "instanceIdentifier": "63d1305d-c37a-314b-8670-47cd83602cd3", "labelIndex": 1, "loadBalanceCompression": "DO_NOT_COMPRESS", "loadBalanceStrategy": "DO_NOT_LOAD_BALANCE", "name": "", "partitioningAttribute": "", "prioritizers": [], "selectedRelationships": ["success"], "source": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "ce9e8e7b-9337-348b-afc0-5a4eba171e6a", "instanceIdentifier": "ce9e8e7b-9337-348b-afc0-5a4eba171e6a", "name": "CrowdStrike Spotlight", "type": "PROCESSOR"}, "zIndex": 0}, {"backPressureDataSizeThreshold": "1 GB", "backPressureObjectThreshold": 500, "bends": [], "componentType": "CONNECTION", "destination": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "acb8fa79-c7dc-3ea2-b966-55b6004fcad5", "instanceIdentifier": "acb8fa79-c7dc-3ea2-b966-55b6004fcad5", "name": "UpdateAttribute", "type": "PROCESSOR"}, "flowFileExpiration": "0 sec", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "6a354fcc-0bdc-33b2-a8f8-c8fd6e5c5847", "instanceIdentifier": "6a354fcc-0bdc-33b2-a8f8-c8fd6e5c5847", "labelIndex": 1, "loadBalanceCompression": "DO_NOT_COMPRESS", "loadBalanceStrategy": "DO_NOT_LOAD_BALANCE", "name": "", "partitioningAttribute": "", "prioritizers": [], "selectedRelationships": ["success"], "source": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "b8bc9f34-904a-3ef4-9b36-5a71af6555de", "instanceIdentifier": "b8bc9f34-904a-3ef4-9b36-5a71af6555de", "name": "ISV", "type": "PROCESSOR"}, "zIndex": 0}, {"backPressureDataSizeThreshold": "1 GB", "backPressureObjectThreshold": 500, "bends": [], "componentType": "CONNECTION", "destination": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "c4ff7111-544b-3107-b8e4-be5ccf0cb82c", "instanceIdentifier": "c4ff7111-544b-3107-b8e4-be5ccf0cb82c", "name": "Funnel", "type": "FUNNEL"}, "flowFileExpiration": "0 sec", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "85401039-0dfc-3950-b58c-9903864f9594", "instanceIdentifier": "85401039-0dfc-3950-b58c-9903864f9594", "labelIndex": 1, "loadBalanceCompression": "DO_NOT_COMPRESS", "loadBalanceStrategy": "DO_NOT_LOAD_BALANCE", "name": "", "partitioningAttribute": "", "prioritizers": [], "selectedRelationships": ["success"], "source": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "77dcbfb9-2956-3cfd-944a-b3bd93808ff5", "instanceIdentifier": "77dcbfb9-2956-3cfd-944a-b3bd93808ff5", "name": "UpdateAttribute", "type": "PROCESSOR"}, "zIndex": 0}, {"backPressureDataSizeThreshold": "1 GB", "backPressureObjectThreshold": 500, "bends": [], "componentType": "CONNECTION", "destination": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "24dcdeea-7fc8-318c-ad08-37e70a456dd3", "instanceIdentifier": "24dcdeea-7fc8-318c-ad08-37e70a456dd3", "name": "SplitContent", "type": "PROCESSOR"}, "flowFileExpiration": "0 sec", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "b6859aa0-a3dd-34c5-bba8-9e38b1977c80", "instanceIdentifier": "b6859aa0-a3dd-34c5-bba8-9e38b1977c80", "labelIndex": 1, "loadBalanceCompression": "DO_NOT_COMPRESS", "loadBalanceStrategy": "DO_NOT_LOAD_BALANCE", "name": "", "partitioningAttribute": "", "prioritizers": [], "selectedRelationships": ["success"], "source": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "22dc646c-9d23-37ed-b2d5-45d790be6d15", "instanceIdentifier": "22dc646c-9d23-37ed-b2d5-45d790be6d15", "name": "GenerateFlowFile", "type": "PROCESSOR"}, "zIndex": 0}, {"backPressureDataSizeThreshold": "1 GB", "backPressureObjectThreshold": 500, "bends": [], "componentType": "CONNECTION", "destination": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "c4ff7111-544b-3107-b8e4-be5ccf0cb82c", "instanceIdentifier": "c4ff7111-544b-3107-b8e4-be5ccf0cb82c", "name": "Funnel", "type": "FUNNEL"}, "flowFileExpiration": "0 sec", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "c3cc3173-1476-3022-83b4-b76fb917e430", "instanceIdentifier": "c3cc3173-1476-3022-83b4-b76fb917e430", "labelIndex": 1, "loadBalanceCompression": "DO_NOT_COMPRESS", "loadBalanceStrategy": "DO_NOT_LOAD_BALANCE", "name": "", "partitioningAttribute": "", "prioritizers": [], "selectedRelationships": ["success"], "source": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "acb8fa79-c7dc-3ea2-b966-55b6004fcad5", "instanceIdentifier": "acb8fa79-c7dc-3ea2-b966-55b6004fcad5", "name": "UpdateAttribute", "type": "PROCESSOR"}, "zIndex": 0}, {"backPressureDataSizeThreshold": "1 GB", "backPressureObjectThreshold": 500, "bends": [], "componentType": "CONNECTION", "destination": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "abc56ad4-fbbd-3dd7-a83b-14cbab94a470", "instanceIdentifier": "abc56ad4-fbbd-3dd7-a83b-14cbab94a470", "name": "SplitContent", "type": "PROCESSOR"}, "flowFileExpiration": "0 sec", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "c9561b1e-4205-35c0-be5c-5acaadc42ecc", "instanceIdentifier": "c9561b1e-4205-35c0-be5c-5acaadc42ecc", "labelIndex": 1, "loadBalanceCompression": "DO_NOT_COMPRESS", "loadBalanceStrategy": "DO_NOT_LOAD_BALANCE", "name": "", "partitioningAttribute": "", "prioritizers": [], "selectedRelationships": ["success"], "source": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "3f4cd7bf-972f-3f8f-bb6b-c7cf6698b36c", "instanceIdentifier": "3f4cd7bf-972f-3f8f-bb6b-c7cf6698b36c", "name": "GenerateFlowFileEventProcessorPayload", "type": "PROCESSOR"}, "zIndex": 0}, {"backPressureDataSizeThreshold": "1 GB", "backPressureObjectThreshold": 500, "bends": [], "componentType": "CONNECTION", "destination": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "c4ff7111-544b-3107-b8e4-be5ccf0cb82c", "instanceIdentifier": "c4ff7111-544b-3107-b8e4-be5ccf0cb82c", "name": "Funnel", "type": "FUNNEL"}, "flowFileExpiration": "0 sec", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "cf02285b-347c-3fa3-8198-6bc521db939b", "instanceIdentifier": "cf02285b-347c-3fa3-8198-6bc521db939b", "labelIndex": 1, "loadBalanceCompression": "DO_NOT_COMPRESS", "loadBalanceStrategy": "DO_NOT_LOAD_BALANCE", "name": "", "partitioningAttribute": "", "prioritizers": [], "selectedRelationships": ["success"], "source": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "0742a8ec-9503-3c6d-8d3b-9f29d5399b05", "instanceIdentifier": "0742a8ec-9503-3c6d-8d3b-9f29d5399b05", "name": "UpdateAttribute", "type": "PROCESSOR"}, "zIndex": 0}, {"backPressureDataSizeThreshold": "1 GB", "backPressureObjectThreshold": 500, "bends": [], "componentType": "CONNECTION", "destination": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "8a743079-855b-326d-b069-cb5b121e8d02", "instanceIdentifier": "8a743079-855b-326d-b069-cb5b121e8d02", "name": "PutUDP", "type": "PROCESSOR"}, "flowFileExpiration": "0 sec", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "d56f4f43-8366-3b6e-9a5d-b70712975d6c", "instanceIdentifier": "d56f4f43-8366-3b6e-9a5d-b70712975d6c", "labelIndex": 1, "loadBalanceCompression": "DO_NOT_COMPRESS", "loadBalanceStrategy": "DO_NOT_LOAD_BALANCE", "name": "", "partitioningAttribute": "", "prioritizers": [], "selectedRelationships": ["splits"], "source": {"comments": "", "groupId": "11111111-2222-3333-4444-555555555555", "id": "24dcdeea-7fc8-318c-ad08-37e70a456dd3", "instanceIdentifier": "24dcdeea-7fc8-318c-ad08-37e70a456dd3", "name": "SplitContent", "type": "PROCESSOR"}, "zIndex": 0}], "controllerServices": [{"bulletinLevel": "WARN", "bundle": {"artifact": "magna-nifi-extensions-ssl-context-service-nar", "group": "com.ibm.ciso.manga", "version": "1.0.5"}, "comments": "", "componentType": "CONTROLLER_SERVICE", "controllerServiceApis": [{"bundle": {"artifact": "nifi-standard-services-api-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "type": "org.apache.nifi.ssl.SSLContextService"}], "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "55b1baf9-f131-31bf-9c6b-4b21e397164c", "instanceIdentifier": "d00d661c-44b3-323e-50b8-bda481a6495f", "name": "MagnaSSLContextServiceKafka", "properties": {"Keystore Filename": "/var/run/userdata/kafka-client-certificate/user.p12", "Keystore Password File": "/var/run/userdata/kafka-client-certificate/user.password", "Keystore Type": "PKCS12", "SSL Protocol": "TLSv1.2", "Truststore Filename": "/var/run/userdata/cluster-certificate/bundle.jks", "Truststore Password File": null, "Truststore Type": "JKS"}, "propertyDescriptors": {"Keystore Filename": {"displayName": "Keystore Filename", "dynamic": false, "identifiesControllerService": false, "name": "Keystore Filename", "resourceDefinition": {"cardinality": "SINGLE", "resourceTypes": ["FILE"]}, "sensitive": false}, "Keystore Password": {"displayName": "Keystore Password", "dynamic": false, "identifiesControllerService": false, "name": "Keystore Password", "sensitive": true}, "Keystore Password File": {"displayName": "Keystore Password File", "dynamic": false, "identifiesControllerService": false, "name": "Keystore Password File", "sensitive": false}, "Keystore Type": {"displayName": "Keystore Type", "dynamic": false, "identifiesControllerService": false, "name": "Keystore Type", "sensitive": false}, "SSL Protocol": {"displayName": "TLS Protocol", "dynamic": false, "identifiesControllerService": false, "name": "SSL Protocol", "sensitive": false}, "Truststore Filename": {"displayName": "Truststore Filename", "dynamic": false, "identifiesControllerService": false, "name": "Truststore Filename", "resourceDefinition": {"cardinality": "SINGLE", "resourceTypes": ["FILE"]}, "sensitive": false}, "Truststore Password": {"displayName": "Truststore Password", "dynamic": false, "identifiesControllerService": false, "name": "Truststore Password", "sensitive": true}, "Truststore Password File": {"displayName": "Truststore Password File", "dynamic": false, "identifiesControllerService": false, "name": "Truststore Password File", "sensitive": false}, "Truststore Type": {"displayName": "Truststore Type", "dynamic": false, "identifiesControllerService": false, "name": "Truststore Type", "sensitive": false}}, "scheduledState": "DISABLED", "type": "com.ibm.ciso.magna.ibm.ssl.MagnaSSLContextService"}, {"bulletinLevel": "WARN", "bundle": {"artifact": "magna-nifi-extensions-ssl-context-service-nar", "group": "com.ibm.ciso.manga", "version": "1.0.5"}, "comments": "", "componentType": "CONTROLLER_SERVICE", "controllerServiceApis": [{"bundle": {"artifact": "nifi-standard-services-api-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "type": "org.apache.nifi.ssl.SSLContextService"}], "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "8a9ad5b3-2c37-3fbb-b8f4-b649dcbf8eae", "instanceIdentifier": "44e43751-d424-1829-d4a1-9bb699172497", "name": "MagnaSSLContextServiceTrustore", "properties": {"Keystore Filename": null, "Keystore Password File": null, "Keystore Type": null, "SSL Protocol": "TLSv1.2", "Truststore Filename": "/var/run/userdata/cluster-certificate/bundle.jks", "Truststore Password File": null, "Truststore Type": "JKS"}, "propertyDescriptors": {"Keystore Filename": {"displayName": "Keystore Filename", "dynamic": false, "identifiesControllerService": false, "name": "Keystore Filename", "resourceDefinition": {"cardinality": "SINGLE", "resourceTypes": ["FILE"]}, "sensitive": false}, "Keystore Password": {"displayName": "Keystore Password", "dynamic": false, "identifiesControllerService": false, "name": "Keystore Password", "sensitive": true}, "Keystore Password File": {"displayName": "Keystore Password File", "dynamic": false, "identifiesControllerService": false, "name": "Keystore Password File", "sensitive": false}, "Keystore Type": {"displayName": "Keystore Type", "dynamic": false, "identifiesControllerService": false, "name": "Keystore Type", "sensitive": false}, "SSL Protocol": {"displayName": "TLS Protocol", "dynamic": false, "identifiesControllerService": false, "name": "SSL Protocol", "sensitive": false}, "Truststore Filename": {"displayName": "Truststore Filename", "dynamic": false, "identifiesControllerService": false, "name": "Truststore Filename", "resourceDefinition": {"cardinality": "SINGLE", "resourceTypes": ["FILE"]}, "sensitive": false}, "Truststore Password": {"displayName": "Truststore Password", "dynamic": false, "identifiesControllerService": false, "name": "Truststore Password", "sensitive": true}, "Truststore Password File": {"displayName": "Truststore Password File", "dynamic": false, "identifiesControllerService": false, "name": "Truststore Password File", "sensitive": false}, "Truststore Type": {"displayName": "Truststore Type", "dynamic": false, "identifiesControllerService": false, "name": "Truststore Type", "sensitive": false}}, "scheduledState": "DISABLED", "type": "com.ibm.ciso.magna.ibm.ssl.MagnaSSLContextService"}], "defaultBackPressureDataSizeThreshold": "1 GB", "defaultBackPressureObjectThreshold": 500, "defaultFlowFileExpiration": "0 sec", "executionEngine": "INHERITED", "flowFileConcurrency": "UNBOUNDED", "flowFileOutboundPolicy": "STREAM_WHEN_AVAILABLE", "funnels": [{"componentType": "FUNNEL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "c4ff7111-544b-3107-b8e4-be5ccf0cb82c", "instanceIdentifier": "c4ff7111-544b-3107-b8e4-be5ccf0cb82c", "position": {"x": 2704.0, "y": 416.0}}], "identifier": "11111111-2222-3333-4444-555555555555", "inputPorts": [], "instanceIdentifier": "11111111-2222-3333-4444-555555555555", "labels": [], "logFileSuffix": "", "maxConcurrentTasks": 1, "name": "Fake_Data", "outputPorts": [], "parameterContextName": "fake-data", "position": {"x": 1271.857912719267, "y": 966.9838839543488}, "processGroups": [], "processors": [{"autoTerminatedRelationships": [], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-update-attribute-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "0742a8ec-9503-3c6d-8d3b-9f29d5399b05", "instanceIdentifier": "0742a8ec-9503-3c6d-8d3b-9f29d5399b05", "maxBackoffPeriod": "10 mins", "name": "UpdateAttribute", "penaltyDuration": "30 sec", "position": {"x": 3296.0, "y": 88.0}, "properties": {"Delete Attributes Expression": null, "Stateful Variables Initial Value": null, "Store State": "Do not store state", "canonical-value-lookup-cache-size": "100", "data_stream.dataset": "m365_defender.alert", "data_stream.namespace": "fake", "magna.source_owner_id": "********-0000-0000-0000-************", "topic": "raw-logs-unarchived-edr"}, "propertyDescriptors": {"Delete Attributes Expression": {"displayName": "Delete Attributes Expression", "dynamic": false, "identifiesControllerService": false, "name": "Delete Attributes Expression", "sensitive": false}, "Stateful Variables Initial Value": {"displayName": "Stateful Variables Initial Value", "dynamic": false, "identifiesControllerService": false, "name": "Stateful Variables Initial Value", "sensitive": false}, "Store State": {"displayName": "Store State", "dynamic": false, "identifiesControllerService": false, "name": "Store State", "sensitive": false}, "canonical-value-lookup-cache-size": {"displayName": "Cache Value Lookup C<PERSON>", "dynamic": false, "identifiesControllerService": false, "name": "canonical-value-lookup-cache-size", "sensitive": false}, "data_stream.dataset": {"displayName": "data_stream.dataset", "dynamic": true, "identifiesControllerService": false, "name": "data_stream.dataset", "sensitive": false}, "data_stream.namespace": {"displayName": "data_stream.namespace", "dynamic": true, "identifiesControllerService": false, "name": "data_stream.namespace", "sensitive": false}, "magna.source_owner_id": {"displayName": "magna.source_owner_id", "dynamic": true, "identifiesControllerService": false, "name": "magna.source_owner_id", "sensitive": false}, "topic": {"displayName": "topic", "dynamic": true, "identifiesControllerService": false, "name": "topic", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 25, "scheduledState": "ENABLED", "schedulingPeriod": "0 sec", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.attributes.UpdateAttribute", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": ["success", "failure"], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-kafka-2-6-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "20b107f6-0d0a-3ff6-b306-f926a2978a9c", "instanceIdentifier": "20b107f6-0d0a-3ff6-b306-f926a2978a9c", "maxBackoffPeriod": "10 mins", "name": "PublishKafka_2_6", "penaltyDuration": "30 sec", "position": {"x": 2552.0, "y": 568.0}, "properties": {"Failure Strategy": "Route to Failure", "ack.wait.time": "5 secs", "acks": "1", "attribute-name-regex": "data_stream.dataset|data_stream.namespace|magna.source_owner_id", "aws.profile.name": null, "bootstrap.servers": "#{magna.kafka_brokers}", "compression.type": "snappy", "kafka-key": null, "kerberos-user-service": null, "key-attribute-encoding": "utf-8", "max.block.ms": "5 sec", "max.request.size": "1 MB", "message-demarcator": "\n", "message-header-encoding": "UTF-8", "partition": null, "partitioner.class": "org.apache.kafka.clients.producer.internals.DefaultPartitioner", "sasl.kerberos.service.name": null, "sasl.mechanism": "GSSAPI", "sasl.token.auth": "false", "sasl.username": null, "security.protocol": "SSL", "ssl.context.service": "55b1baf9-f131-31bf-9c6b-4b21e397164c", "topic": "${topic}", "transactional-id-prefix": null, "use-transactions": "false"}, "propertyDescriptors": {"Failure Strategy": {"displayName": "Failure Strategy", "dynamic": false, "identifiesControllerService": false, "name": "Failure Strategy", "sensitive": false}, "ack.wait.time": {"displayName": "Acknowledgment Wait Time", "dynamic": false, "identifiesControllerService": false, "name": "ack.wait.time", "sensitive": false}, "acks": {"displayName": "Delivery Guarantee", "dynamic": false, "identifiesControllerService": false, "name": "acks", "sensitive": false}, "attribute-name-regex": {"displayName": "Attributes to Send as Headers (Regex)", "dynamic": false, "identifiesControllerService": false, "name": "attribute-name-regex", "sensitive": false}, "aws.profile.name": {"displayName": "AWS Profile Name", "dynamic": false, "identifiesControllerService": false, "name": "aws.profile.name", "sensitive": false}, "bootstrap.servers": {"displayName": "Kafka Brokers", "dynamic": false, "identifiesControllerService": false, "name": "bootstrap.servers", "sensitive": false}, "compression.type": {"displayName": "Compression Type", "dynamic": false, "identifiesControllerService": false, "name": "compression.type", "sensitive": false}, "kafka-key": {"displayName": "Kafka Key", "dynamic": false, "identifiesControllerService": false, "name": "kafka-key", "sensitive": false}, "kerberos-user-service": {"displayName": "Kerberos User Service", "dynamic": false, "identifiesControllerService": true, "name": "kerberos-user-service", "sensitive": false}, "key-attribute-encoding": {"displayName": "Key Attribute Encoding", "dynamic": false, "identifiesControllerService": false, "name": "key-attribute-encoding", "sensitive": false}, "max.block.ms": {"displayName": "<PERSON>", "dynamic": false, "identifiesControllerService": false, "name": "max.block.ms", "sensitive": false}, "max.request.size": {"displayName": "Max Request Size", "dynamic": false, "identifiesControllerService": false, "name": "max.request.size", "sensitive": false}, "message-demarcator": {"displayName": "Message Demarcator", "dynamic": false, "identifiesControllerService": false, "name": "message-demarcator", "sensitive": false}, "message-header-encoding": {"displayName": "Message Header Encoding", "dynamic": false, "identifiesControllerService": false, "name": "message-header-encoding", "sensitive": false}, "partition": {"displayName": "Partition", "dynamic": false, "identifiesControllerService": false, "name": "partition", "sensitive": false}, "partitioner.class": {"displayName": "Partitioner class", "dynamic": false, "identifiesControllerService": false, "name": "partitioner.class", "sensitive": false}, "sasl.kerberos.service.name": {"displayName": "Kerberos Service Name", "dynamic": false, "identifiesControllerService": false, "name": "sasl.kerberos.service.name", "sensitive": false}, "sasl.mechanism": {"displayName": "SASL Mechanism", "dynamic": false, "identifiesControllerService": false, "name": "sasl.mechanism", "sensitive": false}, "sasl.password": {"displayName": "Password", "dynamic": false, "identifiesControllerService": false, "name": "sasl.password", "sensitive": true}, "sasl.token.auth": {"displayName": "Token Authentication", "dynamic": false, "identifiesControllerService": false, "name": "sasl.token.auth", "sensitive": false}, "sasl.username": {"displayName": "Username", "dynamic": false, "identifiesControllerService": false, "name": "sasl.username", "sensitive": false}, "security.protocol": {"displayName": "Security Protocol", "dynamic": false, "identifiesControllerService": false, "name": "security.protocol", "sensitive": false}, "ssl.context.service": {"displayName": "SSL Context Service", "dynamic": false, "identifiesControllerService": true, "name": "ssl.context.service", "sensitive": false}, "topic": {"displayName": "Topic Name", "dynamic": false, "identifiesControllerService": false, "name": "topic", "sensitive": false}, "transactional-id-prefix": {"displayName": "Transactional Id Prefix", "dynamic": false, "identifiesControllerService": false, "name": "transactional-id-prefix", "sensitive": false}, "use-transactions": {"displayName": "Use Transactions", "dynamic": false, "identifiesControllerService": false, "name": "use-transactions", "sensitive": false}}, "retriedRelationships": ["failure"], "retryCount": 2, "runDurationMillis": 0, "scheduledState": "ENABLED", "schedulingPeriod": "0 sec", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.kafka.pubsub.PublishKafka_2_6", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": [], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-standard-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "22dc646c-9d23-37ed-b2d5-45d790be6d15", "instanceIdentifier": "22dc646c-9d23-37ed-b2d5-45d790be6d15", "maxBackoffPeriod": "10 mins", "name": "GenerateFlowFile", "penaltyDuration": "30 sec", "position": {"x": 1336.0, "y": -208.0}, "properties": {"Batch Size": "1", "Data Format": "Text", "File Size": "0B", "Unique FlowFiles": "false", "character-set": "UTF-8", "generate-ff-custom-text": "<44>Jan 08 16:20:59 sj3-ta-vfortigate02 CEF:0|Fortinet|Fortigate|v7.4.6|00007|traffic:forward deny|4|deviceExternalId=FGVM1VTM21000680 FTNTFGTeventtime=1736353253887258622 FTNTFGTlogid=********07 cat=traffic:forward FTNTFGTsubtype=forward FTNTFGTlevel=warning FTNTFGTvd=root src=167.94.146.79 spt=10605 deviceInboundInterface=port2 FTNTFGTsrcintfrole=wan dst=169.44.156.147 dpt=42286 deviceOutboundInterface=port3 FTNTFGTdstintfrole=dmz FTNTFGTsrccountry=United States FTNTFGTdstcountry=United States externalId=38728531 proto=6 act=deny FTNTFGTpolicyid=50 FTNTFGTpolicytype=policy FTNTFGTpoluuid=4611470c-784a-51eb-e382-7d718e348bd7 FTNTFGTpolicyname=Deny rule app=tcp/42286 FTNTFGTtrandisp=noop FTNTFGTappcat=unscanned FTNTFGTduration=0 out=0 in=0 FTNTFGTsentpkt=0 FTNTFGTrcvdpkt=0 msg=Denied by forward policy check (policy 50)\n<13>Jan 08 16:20:57 SJ3-TW-SSO05 AgentDevice=WindowsLog\tAgentLogFile=Security\tPluginVersion=WC.MSEVEN6.10.1.6.3\tSource=Microsoft-Windows-Security-Auditing\tComputer=sj3-tw-sso05.testsso.test.isops.ibm.com\tOriginatingComputer=10.168.165.103\tUser=\tDomain=\tEventID=4624\tEventIDCode=4624\tEventType=8\tEventCategory=12544\tRecordNumber=*********\tTimeGenerated=**********\tTimeWritten=**********\tLevel=Log Always\tKeywords=Audit Success\tTask=SE_ADT_LOGON_LOGON\tOpcode=Info\tMessage=An account was successfully logged on.  Subject:  Security ID:  \\NULL SID  Account Name:  -  Account Domain:  -  Logon ID:  0x0  Logon Information:  Logon Type:  3  Restricted Admin Mode: -  Virtual Account:  No  Elevated Token:  Yes  Impersonation Level:  Impersonation  New Logon:  Security ID:  TESTSSO\\SJ3-TW-RDFS01$  Account Name:  SJ3-TW-RDFS01$  Account Domain:  TESTSSO  Logon ID:  0xDC39EDBC  Linked Logon ID:  0x0  Network Account Name: -  Network Account Domain: -  Logon GUID:  {********-0000-0000-0000-************}  Process Information:  Process ID:  0x0  Process Name:  -  Network Information:  Workstation Name: SJ3-TW-RDFS01  Source Network Address: **************  Source Port:  60035  Detailed Authentication Information:  Logon Process:  NtLmSsp   Authentication Package: NTLM  Transited Services: -  Package Name (NTLM only): NTLM V2  Key Length:  128  This event is generated when a logon session is created. It is generated on the computer that was accessed.  The subject fields indicate the account on the local system which requested the logon. This is most commonly a service such as the Server service, or a local process such as Winlogon.exe or Services.exe.  The logon type field indicates the kind of logon that occurred. The most common types are 2 (interactive) and 3 (network).  The New Logon fields indicate the account for whom the new logon was created, i.e. the account that was logged on.  The network fields indicate where a remote logon request originated. Workstation name is not always available and may be left blank in some cases.  The impersonation level field indicates the extent to which a process in the logon session can impersonate.  The authentication information fields provide detailed information about this specific logon request.  - Logon GUID is a unique identifier that can be used to correlate this event with a KDC event.  - Transited services indicate which inte\n<45>Jan 08 16:20:57 sj3-ta-vfortigate02 CEF:0|Fortinet|Fortigate|v7.4.6|00015|traffic:forward start|3|deviceExternalId=FGVM1VTM21000680 FTNTFGTeventtime=1736353256503239631 FTNTFGTlogid=********** cat=traffic:forward FTNTFGTsubtype=forward FTNTFGTlevel=notice FTNTFGTvd=root src=8.222.10.112 FTNTFGTidentifier=0 deviceInboundInterface=port2 FTNTFGTsrcintfrole=wan dst=169.45.105.39 deviceOutboundInterface=port3 FTNTFGTdstintfrole=dmz FTNTFGTsrccountry=Thailand FTNTFGTdstcountry=United States externalId=38728571 proto=1 act=start FTNTFGTpolicyid=2 FTNTFGTpolicytype=policy FTNTFGTpoluuid=10e0717a-6ac1-51eb-a75d-3c7572e3a846 FTNTFGTpolicyname=Allow inbound ping app=PING FTNTFGTtrandisp=noop FTNTFGTappcat=unscanned FTNTFGTduration=0 out=0 in=0 FTNTFGTsentpkt=0 FTNTFGTrcvdpkt=0\n<181>1 2025-01-08T16:21:11.354992+00:00 sj3-pa-vpn02 vyatta-dataplane.service dataplane[3792]: - - FIREWALL: SESSION_CREATE duration=3.296 ifname=vti1 session-id=80203958 proto=tcp(6) dir=in addr=*************->************** port=21068->8835 fw-rule=Customer-to-SosTransit:40\n<134>1 2025-01-08T11:21:00.013-05:00 sj3-tl-sosdlc2 DLC 1213 - - [NOT:0000006000][**************/- -] [-/- -]LEEF:1.0|IBM|DLC|1.8.4|DLCMetrics|src=**************        InstanceID=6786efee-e76e-41d4-a0cc-0e334a488b6a ComponentType=sources   ComponentName=Source Monitor    MetricID=EventRate  Value=12.4", "mime-type": null}, "propertyDescriptors": {"Batch Size": {"displayName": "<PERSON><PERSON> Si<PERSON>", "dynamic": false, "identifiesControllerService": false, "name": "<PERSON><PERSON> Si<PERSON>", "sensitive": false}, "Data Format": {"displayName": "Data Format", "dynamic": false, "identifiesControllerService": false, "name": "Data Format", "sensitive": false}, "File Size": {"displayName": "File Size", "dynamic": false, "identifiesControllerService": false, "name": "File Size", "sensitive": false}, "Unique FlowFiles": {"displayName": "Unique FlowFiles", "dynamic": false, "identifiesControllerService": false, "name": "Unique FlowFiles", "sensitive": false}, "character-set": {"displayName": "Character Set", "dynamic": false, "identifiesControllerService": false, "name": "character-set", "sensitive": false}, "generate-ff-custom-text": {"displayName": "Custom Text", "dynamic": false, "identifiesControllerService": false, "name": "generate-ff-custom-text", "sensitive": false}, "mime-type": {"displayName": "Mime Type", "dynamic": false, "identifiesControllerService": false, "name": "mime-type", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 0, "scheduledState": "ENABLED", "schedulingPeriod": "1 min", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.standard.GenerateFlowFile", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": ["original"], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-standard-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "24dcdeea-7fc8-318c-ad08-37e70a456dd3", "instanceIdentifier": "24dcdeea-7fc8-318c-ad08-37e70a456dd3", "maxBackoffPeriod": "10 mins", "name": "SplitContent", "penaltyDuration": "30 sec", "position": {"x": 1336.0, "y": 56.0}, "properties": {"Byte Sequence": "\n", "Byte Sequence Format": "Text", "Byte Sequence Location": "Trailing", "Keep Byte Sequence": "false"}, "propertyDescriptors": {"Byte Sequence": {"displayName": "Byte Sequence", "dynamic": false, "identifiesControllerService": false, "name": "Byte Sequence", "sensitive": false}, "Byte Sequence Format": {"displayName": "Byte Sequence Format", "dynamic": false, "identifiesControllerService": false, "name": "Byte Sequence Format", "sensitive": false}, "Byte Sequence Location": {"displayName": "Byte Sequence Location", "dynamic": false, "identifiesControllerService": false, "name": "Byte Sequence Location", "sensitive": false}, "Keep Byte Sequence": {"displayName": "Keep Byte Sequence", "dynamic": false, "identifiesControllerService": false, "name": "Keep Byte Sequence", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 0, "scheduledState": "ENABLED", "schedulingPeriod": "0 sec", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.standard.SplitContent", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": [], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-standard-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "3f4cd7bf-972f-3f8f-bb6b-c7cf6698b36c", "instanceIdentifier": "3f4cd7bf-972f-3f8f-bb6b-c7cf6698b36c", "maxBackoffPeriod": "10 mins", "name": "GenerateFlowFileEventProcessorPayload", "penaltyDuration": "30 sec", "position": {"x": 800.0, "y": -208.0}, "properties": {"Batch Size": "1", "Data Format": "Text", "File Size": "0B", "Unique FlowFiles": "false", "character-set": "UTF-8", "generate-ff-custom-text": "{\"domainID\":\"0\",\"host\":\"************\",\"isoTimeFormat\":\"yyyy-MM-dd'T'HH:mm:ss.SSSZ\",\"logSource\":\"LinuxServer @ support-vsi-host1\",\"logSourceType\":\"Linux OS\",\"name\":\"Vector\",\"payload\":\"<86>Nov  6 14:55:09 support-vsi-host1 sshd[3612132]: Invalid user ubuntu from *************** port 48906\\n\",\"port\":46264,\"source_type\":\"socket\",\"src\": \"***************\",\"srcPort\": \"0\",\"timestamp\":\"2025-11-06T14:57:01.935252775Z\",\"type\":\"Event\",\"version\":\"1.0\"}\n{\"domainID\":\"2\",\"domainName\": \"Foobar\",\"host\":\"************\",\"isoTimeFormat\":\"yyyy-MM-dd'T'HH:mm:ss.SSSZ\",\"logSource\":\"LinuxServer @ support-vsi-host2\",\"logSourceType\":\"Linux OS\",\"name\":\"Vector\",\"payload\":\"<86>Nov  6 14:55:09 support-vsi-host2 sshd[3612132]: Invalid user ubuntu from *************** port 48906\\n\",\"port\":46264,\"source_type\":\"socket\",\"src\": \"***************\",\"srcPort\": \"0\",\"timestamp\":\"2025-11-06T14:57:01.935252775Z\",\"type\":\"Event\",\"version\":\"1.0\"}", "mime-type": null}, "propertyDescriptors": {"Batch Size": {"displayName": "<PERSON><PERSON> Si<PERSON>", "dynamic": false, "identifiesControllerService": false, "name": "<PERSON><PERSON> Si<PERSON>", "sensitive": false}, "Data Format": {"displayName": "Data Format", "dynamic": false, "identifiesControllerService": false, "name": "Data Format", "sensitive": false}, "File Size": {"displayName": "File Size", "dynamic": false, "identifiesControllerService": false, "name": "File Size", "sensitive": false}, "Unique FlowFiles": {"displayName": "Unique FlowFiles", "dynamic": false, "identifiesControllerService": false, "name": "Unique FlowFiles", "sensitive": false}, "character-set": {"displayName": "Character Set", "dynamic": false, "identifiesControllerService": false, "name": "character-set", "sensitive": false}, "generate-ff-custom-text": {"displayName": "Custom Text", "dynamic": false, "identifiesControllerService": false, "name": "generate-ff-custom-text", "sensitive": false}, "mime-type": {"displayName": "Mime Type", "dynamic": false, "identifiesControllerService": false, "name": "mime-type", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 0, "scheduledState": "ENABLED", "schedulingPeriod": "1 min", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.standard.GenerateFlowFile", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": [], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-standard-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "5e02d58e-6180-33a0-9118-3ff86642a934", "instanceIdentifier": "5e02d58e-6180-33a0-9118-3ff86642a934", "maxBackoffPeriod": "10 mins", "name": "CrowdStrike FDR", "penaltyDuration": "30 sec", "position": {"x": 2256.0, "y": -216.0}, "properties": {"Batch Size": "1", "Data Format": "Text", "File Size": "0B", "Unique FlowFiles": "false", "character-set": "UTF-8", "generate-ff-custom-text": "{\"event_simpleName\":\"AsepFileChange\",\"ContextTimeStamp\":\"1625677482.148\",\"ConfigStateHash\":\"1620585913\",\"ContextProcessId\":\"364936256754041721\",\"ContextThreadId\":\"0\",\"aip\":\"************\",\"ConfigBuild\":\"1007.4.0013701.1\",\"event_platform\":\"Mac\",\"Entitlements\":\"15\",\"name\":\"AsepFileChangeMacV1\",\"id\":\"ffffffff-1111-11eb-9e50-064be6e56df7\",\"EffectiveTransmissionClass\":\"2\",\"aid\":\"fffffffff1a64286a233d09974b1b377\",\"timestamp\":\"${now():toNumber()}\",\"cid\":\"ffffffff15754bcfb5f9152ec7ac90ac\",\"TargetFileName\":\"/System/Library/AssetsV2/com_apple_MobileAsset_MacSoftwareUpdate/5968e4faeba359dd5270ac282340cc4bd94d348c.asset/AssetData/payloadv2/ecc_data/System/Library/Spotlight/SystemPrefs.mdimporter/Contents/MacOS/SystemPrefs\",\"VnodeModificationType\":\"6\"}", "mime-type": null}, "propertyDescriptors": {"Batch Size": {"displayName": "<PERSON><PERSON> Si<PERSON>", "dynamic": false, "identifiesControllerService": false, "name": "<PERSON><PERSON> Si<PERSON>", "sensitive": false}, "Data Format": {"displayName": "Data Format", "dynamic": false, "identifiesControllerService": false, "name": "Data Format", "sensitive": false}, "File Size": {"displayName": "File Size", "dynamic": false, "identifiesControllerService": false, "name": "File Size", "sensitive": false}, "Unique FlowFiles": {"displayName": "Unique FlowFiles", "dynamic": false, "identifiesControllerService": false, "name": "Unique FlowFiles", "sensitive": false}, "character-set": {"displayName": "Character Set", "dynamic": false, "identifiesControllerService": false, "name": "character-set", "sensitive": false}, "generate-ff-custom-text": {"displayName": "Custom Text", "dynamic": false, "identifiesControllerService": false, "name": "generate-ff-custom-text", "sensitive": false}, "mime-type": {"displayName": "Mime Type", "dynamic": false, "identifiesControllerService": false, "name": "mime-type", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 0, "scheduledState": "ENABLED", "schedulingPeriod": "15 sec", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.standard.GenerateFlowFile", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": [], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-update-attribute-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "77dcbfb9-2956-3cfd-944a-b3bd93808ff5", "instanceIdentifier": "77dcbfb9-2956-3cfd-944a-b3bd93808ff5", "maxBackoffPeriod": "10 mins", "name": "UpdateAttribute", "penaltyDuration": "30 sec", "position": {"x": 2256.0, "y": 80.0}, "properties": {"Delete Attributes Expression": null, "Stateful Variables Initial Value": null, "Store State": "Do not store state", "canonical-value-lookup-cache-size": "100", "data_stream.dataset": "crowdstrike.fdr_data", "data_stream.namespace": "fake", "magna.source_owner_id": "********-0000-0000-0000-************", "topic": "raw-logs-unarchived-low-priority"}, "propertyDescriptors": {"Delete Attributes Expression": {"displayName": "Delete Attributes Expression", "dynamic": false, "identifiesControllerService": false, "name": "Delete Attributes Expression", "sensitive": false}, "Stateful Variables Initial Value": {"displayName": "Stateful Variables Initial Value", "dynamic": false, "identifiesControllerService": false, "name": "Stateful Variables Initial Value", "sensitive": false}, "Store State": {"displayName": "Store State", "dynamic": false, "identifiesControllerService": false, "name": "Store State", "sensitive": false}, "canonical-value-lookup-cache-size": {"displayName": "Cache Value Lookup C<PERSON>", "dynamic": false, "identifiesControllerService": false, "name": "canonical-value-lookup-cache-size", "sensitive": false}, "data_stream.dataset": {"displayName": "data_stream.dataset", "dynamic": true, "identifiesControllerService": false, "name": "data_stream.dataset", "sensitive": false}, "data_stream.namespace": {"displayName": "data_stream.namespace", "dynamic": true, "identifiesControllerService": false, "name": "data_stream.namespace", "sensitive": false}, "magna.source_owner_id": {"displayName": "magna.source_owner_id", "dynamic": true, "identifiesControllerService": false, "name": "magna.source_owner_id", "sensitive": false}, "topic": {"displayName": "topic", "dynamic": true, "identifiesControllerService": false, "name": "topic", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 25, "scheduledState": "ENABLED", "schedulingPeriod": "0 sec", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.attributes.UpdateAttribute", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": ["success", "failure"], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-standard-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "8a743079-855b-326d-b069-cb5b121e8d02", "instanceIdentifier": "8a743079-855b-326d-b069-cb5b121e8d02", "maxBackoffPeriod": "10 mins", "name": "PutUDP", "penaltyDuration": "30 sec", "position": {"x": 1336.0, "y": 312.0}, "properties": {"Hostname": "vector-syslog-sos.collection-jobs.svc", "Idle Connection Expiration": "15 seconds", "Max Size of Socket Send Buffer": "1 MB", "Port": "1514", "Timeout": "10 seconds"}, "propertyDescriptors": {"Hostname": {"displayName": "Hostname", "dynamic": false, "identifiesControllerService": false, "name": "Hostname", "sensitive": false}, "Idle Connection Expiration": {"displayName": "Idle Connection Expiration", "dynamic": false, "identifiesControllerService": false, "name": "Idle Connection Expiration", "sensitive": false}, "Max Size of Socket Send Buffer": {"displayName": "<PERSON> of Socket Send Buffer", "dynamic": false, "identifiesControllerService": false, "name": "<PERSON> of Socket Send Buffer", "sensitive": false}, "Port": {"displayName": "Port", "dynamic": false, "identifiesControllerService": false, "name": "Port", "sensitive": false}, "Timeout": {"displayName": "Timeout", "dynamic": false, "identifiesControllerService": false, "name": "Timeout", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 0, "scheduledState": "ENABLED", "schedulingPeriod": "0 sec", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.standard.PutUDP", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": ["success", "failure"], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-standard-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "968be8cc-e3a6-319d-b6d6-62f135be966f", "instanceIdentifier": "968be8cc-e3a6-319d-b6d6-62f135be966f", "maxBackoffPeriod": "10 mins", "name": "PutTCP", "penaltyDuration": "30 sec", "position": {"x": 800.0, "y": 328.0}, "properties": {"Character Set": "UTF-8", "Connection Per FlowFile": "false", "Hostname": "vector-syslog-sos.collection-jobs.svc", "Idle Connection Expiration": "15 seconds", "Max Size of Socket Send Buffer": "1 MB", "Outgoing Message Delimiter": null, "Port": "514", "Record Reader": null, "Record Writer": null, "SSL Context Service": "8a9ad5b3-2c37-3fbb-b8f4-b649dcbf8eae", "Timeout": "10 seconds", "Transmission Strategy": "FLOWFILE_ORIENTED"}, "propertyDescriptors": {"Character Set": {"displayName": "Character Set", "dynamic": false, "identifiesControllerService": false, "name": "Character Set", "sensitive": false}, "Connection Per FlowFile": {"displayName": "Connection Per FlowFile", "dynamic": false, "identifiesControllerService": false, "name": "Connection Per FlowFile", "sensitive": false}, "Hostname": {"displayName": "Hostname", "dynamic": false, "identifiesControllerService": false, "name": "Hostname", "sensitive": false}, "Idle Connection Expiration": {"displayName": "Idle Connection Expiration", "dynamic": false, "identifiesControllerService": false, "name": "Idle Connection Expiration", "sensitive": false}, "Max Size of Socket Send Buffer": {"displayName": "<PERSON> of Socket Send Buffer", "dynamic": false, "identifiesControllerService": false, "name": "<PERSON> of Socket Send Buffer", "sensitive": false}, "Outgoing Message Delimiter": {"displayName": "Outgoing Message Delimiter", "dynamic": false, "identifiesControllerService": false, "name": "Outgoing Message Delimiter", "sensitive": false}, "Port": {"displayName": "Port", "dynamic": false, "identifiesControllerService": false, "name": "Port", "sensitive": false}, "Record Reader": {"displayName": "Record Reader", "dynamic": false, "identifiesControllerService": true, "name": "Record Reader", "sensitive": false}, "Record Writer": {"displayName": "Record Writer", "dynamic": false, "identifiesControllerService": true, "name": "Record Writer", "sensitive": false}, "SSL Context Service": {"displayName": "SSL Context Service", "dynamic": false, "identifiesControllerService": true, "name": "SSL Context Service", "sensitive": false}, "Timeout": {"displayName": "Timeout", "dynamic": false, "identifiesControllerService": false, "name": "Timeout", "sensitive": false}, "Transmission Strategy": {"displayName": "Transmission Strategy", "dynamic": false, "identifiesControllerService": false, "name": "Transmission Strategy", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 0, "scheduledState": "ENABLED", "schedulingPeriod": "0 sec", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.standard.PutTCP", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": [], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-update-attribute-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "9caaac90-d822-327d-b784-08236dbe78e4", "instanceIdentifier": "9caaac90-d822-327d-b784-08236dbe78e4", "maxBackoffPeriod": "10 mins", "name": "UpdateAttribute", "penaltyDuration": "30 sec", "position": {"x": 1824.0, "y": 80.0}, "properties": {"Delete Attributes Expression": null, "Stateful Variables Initial Value": null, "Store State": "Do not store state", "canonical-value-lookup-cache-size": "100", "data_stream.dataset": "crowdstrike.spotlight", "data_stream.namespace": "fake", "magna.source_owner_id": "********-0000-0000-0000-************", "topic": "raw-logs-unarchived-crowdstrike-spotlight"}, "propertyDescriptors": {"Delete Attributes Expression": {"displayName": "Delete Attributes Expression", "dynamic": false, "identifiesControllerService": false, "name": "Delete Attributes Expression", "sensitive": false}, "Stateful Variables Initial Value": {"displayName": "Stateful Variables Initial Value", "dynamic": false, "identifiesControllerService": false, "name": "Stateful Variables Initial Value", "sensitive": false}, "Store State": {"displayName": "Store State", "dynamic": false, "identifiesControllerService": false, "name": "Store State", "sensitive": false}, "canonical-value-lookup-cache-size": {"displayName": "Cache Value Lookup C<PERSON>", "dynamic": false, "identifiesControllerService": false, "name": "canonical-value-lookup-cache-size", "sensitive": false}, "data_stream.dataset": {"displayName": "data_stream.dataset", "dynamic": true, "identifiesControllerService": false, "name": "data_stream.dataset", "sensitive": false}, "data_stream.namespace": {"displayName": "data_stream.namespace", "dynamic": true, "identifiesControllerService": false, "name": "data_stream.namespace", "sensitive": false}, "magna.source_owner_id": {"displayName": "magna.source_owner_id", "dynamic": true, "identifiesControllerService": false, "name": "magna.source_owner_id", "sensitive": false}, "topic": {"displayName": "topic", "dynamic": true, "identifiesControllerService": false, "name": "topic", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 25, "scheduledState": "ENABLED", "schedulingPeriod": "0 sec", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.attributes.UpdateAttribute", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": ["original"], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-standard-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "abc56ad4-fbbd-3dd7-a83b-14cbab94a470", "instanceIdentifier": "abc56ad4-fbbd-3dd7-a83b-14cbab94a470", "maxBackoffPeriod": "10 mins", "name": "SplitContent", "penaltyDuration": "30 sec", "position": {"x": 800.0, "y": 56.0}, "properties": {"Byte Sequence": "\n", "Byte Sequence Format": "Text", "Byte Sequence Location": "Trailing", "Keep Byte Sequence": "false"}, "propertyDescriptors": {"Byte Sequence": {"displayName": "Byte Sequence", "dynamic": false, "identifiesControllerService": false, "name": "Byte Sequence", "sensitive": false}, "Byte Sequence Format": {"displayName": "Byte Sequence Format", "dynamic": false, "identifiesControllerService": false, "name": "Byte Sequence Format", "sensitive": false}, "Byte Sequence Location": {"displayName": "Byte Sequence Location", "dynamic": false, "identifiesControllerService": false, "name": "Byte Sequence Location", "sensitive": false}, "Keep Byte Sequence": {"displayName": "Keep Byte Sequence", "dynamic": false, "identifiesControllerService": false, "name": "Keep Byte Sequence", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 0, "scheduledState": "ENABLED", "schedulingPeriod": "0 sec", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.standard.SplitContent", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": [], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-update-attribute-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "acb8fa79-c7dc-3ea2-b966-55b6004fcad5", "instanceIdentifier": "acb8fa79-c7dc-3ea2-b966-55b6004fcad5", "maxBackoffPeriod": "10 mins", "name": "UpdateAttribute", "penaltyDuration": "30 sec", "position": {"x": 2872.0, "y": 80.0}, "properties": {"Delete Attributes Expression": null, "Stateful Variables Initial Value": null, "Store State": "Do not store state", "canonical-value-lookup-cache-size": "100", "data_stream.dataset": "isv.general", "data_stream.namespace": "fake", "magna.source_owner_id": "********-0000-0000-0000-************", "topic": "raw-logs-unarchived-isv-general"}, "propertyDescriptors": {"Delete Attributes Expression": {"displayName": "Delete Attributes Expression", "dynamic": false, "identifiesControllerService": false, "name": "Delete Attributes Expression", "sensitive": false}, "Stateful Variables Initial Value": {"displayName": "Stateful Variables Initial Value", "dynamic": false, "identifiesControllerService": false, "name": "Stateful Variables Initial Value", "sensitive": false}, "Store State": {"displayName": "Store State", "dynamic": false, "identifiesControllerService": false, "name": "Store State", "sensitive": false}, "canonical-value-lookup-cache-size": {"displayName": "Cache Value Lookup C<PERSON>", "dynamic": false, "identifiesControllerService": false, "name": "canonical-value-lookup-cache-size", "sensitive": false}, "data_stream.dataset": {"displayName": "data_stream.dataset", "dynamic": true, "identifiesControllerService": false, "name": "data_stream.dataset", "sensitive": false}, "data_stream.namespace": {"displayName": "data_stream.namespace", "dynamic": true, "identifiesControllerService": false, "name": "data_stream.namespace", "sensitive": false}, "magna.source_owner_id": {"displayName": "magna.source_owner_id", "dynamic": true, "identifiesControllerService": false, "name": "magna.source_owner_id", "sensitive": false}, "topic": {"displayName": "topic", "dynamic": true, "identifiesControllerService": false, "name": "topic", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 25, "scheduledState": "ENABLED", "schedulingPeriod": "0 sec", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.attributes.UpdateAttribute", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": [], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-standard-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "b8bc9f34-904a-3ef4-9b36-5a71af6555de", "instanceIdentifier": "b8bc9f34-904a-3ef4-9b36-5a71af6555de", "maxBackoffPeriod": "10 mins", "name": "ISV", "penaltyDuration": "30 sec", "position": {"x": 2872.0, "y": -216.0}, "properties": {"Batch Size": "1", "Data Format": "Text", "File Size": "0B", "Unique FlowFiles": "false", "character-set": "UTF-8", "generate-ff-custom-text": "{\"_index\":\"event-authentication-2024.5-000001\",\"_type\":\"_doc\",\"_id\":\"********-1111-aaaa-bbbb-************\",\"_score\":0,\"_source\":{\"geoip\":{\"continent_name\":\"North America\",\"city_name\":\"Austin\",\"country_iso_code\":\"USA\",\"ip\":\"*************\",\"country_name\":\"United States\",\"region_name\":\"Texas\",\"location\":{\"lon\":\"-10.0000\",\"lat\":\"50.0000\"}},\"data\":{\"unique security name\":\"UNIQUE-SECURITY-NAME-EXAMPLE\",\"performedby_type\":\"api\",\"subject\":\"TEST-SUBJECT-HERE\",\"origin\":\"*************\",\"cause\":\"Token Exchange Successful\",\"devicetype\":\"Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\",\"client_id\":\"TEST-CLIENT-ID\",\"result\":\"success\",\"performedby\":\"TEST-PERFORMED-BY\",\"subtype\":\"token-exchange\",\"grant_type\":\"resource_owner\",\"sourcetype\":\"oidc\",\"realm\":\"cloudIdentityRealm\",\"exp\":\"1111111111\",\"iat\":\"1111111111\",\"username\":\"testuser0000\"},\"year\":2024,\"@metadata\":{\"source_dc\":\"prod-us01a\"},\"event_type\":\"authentication\",\"month\":5,\"indexed_at\":1714592699837,\"@processing_time\":440,\"tenantid\":\"TEST-TENANT-ID\",\"tenantname\":\"test.gov\",\"correlationid\":\"TEST-CORR-ID\",\"servicename\":\"authbroker\",\"id\":\"********-1111-aaaa-bbbb-************\",\"time\":${now():toNumber()},\"day\":1}}", "mime-type": null}, "propertyDescriptors": {"Batch Size": {"displayName": "<PERSON><PERSON> Si<PERSON>", "dynamic": false, "identifiesControllerService": false, "name": "<PERSON><PERSON> Si<PERSON>", "sensitive": false}, "Data Format": {"displayName": "Data Format", "dynamic": false, "identifiesControllerService": false, "name": "Data Format", "sensitive": false}, "File Size": {"displayName": "File Size", "dynamic": false, "identifiesControllerService": false, "name": "File Size", "sensitive": false}, "Unique FlowFiles": {"displayName": "Unique FlowFiles", "dynamic": false, "identifiesControllerService": false, "name": "Unique FlowFiles", "sensitive": false}, "character-set": {"displayName": "Character Set", "dynamic": false, "identifiesControllerService": false, "name": "character-set", "sensitive": false}, "generate-ff-custom-text": {"displayName": "Custom Text", "dynamic": false, "identifiesControllerService": false, "name": "generate-ff-custom-text", "sensitive": false}, "mime-type": {"displayName": "Mime Type", "dynamic": false, "identifiesControllerService": false, "name": "mime-type", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 0, "scheduledState": "ENABLED", "schedulingPeriod": "15 sec", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.standard.GenerateFlowFile", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": [], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-standard-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "ce9e8e7b-9337-348b-afc0-5a4eba171e6a", "instanceIdentifier": "ce9e8e7b-9337-348b-afc0-5a4eba171e6a", "maxBackoffPeriod": "10 mins", "name": "CrowdStrike Spotlight", "penaltyDuration": "30 sec", "position": {"x": 1832.0, "y": -216.0}, "properties": {"Batch Size": "1", "Data Format": "Text", "File Size": "0B", "Unique FlowFiles": "false", "character-set": "UTF-8", "generate-ff-custom-text": "{\"id\":\"b7ad61cf744796b1ddb5c2c484f8f233_5aa23da5cfc48228781ebed23aee3503\",\"cid\":\"5aa23da5cfc48228781ebed23aee3503\",\"aid\":\"b7ad61cf744796b1ddb5c2c484f8f233\",\"created_timestamp\":\"${now():format(\"yyyy-MM-dd'T'HH:mm:ss'Z'\", \"GMT\")}\",\"updated_timestamp\":\"${now():format(\"yyyy-MM-dd'T'HH:mm:ss'Z'\", \"GMT\")}\",\"status\":\"open\",\"apps\":[{\"product_name_version\":\"kernel 3.10.0-1160.105.1.el7\",\"sub_status\":\"closed\",\"remediation\":{\"ids\":null},\"evaluation_logic\":null},{\"product_name_version\":\"kernel 3.10.0-1160.108.1.el7\",\"sub_status\":\"open\",\"remediation\":{\"ids\":[\"9a1bb4738573483hfudhfu99647832843\"]},\"evaluation_logic\":{\"id\":\"e8d1065475324830430438403fdgguf88\",\"cid\":\"5aa23da5cfc48228781ebed23aee3503\",\"aid\":\"b7ad61cf744796b1ddb5c2c484f8f233\",\"created_timestamp\":\"2024-02-27T19:07:52Z\",\"updated_timestamp\":\"2024-02-27T19:07:52Z\",\"logic\":[{\"id\":\"4327575324324595975\",\"title\":\"Check if kernel is installed\",\"type\":\"inventory\",\"negate\":false,\"existence_check\":\"at_least_one_exists\",\"comparison_check\":\"\",\"determined_by_comparison\":false,\"comparisons\":null,\"items\":[{\"comparison_result\":\"not evaluated\",\"hive\":null,\"item_type\":\"rpminfo_item\",\"key\":null,\"name\":\"kernel-headers\",\"type\":null,\"value\":null,\"windows_view\":null},{\"comparison_result\":\"not evaluated\",\"hive\":null,\"item_type\":\"rpminfo_item\",\"key\":null,\"name\":\"kernel\",\"type\":null,\"value\":null,\"windows_view\":null},{\"comparison_result\":\"not evaluated\",\"hive\":null,\"item_type\":\"rpminfo_item\",\"key\":null,\"name\":\"kernel-tools-libs\",\"type\":null,\"value\":null,\"windows_view\":null},{\"comparison_result\":\"not evaluated\",\"hive\":null,\"item_type\":\"rpminfo_item\",\"key\":null,\"name\":\"kernel-tools\",\"type\":null,\"value\":null,\"windows_view\":null},{\"comparison_result\":\"not evaluated\",\"hive\":null,\"item_type\":\"rpminfo_item\",\"key\":null,\"name\":\"python-perf\",\"type\":null,\"value\":null,\"windows_view\":null}]}]}}],\"suppression_info\":{\"is_suppressed\":false},\"host_info\":{\"hostname\":\"trzlmnprz44\",\"local_ip\":\"***************\",\"machine_domain\":\"\",\"os_version\":\"RHEL 7.9\",\"ou\":\"\",\"site_name\":\"\",\"system_manufacturer\":\"VMware, Inc.\",\"groups\":[{\"id\":\"c796bc94i32re8yffblkjdiff88fb7eed7\",\"name\":\"Lin_7.07.16206\"},{\"id\":\"f0d2c7r8hrufufbufbf9u904093244f04\",\"name\":\"DYN_IBM_PROD_UPDATE\"}],\"tags\":[\"AV/YES\",\"CCODE/IBMC_GHHS_SLE\",\"OWNER/6G6856897\",\"UPDATES/PROD\",\"UT/10J00\"],\"platform\":\"Linux\",\"os_build\":null,\"product_type_desc\":\"Server\",\"host_last_seen_timestamp\":\"2024-02-27T19:07:52Z\",\"asset_criticality\":\"Unassigned\",\"internet_exposure\":\"No\"},\"remediation\":{\"entities\":[{\"id\":\"9a1bb4738573483hfudhfu99647832843\",\"reference\":\"Red Hat kernel\",\"title\":\"No fix available for Red Hat kernel\",\"action\":\"No fix available for kernel on RHEL 7.9\",\"link\":\"\",\"vendor_url\":\"\"}]},\"cve\":{\"id\":\"CVE-2016-3695\",\"base_score\":5.5,\"severity\":\"MEDIUM\",\"exploit_status\":0,\"exprt_rating\":\"LOW\",\"remediation_level\":\"U\",\"cisa_info\":{\"is_cisa_kev\":false},\"spotlight_published_date\":\"2021-01-11T03:55:00Z\",\"description\":\"The einj_error_inject function in drivers/acpi/apei/einj.c in the Linux kernel allows local users to simulate hardware errors and consequently cause a denial of service by leveraging failure to disable APEI error injection through EINJ when securelevel is set.\\n\",\"published_date\":\"2017-12-29T15:29:00Z\",\"vendor_advisory\":null,\"references\":[\"http://www.securityfocus.com/bid/102327\",\"https://bugzilla.redhat.com/show_bug.cgi?id=1322755\",\"https://github.com/mjg59/linux/commit/d7a6be58edc01b1c66ecd8fcc91236bfbce0a420\"],\"exploitability_score\":1.8,\"impact_score\":2.5,\"vector\":\"CVSS:3.0/UI:N/S:U/C:N/I:NAV:L/AC:L/PR:L//A:H\"}}", "mime-type": null}, "propertyDescriptors": {"Batch Size": {"displayName": "<PERSON><PERSON> Si<PERSON>", "dynamic": false, "identifiesControllerService": false, "name": "<PERSON><PERSON> Si<PERSON>", "sensitive": false}, "Data Format": {"displayName": "Data Format", "dynamic": false, "identifiesControllerService": false, "name": "Data Format", "sensitive": false}, "File Size": {"displayName": "File Size", "dynamic": false, "identifiesControllerService": false, "name": "File Size", "sensitive": false}, "Unique FlowFiles": {"displayName": "Unique FlowFiles", "dynamic": false, "identifiesControllerService": false, "name": "Unique FlowFiles", "sensitive": false}, "character-set": {"displayName": "Character Set", "dynamic": false, "identifiesControllerService": false, "name": "character-set", "sensitive": false}, "generate-ff-custom-text": {"displayName": "Custom Text", "dynamic": false, "identifiesControllerService": false, "name": "generate-ff-custom-text", "sensitive": false}, "mime-type": {"displayName": "Mime Type", "dynamic": false, "identifiesControllerService": false, "name": "mime-type", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 0, "scheduledState": "ENABLED", "schedulingPeriod": "15 sec", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.standard.GenerateFlowFile", "yieldDuration": "1 sec"}, {"autoTerminatedRelationships": [], "backoffMechanism": "PENALIZE_FLOWFILE", "bulletinLevel": "WARN", "bundle": {"artifact": "nifi-standard-nar", "group": "org.apache.nifi", "version": "2.0.0-M3"}, "comments": "", "componentType": "PROCESSOR", "concurrentlySchedulableTaskCount": 1, "executionNode": "ALL", "groupIdentifier": "11111111-2222-3333-4444-555555555555", "identifier": "f1004c5e-ea74-32c4-9f42-bb500d9aee68", "instanceIdentifier": "f1004c5e-ea74-32c4-9f42-bb500d9aee68", "maxBackoffPeriod": "10 mins", "name": "M365 <PERSON><PERSON>s", "penaltyDuration": "30 sec", "position": {"x": 3304.0, "y": -208.0}, "properties": {"Batch Size": "1", "Data Format": "Text", "File Size": "0B", "Unique FlowFiles": "false", "character-set": "UTF-8", "generate-ff-custom-text": "{ \"id\": \"1db0228c-1fc8-4b37-9558-514ea339825d\", \"providerAlertId\": \"720cba5f-5480-4a8d-a622-47bba5515d73\", \"incidentId\": \"1974164\", \"status\": \"new\", \"severity\": \"high\", \"classification\": null, \"determination\": \"malware\", \"serviceSource\": \"microsoftDefenderForEndpoint\", \"detectionSource\": \"microsoftDefenderForEndpoint\", \"productName\": \"Microsoft Defender for Endpoint\", \"detectorId\": \"c194638f-c862-4078-8ce2-0c53cd3932a5 \", \"tenantId\": \"c416135a-5bd9-4816-8b0e-dca9e0375262\", \"title\": \"Information stealing malware activity\", \"description\": \"A file creation or network connection event contained a suspicious indicator related to information stealers activity\", \"recommendedActions\": \"A. Validate the alert. B. Check for other suspicious activities in the device timeline\", \"category\": \"malware\", \"assignedTo\": \"ester\", \"alertWebUrl\": \"https://security.microsoft.com/alerts/1db0228c-1fc8-4b37-9558-514ea339825d\", \"incidentWebUrl\": \"https://security.microsoft.com/incidents/1974164?tid=c416135a-5bd9-4816-8b0e-dca9e0375262\", \"actorDisplayName\": null, \"threatDisplayName\": null, \"threatFamilyName\": \"abc\", \"mitreTechniques\": [ \"xyz\" ], \"createdDateTime\": \"${now():format(\"yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'\", \"GMT\")}\", \"lastUpdateDateTime\": \"${now():format(\"yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'\", \"GMT\")}\", \"resolvedDateTime\": null, \"firstActivityDateTime\": \"${now():format(\"yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'\", \"GMT\")}\", \"lastActivityDateTime\": \"${now():format(\"yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'\", \"GMT\")}\", \"systemTags\": [], \"alertPolicyId\": null, \"additionalData\": null, \"comments\": [], \"evidence\": [ { \"@odata.type\": \"#microsoft.graph.security.deviceEvidence\", \"createdDateTime\": \"2024-09-30T04:45:15.79Z\", \"verdict\": \"unknown\", \"remediationStatus\": \"4321\", \"remediationStatusDetails\": null, \"roles\": [], \"detailedRoles\": [ \"PrimaryDevice\" ], \"tags\": [], \"firstSeenDateTime\": \"2024-05-09T14:47:50.5726656Z\", \"mdeDeviceId\": \"af00341c1cba44c98c90f8d0464cedfc\", \"azureAdDeviceId\": \"5530454a-52c9-49ad-8d4c-74458394a6b2\", \"deviceDnsName\": \"xyz-pwr532461\", \"hostName\": \"xyz-pwr532461\", \"ntDomain\": null, \"dnsDomain\": null, \"osPlatform\": \"Windows11\", \"osBuild\": 22631, \"version\": \"23H2\", \"healthStatus\": \"active\", \"riskScore\": \"high\", \"rbacGroupId\": 17, \"rbacGroupName\": \"UnassignedGroup\", \"onboardingStatus\": \"onboarded\", \"defenderAvStatus\": \"notSupported\", \"lastIpAddress\": \"***********\", \"lastExternalIpAddress\": \"***********\", \"ipInterfaces\": [ \"*************\", \"**************\", \"fe80::abcd:efgh:1234:5678\", \"127.0.0.1\", \"::1\" ], \"vmMetadata\": null, \"loggedOnUsers\": [] }, { \"@odata.type\": \"#microsoft.graph.security.userEvidence\", \"createdDateTime\": \"2024-09-30T04:45:15.79Z\", \"verdict\": \"unknown\", \"remediationStatus\": \"none\", \"remediationStatusDetails\": null, \"roles\": [], \"detailedRoles\": [], \"tags\": [], \"stream\": null, \"userAccount\": { \"accountName\": \"FannyMandy\", \"domainName\": \"AzureAD\", \"userSid\": \"S-1-***********\", \"azureAdUserId\": \"569c2f6c4b61c242\", \"userPrincipalName\": \"<EMAIL>\", \"displayName\": null } }, { \"@odata.type\": \"#microsoft.graph.security.urlEvidence\", \"createdDateTime\": \"2024-09-30T04:45:15.79Z\", \"verdict\": \"suspicious\", \"remediationStatus\": \"none\", \"remediationStatusDetails\": null, \"roles\": [], \"detailedRoles\": [], \"tags\": [], \"url\": \"xznx.net\" }, { \"@odata.type\": \"#microsoft.graph.security.ipEvidence\", \"createdDateTime\": \"2024-09-30T04:45:15.79Z\", \"verdict\": \"suspicious\", \"remediationStatus\": \"none\", \"remediationStatusDetails\": null, \"roles\": [], \"detailedRoles\": [], \"tags\": [], \"ipAddress\": \"127.0.0.1\", \"countryLetterCode\": null, \"stream\": null, \"location\": null }, { \"@odata.type\": \"#microsoft.graph.security.processEvidence\", \"createdDateTime\": \"2024-09-30T04:45:15.79Z\", \"verdict\": \"suspicious\", \"remediationStatus\": \"none\", \"remediationStatusDetails\": null, \"roles\": [], \"detailedRoles\": [], \"tags\": [], \"processId\": 23608, \"parentProcessId\": 23664, \"processCommandLine\": \"\\\"firefox.exe\\\"\", \"processCreationDateTime\": \"2024-09-30T02:52:53.8871758Z\", \"parentProcessCreationDateTime\": \"2024-09-30T02:52:53.8562909Z\", \"detectionStatus\": \"detected\", \"mdeDeviceId\": \"75345y43985453545u454545\", \"imageFile\": { \"sha1\": \"8983629d1c8197f2942e2c0cee9734cb0a04870b\", \"sha256\": \"c765f3ed8a7bf7c2f5d72c1f4ce63493b24bdc11a603fcea1948d248ca2a83ef\", \"fileName\": \"firefox.exe\", \"filePath\": \"C:\\\\Program Files\\\\Mozilla Firefox\", \"fileSize\": 672328, \"filePublisher\": \"Mozilla Corporation\", \"signer\": null, \"issuer\": null }, \"parentProcessImageFile\": { \"sha1\": \"c331590a40ee6c46dd0bc61d0e2b18495a19396d\", \"sha256\": \"f2d11e53533eeb0731467950b5928a08375f6c22f5e24b556943eb30369f4ee5\", \"fileName\": \"firefox.exe\", \"filePath\": \"\\\\Device\\\\vol1\\\\Program Files\\\\Mozilla Firefox\", \"fileSize\": 672328, \"filePublisher\": \"oreily\", \"signer\": null, \"issuer\": null }, \"userAccount\": { \"accountName\": \"FannyMandy\", \"domainName\": \"AzureAD\", \"userSid\": \"S-1-***********\", \"azureAdUserId\": \"569c2f6c4b61c242\", \"userPrincipalName\": \"<EMAIL>\", \"displayName\": null } } ] }", "mime-type": null}, "propertyDescriptors": {"Batch Size": {"displayName": "<PERSON><PERSON> Si<PERSON>", "dynamic": false, "identifiesControllerService": false, "name": "<PERSON><PERSON> Si<PERSON>", "sensitive": false}, "Data Format": {"displayName": "Data Format", "dynamic": false, "identifiesControllerService": false, "name": "Data Format", "sensitive": false}, "File Size": {"displayName": "File Size", "dynamic": false, "identifiesControllerService": false, "name": "File Size", "sensitive": false}, "Unique FlowFiles": {"displayName": "Unique FlowFiles", "dynamic": false, "identifiesControllerService": false, "name": "Unique FlowFiles", "sensitive": false}, "character-set": {"displayName": "Character Set", "dynamic": false, "identifiesControllerService": false, "name": "character-set", "sensitive": false}, "generate-ff-custom-text": {"displayName": "Custom Text", "dynamic": false, "identifiesControllerService": false, "name": "generate-ff-custom-text", "sensitive": false}, "mime-type": {"displayName": "Mime Type", "dynamic": false, "identifiesControllerService": false, "name": "mime-type", "sensitive": false}}, "retriedRelationships": [], "retryCount": 10, "runDurationMillis": 0, "scheduledState": "ENABLED", "schedulingPeriod": "15 sec", "schedulingStrategy": "TIMER_DRIVEN", "style": {}, "type": "org.apache.nifi.processors.standard.GenerateFlowFile", "yieldDuration": "1 sec"}], "remoteProcessGroups": [], "scheduledState": "ENABLED", "statelessFlowTimeout": "1 min"}, "flowEncodingVersion": "1.0", "latest": false, "parameterContexts": {"fake-data": {"componentType": "PARAMETER_CONTEXT", "inheritedParameterContexts": ["magna.kafka_params"], "name": "fake-data", "parameters": []}, "magna.kafka_params": {"componentType": "PARAMETER_CONTEXT", "inheritedParameterContexts": [], "name": "magna.kafka_params", "parameters": [{"description": "", "name": "magna.kafka_brokers", "provided": false, "sensitive": false, "value": "magna2-kafka-bootstrap.persistence.svc.cluster.local:9093"}, {"description": "Magna master topic for raw logs", "name": "magna.kafka_topic_raw", "provided": false, "sensitive": false, "value": "raw-logs-unarchived"}]}}, "parameterProviders": {}}
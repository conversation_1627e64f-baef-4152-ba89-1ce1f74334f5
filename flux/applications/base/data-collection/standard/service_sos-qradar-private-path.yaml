---
apiVersion: v1
kind: Service
metadata:
  name: sos-qradar-private-path
  namespace: collection-jobs
  annotations:
    magna.ciso.ibm.com/component-domain: data-collection
    magna.ciso.ibm.com/deploy-to-local: "false"
    magna.ciso.ibm.com/deploy-to-dev: "true"
    magna.ciso.ibm.com/deploy-to-prod: "true"
    service.kubernetes.io/ibm-load-balancer-cloud-provider-vpc-lb-name: "sos-qradarprivate-path"
    service.kubernetes.io/ibm-load-balancer-cloud-provider-enable-features: "private-path"
    service.kubernetes.io/ibm-load-balancer-cloud-provider-ip-type: "private"
    service.kubernetes.io/ibm-load-balancer-cloud-provider-vpc-subnets: "0757-31c3edf9-13cb-4e98-90da-291df3c084e5"
    service.kubernetes.io/ibm-load-balancer-cloud-provider-vpc-node-selector: "ibm-cloud.kubernetes.io/worker-pool-name=loadbalancer-workload"
    service.kubernetes.io/ibm-load-balancer-cloud-provider-vpc-health-check-protocol: "tcp"
spec:
  selector:
    "app.kubernetes.io/name": "vector-syslog-sos"
  type: LoadBalancer
  ports:
    - name: qradar-tcp
      protocol: TCP
      port: 10514
      targetPort: 10514

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: vector-syslog-sos
  namespace: collection-jobs
  annotations:
    magna.ciso.ibm.com/component-domain: data-collection
    magna.ciso.ibm.com/deploy-to-local: "false"
spec:
  selector:
    matchLabels:
      "app.kubernetes.io/component": "vector-syslog-sos"
  serviceName: "vector-syslog-sos"
  replicas: 3
  template:
    metadata:
      labels:
        "app.kubernetes.io/name": "vector-syslog-sos"
        "app.kubernetes.io/component": "vector-syslog-sos"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: /metrics
        prometheus.io/port: "9363"
    spec:
      priorityClassName: magna-services
      terminationGracePeriodSeconds: 10
      containers:
        - name: vector
          image: "timberio/vector:0.40.0.custom.fa2654a-debian"
          workingDir: /etc/vector
          args: ["-c", "base.yaml", "-c", "sink.yaml"]
          ports:
            - containerPort: 1514
              name: syslog-tcp
              protocol: TCP
            - containerPort: 10514
              name: qradar-tcp
              protocol: TCP
            - containerPort: 1514
              name: syslog-udp
              protocol: UDP
          volumeMounts:
            - name: config
              mountPath: /etc/vector/
            - mountPath: /var/run/userdata/cluster-certificate
              name: cluster-certificate
              readOnly: true
            - mountPath: /var/run/userdata/kafka-client-certificate
              name: kafka-client-certificate
              readOnly: true
            - mountPath: /var/run/userdata/vector-sos-certificate
              name: vector-sos-certificate
              readOnly: true
          env:
            - name: KAFKA_BROKERS
              value: magna2-kafka-bootstrap.persistence.svc.cluster.local:9093
            - name: KAFKA_TOPIC
              value: raw-logs-unarchived-syslog-sos-oct03
            - name: KAFKA_CA_FILE
              value: /var/run/userdata/cluster-certificate/bundle.pem
            - name: KAFKA_CERT_FILE
              value: /var/run/userdata/kafka-client-certificate/user.crt
            - name: KAFKA_KEY_FILE
              value: /var/run/userdata/kafka-client-certificate/user.key
          resources:
            requests:
              cpu: "1"
            limits:
              memory: 4Gi
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: ibm-cloud.kubernetes.io/worker-pool-name
                operator: In
                values:
                  - loadbalancer-workload
      volumes:
        - name: config
          configMap:
            name: vector-syslog-sos
        - name: cluster-certificate
          configMap:
            name: cluster-trust-bundle
        - name: kafka-client-certificate
          secret:
            secretName: vector-default-kafka-user-new
        - name: vector-sos-certificate
          secret:
            secretName: vector-sos-certificate
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              "app.kubernetes.io/component": "vector-syslog-sos"

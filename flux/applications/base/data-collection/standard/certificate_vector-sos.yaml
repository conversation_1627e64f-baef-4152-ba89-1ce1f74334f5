---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: vector-sos
  namespace: collection-jobs
  annotations:
    "magna.ciso.ibm.com/component-domain": "data-collection"
    magna.ciso.ibm.com/deploy-to-local: "false"
spec:
  secretName: vector-sos-certificate
  duration: 2160h0m0s # 90d
  renewBefore: 360h0m0s # 15d
  subject:
    organizations:
      - magna
  privateKey:
    algorithm: ECDSA
    size: 256
    encoding: PKCS8
  usages:
    - server auth
  commonName: sos-qradar-magna-private-path.ciso.ibm.private
  dnsNames:
    - sos-qradar-magna-private-path.ciso.ibm.private
  issuerRef:
    name: default-issuer
    kind: Cluster<PERSON>ssuer

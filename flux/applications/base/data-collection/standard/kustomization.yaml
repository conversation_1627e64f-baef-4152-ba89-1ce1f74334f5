---
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - certificate_magna-aws.yaml
  - certificate_vector-sos.yaml
  - deployment_proofpoint-pod-mail-out.yaml
  - deployment_proofpoint-pod-message-in.yaml
  - deployment_proofpoint-pod-message-out.yaml
  - externalSecret_crowdstrike-falcon-siem-config.yaml
  - externalSecret_proofpoint-pod-mail-out-config.yaml
  - externalSecret_proofpoint-pod-message-in-config.yaml
  - externalSecret_proofpoint-pod-message-out-config.yaml
  - externalSecret_vector-syslog-cortex-tokens.yaml
  - externalSecret_vector-syslog-sos-cortex-tokens.yaml
  - helmRelease_collector-akamai-siem-apollo.yaml
  - helmRelease_collector-akamai-siem-bluemix.yaml
  - helmRelease_collector-akamai-siem-ibmcom.yaml
  - helmRelease_collector-akamai-siem-ibmw3id.yaml
  - helmRelease_collector-akamai-siem-itsecurity1.yaml
  - helmRelease_collector-akamai-siem-itsecurity2.yaml
  - helmRelease_collector-akamai-siem-itsecurity3.yaml
  - helmRelease_collector-akamai-siem-itsecurity4.yaml
  - helmRelease_collector-beekeeper-devices.yaml
  - helmRelease_collector-bluepages-test.yaml
  - helmRelease_collector-bluepages.yaml
  - helmRelease_collector-cisa-kev-catalog.yaml
  - helmRelease_collector-code42-alerts.yaml
  - helmRelease_collector-code42-audit-logs.yaml
  - helmRelease_collector-code42-files.yaml
  - helmRelease_collector-crowdstrike-inventory-dev.yaml
  - helmRelease_collector-crowdstrike-inventory-servers.yaml
  - helmRelease_collector-crowdstrike-spotlight-global-incremental.yaml
  - helmRelease_collector-fake-data-isv.yaml
  - helmRelease_collector-forward-networks.yaml
  - helmRelease_collector-ibm-cloud-iam-fetch-s3.yaml
  - helmRelease_collector-ibm-cloud-iam-list-s3.yaml
  - helmRelease_collector-ibm-verify-w3id-preprod.yaml
  - helmRelease_collector-ibm-verify-w3id-prod.yaml
  - helmRelease_collector-microsoft-o365-audit-ad.yaml
  - helmRelease_collector-microsoft-o365-audit-exchange.yaml
  - helmRelease_collector-microsoft-o365-audit-general.yaml
  - helmRelease_collector-microsoft-o365-audit-sharepoint.yaml
  - helmRelease_collector-microsoft-o365-dlp-all.yaml
  - helmRelease_collector-onboarding-service-providers.yaml
  - helmRelease_collector-onboarding-tenants.yaml
  - helmRelease_collector-proofpoint-pod-maillog-in.yaml
  - helmRelease_collector-proofpoint-pod-maillog-out.yaml
  - helmRelease_collector-proofpoint-pod-message-in.yaml
  - helmRelease_collector-proofpoint-pod-message-out.yaml
  - helmRelease_collector-proofpoint-tap.yaml
  - helmRelease_collector-ssca.yaml
  - helmRelease_collector-uptycs-cnapp-aws-accounts.yaml
  - helmRelease_collector-uptycs-cnapp-aws-ec2.yaml
  - helmRelease_collector-uptycs-cnapp-aws-elbv1.yaml
  - helmRelease_collector-uptycs-cnapp-aws-elbv2.yaml
  - helmRelease_collector-uptycs-cnapp-aws-igw.yaml
  - helmRelease_collector-uptycs-cnapp-aws-lambda.yaml
  - helmRelease_collector-uptycs-cnapp-aws-natgw.yaml
  - helmRelease_collector-uptycs-cnapp-aws-rds.yaml
  - helmRelease_collector-uptycs-cnapp-aws-redshift.yaml
  - helmRelease_collector-uptycs-cnapp-aws-s3.yaml
  - helmRelease_collector-uptycs-cnapp-aws-users.yaml
  - helmRelease_collector-uptycs-cnapp-azure-databases.yaml
  - helmRelease_collector-uptycs-cnapp-azure-dbservers.yaml
  - helmRelease_collector-uptycs-cnapp-azure-k8s.yaml
  - helmRelease_collector-uptycs-cnapp-azure-lb.yaml
  - helmRelease_collector-uptycs-cnapp-azure-natgw.yaml
  - helmRelease_collector-uptycs-cnapp-azure-sites.yaml
  - helmRelease_collector-uptycs-cnapp-azure-storage.yaml
  - helmRelease_collector-uptycs-cnapp-azure-users.yaml
  - helmRelease_collector-uptycs-cnapp-azure-vm.yaml
  - helmRelease_collector-xforce-threat-score.yaml
  - helmRelease_minifi-aws-cloudtrail-flow.yaml
  - helmRelease_minifi-box-events-flow.yaml
  - helmRelease_minifi-cio-it-eventstreams-flow.yaml
  - helmRelease_minifi-cisco-umbrella-dns-ddi-amer-flow.yaml
  - helmRelease_minifi-cisco-umbrella-dns-ddi-apac-flow.yaml
  - helmRelease_minifi-cisco-umbrella-dns-ddi-emea-flow.yaml
  - helmRelease_minifi-cisco-umbrella-dns-vpn-emea-flow.yaml
  - helmRelease_minifi-corp-sec-door-log-flow.yaml
  - helmRelease_minifi-crowdstrike-fdr-kafka-flow.yaml
  - helmRelease_minifi-crowdstrike-fdr-sqs-flow.yaml
  - helmRelease_minifi-crowdstrike-spotlight-global-flow.yaml
  - helmRelease_minifi-fake-local-data-flow.yaml
  - helmRelease_minifi-github-dorker-detectsecrets-flow.yaml
  - helmRelease_minifi-ims-employee-ipam-flow.yaml
  - helmRelease_minifi-isv-api-ibmidprod-flow.yaml
  - helmRelease_minifi-isv-api-protectb-flow.yaml
  - helmRelease_minifi-isv-api-w3idtest-flow.yaml
  - helmRelease_minifi-isv-objects-kafka-flow.yaml
  - helmRelease_minifi-isv-s3listing-flow.yaml
  - helmRelease_minifi-m365-alerts-kafka-flow.yaml
  - helmRelease_minifi-m365-events-kafka-flow.yaml
  - helmRelease_minifi-m365-machineactions-kafka-flow.yaml
  - helmRelease_minifi-magna1-archiver-ocsf-flow.yaml
  - helmRelease_minifi-magna1-indexer-akamai-flow.yaml
  - helmRelease_minifi-magna1-indexer-w3idtest-flow.yaml
  - helmRelease_minifi-microsoft-office365-flow.yaml
  - helmRelease_minifi-postman-detect-secrets-s3list-fetch-flow.yaml
  - helmRelease_minifi-qroc-s3fetch-flow.yaml
  - helmRelease_minifi-qroc-s3listing-flow.yaml
  - helmRelease_minifi-recorded-future-alerts.yaml
  - helmRelease_minifi-rf-identity-flow.yaml
  - helmRelease_minifi-sos-s3-fetch-flow.yaml
  - helmRelease_minifi-sos-vulnerabilities-flow.yaml
  - helmRelease_minifi-uptycs-detections-armada-flow.yaml
  - helmRelease_minifi-uptycs-detections-dev-flow.yaml
  - helmRelease_minifi-uptycs-detections-mind-flow.yaml
  - helmRelease_minifi-uptycs-detections-systems-flow.yaml
  - helmRelease_minifi-uptycs-detections-techzone-flow.yaml
  - helmRelease_minifi-uptycs-detections-think-flow.yaml
  - helmRelease_minifi-uptycs-detections-worldwide-flow.yaml
  - helmRelease_minifi-uptycs-detections-xforce-flow.yaml
  - helmRelease_minifi-uptycs-vulns-s3fetch-flow.yaml
  - helmRelease_minifi-uptycs-vulns-s3listing-flow.yaml
  - kafkaTopic_metadata-crowdstrikefdr-sqs-new.yaml
  - kafkaTopic_metadata-isv-object-keys-new.yaml
  - kafkaTopic_metadata-s3-ibm-cloud-iam-new.yaml
  - kafkaTopic_metadata-uptycs-vulnerabilities-keys-new.yaml
  - kafkaUser_collector-default-kafka-user-new.yaml
  - kafkaUser_minifi-default-kafka-user-new.yaml
  - kafkaUser_vector-default-kafka-user-new.yaml
  - networkPolicy_collection-jobs-inbound.yaml
  - networkPolicy_vector-syslog-inbound.yaml
  - networkPolicy_vector-syslog-sos-inbound.yaml
  - policy_inject-vector-cortex-tokens.yaml
  - policy_inject-vector-sos-cortex-tokens.yaml
  - policy_mutate-minifi-iam-anywhere.yaml
  - secretStore_collection-jobs.yaml
  - service_sos-qradar-private-path.yaml
  - service_syslog-events-bluefringe-east1.yaml
  - service_syslog-events-bluefringe-east2.yaml
  - service_syslog-events-bluefringe-east3.yaml
  - service_syslog-private-path.yaml
  - service_vector-syslog-sos.yaml
  - service_vector-syslog.yaml
  - statefulSet_crowdstrike-falcon-siem.yaml
  - statefulSet_vector-syslog-sos.yaml
  - statefulSet_vector-syslog.yaml
configMapGenerator:
  - name: akamai-siem-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Akamai_SIEM_-_API_to_Kafka.json
    options:
      disableNameSuffixHash: true
  - name: aws-cloudtrail-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/AWS_Cloud_Trail.json
    options:
      disableNameSuffixHash: true
  - name: aws-ec2-metadata-config
    namespace: collection-jobs
    files:
      - config=config/aws_config_ec2_metadata.ini
    options:
      disableNameSuffixHash: true
  - name: box-events-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Box_Events_API_to_Kafka.json
    options:
      disableNameSuffixHash: true
  - name: cio-it-eventstreams-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/CIO_IT_EventStreams.json
    options:
      disableNameSuffixHash: true
  - name: cisco-umbrella-s3-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Cisco_Umbrella_S3.json
    options:
      disableNameSuffixHash: true
  - name: corp-sec-door-log-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/CorpSec_DoorLog.json
    options:
      disableNameSuffixHash: true
  - name: crowdstrike-falconhoseclient-daemon
    namespace: collection-jobs
    files:
      - cs.falconhoseclient_daemon.cfg=config/cs.falconhoseclient_daemon.cfg
  - name: crowdstrike-fdr-kafka-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/CrowdStrike_FDR_S3_to_Kafka.json
    options:
      disableNameSuffixHash: true
  - name: crowdstrike-fdr-sqs-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/CrowdStrike_FDR_SQS_to_Kafka.json
    options:
      disableNameSuffixHash: true
  - name: crowdstrike-spotlight-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/CrowdStrike_Spotlight.json
    options:
      disableNameSuffixHash: true
  - name: github-dorker-detectsecrets-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Github_Dorker_Detect_Secrets.json
    options:
      disableNameSuffixHash: true
  - name: fake-local-data-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Fake_Data.json
    options:
      disableNameSuffixHash: true
  - name: generic-s3list-fetch-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Generic_S3_object_list_fetch_to_Kafka.json
    options:
      disableNameSuffixHash: true
  - name: generic-s3listing-to-kafka-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Generic_S3_object_listing_to_Kafka.json
    options:
      disableNameSuffixHash: true
  - name: ims-employee-ipam-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Cloud_Inventory_IMS_IPAM_Employees.json
    options:
      disableNameSuffixHash: true
  - name: isv-api-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/ISV_API_to_Kafka.json
    options:
      disableNameSuffixHash: true
  - name: isv-objects-kafka-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/ISV_Objects_COS_to_Kafka.json
    options:
      disableNameSuffixHash: true
  - name: magna1-archiver-ocsf-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Magna1_COS_Archiver_OCSF.json
    options:
      disableNameSuffixHash: true
  - name: magna1-akamai-indexer-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Magna1_Akamai_Indexer.json
    options:
      disableNameSuffixHash: true
  - name: magna1-isv-indexer-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Magna1_ISV_Indexer.json
    options:
      disableNameSuffixHash: true
  - name: microsoft-m365-defender-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Microsoft_M365_Defender.json
    options:
      disableNameSuffixHash: true
  - name: microsoft-eventhub-kafka-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/M365_EventHub_to_Kafka.json
    options:
      disableNameSuffixHash: true
  - name: microsoft-office365-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/O365.json
    options:
      disableNameSuffixHash: true
  - name: recorded-future-alerts
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Recorded_Future_Alerts.json
    options:
      disableNameSuffixHash: true
  - name: rf-identity-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Recorded_Future_Identity_Alerts.json
    options:
      disableNameSuffixHash: true
  - name: sos-s3-fetch-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/SOS_S3_objects_list_fetch_to_Kafka.json
    options:
      disableNameSuffixHash: true
  - name: sos-vulnerabilities-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/SOS_Vulnerabilities.json
    options:
      disableNameSuffixHash: true
  - name: uptycs-detections-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Uptycs_Detections_API_to_Kafka.json
    options:
      disableNameSuffixHash: true
  - name: uptycs-vulnerabilities-s3listing-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Uptycs_Vulnerabilities_S3_Listing.json
    options:
      disableNameSuffixHash: true
  - name: uptycs-vulnerabilities-s3fetch-flow
    namespace: collection-jobs
    files:
      - flow.json.raw=flows/Uptycs_Vulnerabilities_S3_Fetch.json
    options:
      disableNameSuffixHash: true
  - name: vector-crowdstrike-falcon-siem
    namespace: collection-jobs
    files:
      - vector.yaml=vector-crowdstrike-falcon-siem/vector.yaml
  - name: vector-syslog
    namespace: collection-jobs
    files:
      - vector.yaml=vector-syslog/vector.yaml
      - syslog_transform.vrl=vector-syslog/syslog_transform.vrl
  - name: vector-syslog-sos
    namespace: collection-jobs
    files:
      - base.yaml=vector-syslog-sos/base.yaml
      - sink.yaml=vector-syslog-sos/sink.yaml
      - inject_headers.vrl=vector-syslog-sos/inject_headers.vrl
patches:
  - target:
      kind: HelmRelease
      annotationSelector: magna.ciso.ibm.com/chart==minifi-deployment
    path: patches/minifi-helm-release-patch.yaml
  - target:
      kind: HelmRelease
      annotationSelector: magna.ciso.ibm.com/chart==collector
    path: patches/collector-helm-release-patch.yaml

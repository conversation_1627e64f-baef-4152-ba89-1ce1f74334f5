---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: vector-syslog-sos-inbound
  namespace: collection-jobs
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: vector-syslog-sos
  policyTypes:
    - Ingress
  ingress:
    - ports:
        - protocol: TCP
          port: 1514
        - protocol: UDP
          port: 1514
        - protocol: TCP
          port: 10514

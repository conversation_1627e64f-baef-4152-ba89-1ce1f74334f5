---
apiVersion: kyverno.io/v1
kind: Policy
metadata:
  name: inject-vector-sos-cortex-tokens
  namespace: collection-jobs
spec:
  rules:
    - name: mutate-pods-with-label
      skipBackgroundRequests: true
      match:
        any:
          - resources:
              selector:
                matchLabels:
                  "app.kubernetes.io/name": "vector-syslog-sos"
              kinds:
                - Pod
      preconditions:
        all:
          - key: "{{request.operation}}"
            operator: Equals
            value: CREATE
      mutate:
        patchStrategicMerge:
          spec:
            containers:
              - name: vector
                env:
                  - name: "CORTEX_API_ENDPOINT"
                    valueFrom:
                      secretKeyRef:
                        name: vector-syslog-sos-cortex-tokens
                        key: "cortex-api-endpoint"
                  - name: "CORTEX_API_TOKEN"
                    valueFrom:
                      secretKeyRef:
                        name: vector-syslog-sos-cortex-tokens
                        key: "cortex-ingest-{{request.object.metadata.labels.\"apps.kubernetes.io/pod-index\"}}"

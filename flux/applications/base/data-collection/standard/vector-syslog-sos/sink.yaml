sinks:
  prom_exporter:
    type: prometheus_exporter
    inputs:
      - vector_metrics
    address: 0.0.0.0:9363
  magna_kafka:
    type: kafka
    inputs:
      - inject_header
    bootstrap_servers: "${KAFKA_BROKERS}"
    compression: "snappy"
    batch:
      max_events: 20000
    buffer:
      - type: memory
        max_events: 100000
        when_full: drop_newest
    headers_key: "headers"
    encoding:
      codec: "json"
    librdkafka_options:
      "enable.ssl.certificate.verification": "false"
      "security.protocol": "ssl"
      "ssl.ca.location": "${KAFKA_CA_FILE}"
      "ssl.certificate.location": "${KAFKA_CERT_FILE}"
      "ssl.key.location": "${KAFKA_KEY_FILE}"
    topic: "${KAFKA_TOPIC}"
  cortex_http_raw:
    type: http
    inputs:
      - inbound_tcp
    uri: "${CORTEX_API_ENDPOINT}"
    auth:
      strategy: "bearer"
      token: "${CORTEX_API_TOKEN}"
    framing:
      method: "newline_delimited"
    encoding:
      codec: "json"
    buffer:
      - type: memory
        max_events: 100000
        when_full: drop_newest

---
api:
  enabled: true
  address: "127.0.0.1:8686"

sources:
  inbound_hec:
    type: "splunk_hec"
    store_hec_token: true
    address: "0.0.0.0:1514"
  inbound_tcp:
    type: "socket"
    address: "0.0.0.0:10514"
    mode: "tcp"
    framing:
      method: "newline_delimited"
    decoding:
      codec: "json"
    tls:
      enabled: true
      ca_file: "/var/run/userdata/vector-sos-certificate/ca.crt"
      crt_file: "/var/run/userdata/vector-sos-certificate/tls.crt"
      key_file: "/var/run/userdata/vector-sos-certificate/tls.key"
  vector_metrics:
    type: internal_metrics
    scrape_interval_secs: 10

transforms:
  inject_header:
    inputs:
      - inbound_hec
    type: "remap"
    file: "inject_headers.vrl"

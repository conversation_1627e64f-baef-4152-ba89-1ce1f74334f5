apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base/data-collection
images:
  - name: crowdstrike-siem-connector
    newName: us.icr.io/magna-icr-ns/css-ciso-dev-team-magna-images-docker-local/crowdstrike-siem-connector # {"$imagepolicy": "flux-system:crowdstrike-siem-connector-prod:name"}
    newTag: 2.0.11-pre.20250611210924-330b5a6c-falconhoseclient2.27.0 # {"$imagepolicy": "flux-system:crowdstrike-siem-connector-prod:tag"}
  - name: enrichment-writer
    newName: us.icr.io/magna-icr-ns/css-ciso-dev-team-magna-images-docker-local/enrichment-writer # {"$imagepolicy": "flux-system:enrichment-writer-prod:name"}
    newTag: 2.0.39-1fb31d2c # {"$imagepolicy": "flux-system:enrichment-writer-prod:tag"}
patches:
  - target:
      annotationSelector: magna.ciso.ibm.com/deploy-to-dev==false
    patch: |
      $patch: delete
      apiVersion: v1
      kind: Any
      metadata:
        name: any
  - target:
      kind: Deployment
      name: streamsets-agent
    path: patches/deployment-streamsets-agent-patch.yaml
  - target:
      kind: ExternalSecret
      name: crowdstrike-falcon-siem-config
    path: patches/externalSecret_crowdstrike-falcon-siem-config-patch.yaml
  - target:
      kind: HelmRelease
      annotationSelector: magna.ciso.ibm.com/chart==minifi-deployment
    path: patches/minifi-helm-release-patch.yaml
  - target:
      kind: HelmRelease
      annotationSelector: magna.ciso.ibm.com/chart==collector
    path: patches/collector-helm-release-patch.yaml
  - target:
      kind: SecretStore
    path: patches/secret-store-patch.yaml
  - target:
      kind: Service
      name: syslog-events-bluefringe-east1
    path: patches/service-syslog-events-bluefringe-east1-patch.yaml
  - target:
      kind: Service
      name: syslog-events-bluefringe-east2
    path: patches/service-syslog-events-bluefringe-east2-patch.yaml
  - target:
      kind: Service
      name: syslog-events-bluefringe-east3
    path: patches/service-syslog-events-bluefringe-east3-patch.yaml
  - target:
      kind: Service
      name: syslog-private-path
    path: patches/service-syslog-private-path-patch.yaml
  - target:
      kind: Service
      name: sos-qradar-private-path
    path: patches/service-sos-qradar-private-path.yaml
  - target:
      kind: StatefulSet
    path: patches/stateful-set-vector-syslog-patch.yaml
  - target:
      kind: Policy
      name: mutate-streamsets-iam-anywhere
      namespace: streamsets-collection-jobs
    path: patches/policy-mutate-streamsets-iam-anywhere-patch.yaml

package datalake

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// AnalyticsEngine provides analytics capabilities for the data lake
type AnalyticsEngine struct {
	config       *config.Config
	logger       *zap.Logger
	queryEngine  *QueryEngine
	tableManager *TableManager
	jobs         map[string]*AnalyticsJob
	running      bool
	ctx          context.Context
	cancel       context.CancelFunc
	wg           sync.WaitGroup
	mu           sync.RWMutex
}

// AnalyticsJob represents a running analytics job
type AnalyticsJob struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Status      string                 `json:"status"`
	Progress    float64                `json:"progress"`
	StartedAt   time.Time              `json:"started_at"`
	CompletedAt *time.Time             `json:"completed_at,omitempty"`
	Results     map[string]interface{} `json:"results,omitempty"`
	Error       string                 `json:"error,omitempty"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// NewAnalyticsEngine creates a new analytics engine
func NewAnalyticsEngine(cfg *config.Config, logger *zap.Logger, queryEngine *QueryEngine, tableManager *TableManager) (*AnalyticsEngine, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}
	if queryEngine == nil {
		return nil, fmt.Errorf("query engine is required")
	}
	if tableManager == nil {
		return nil, fmt.Errorf("table manager is required")
	}

	ctx, cancel := context.WithCancel(context.Background())

	ae := &AnalyticsEngine{
		config:       cfg,
		logger:       logger,
		queryEngine:  queryEngine,
		tableManager: tableManager,
		jobs:         make(map[string]*AnalyticsJob),
		ctx:          ctx,
		cancel:       cancel,
	}

	return ae, nil
}

// Start starts the analytics engine
func (ae *AnalyticsEngine) Start() error {
	ae.mu.Lock()
	defer ae.mu.Unlock()

	if ae.running {
		return fmt.Errorf("analytics engine is already running")
	}

	ae.logger.Info("Starting analytics engine")

	// Start background workers
	ae.wg.Add(1)
	go ae.jobMonitorWorker()

	ae.running = true
	ae.logger.Info("Analytics engine started successfully")

	return nil
}

// Stop stops the analytics engine
func (ae *AnalyticsEngine) Stop() error {
	ae.mu.Lock()
	defer ae.mu.Unlock()

	if !ae.running {
		return nil
	}

	ae.logger.Info("Stopping analytics engine")

	// Cancel context
	ae.cancel()

	// Wait for workers to finish
	ae.wg.Wait()

	ae.running = false
	ae.logger.Info("Analytics engine stopped")

	return nil
}

// RunAnalytics runs an analytics job
func (ae *AnalyticsEngine) RunAnalytics(ctx context.Context, request *AnalyticsRequest) (*AnalyticsResult, error) {
	if request == nil {
		return nil, fmt.Errorf("analytics request is required")
	}

	if request.JobID == "" {
		return nil, fmt.Errorf("job ID is required")
	}

	if request.JobType == "" {
		return nil, fmt.Errorf("job type is required")
	}

	if len(request.Tables) == 0 {
		return nil, fmt.Errorf("at least one table is required")
	}

	// Check if engine is running
	ae.mu.RLock()
	if !ae.running {
		ae.mu.RUnlock()
		return nil, fmt.Errorf("analytics engine is not running")
	}
	ae.mu.RUnlock()

	ae.logger.Info("Running analytics job",
		zap.String("job_id", request.JobID),
		zap.String("job_type", request.JobType))

	// Check for duplicate job ID
	ae.mu.Lock()
	if _, exists := ae.jobs[request.JobID]; exists {
		ae.mu.Unlock()
		return nil, fmt.Errorf("job with ID %s already exists", request.JobID)
	}

	// Create analytics job
	job := &AnalyticsJob{
		ID:         request.JobID,
		Type:       request.JobType,
		Status:     "running",
		Progress:   0.0,
		StartedAt:  time.Now(),
		Parameters: request.Parameters,
		Results:    make(map[string]interface{}),
	}

	// Store job
	ae.jobs[job.ID] = job
	ae.mu.Unlock()

	// Execute job based on type
	go ae.executeJob(ctx, job, request)

	// Return initial result
	result := &AnalyticsResult{
		JobID:    job.ID,
		Status:   job.Status,
		Results:  job.Results,
		Duration: 0,
		Metadata: map[string]interface{}{
			"job_type":   job.Type,
			"started_at": job.StartedAt,
		},
	}

	return result, nil
}

// GetJobStatus returns the status of an analytics job
func (ae *AnalyticsEngine) GetJobStatus(jobID string) (*AnalyticsJob, error) {
	if jobID == "" {
		return nil, fmt.Errorf("job ID is required")
	}

	ae.mu.RLock()
	defer ae.mu.RUnlock()

	job, exists := ae.jobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job %s not found", jobID)
	}

	return job, nil
}

// ListJobs returns all analytics jobs
func (ae *AnalyticsEngine) ListJobs() []*AnalyticsJob {
	ae.mu.RLock()
	defer ae.mu.RUnlock()

	jobs := make([]*AnalyticsJob, 0, len(ae.jobs))
	for _, job := range ae.jobs {
		jobs = append(jobs, job)
	}

	return jobs
}

// executeJob executes an analytics job
func (ae *AnalyticsEngine) executeJob(ctx context.Context, job *AnalyticsJob, request *AnalyticsRequest) {
	defer func() {
		if r := recover(); r != nil {
			ae.logger.Error("Analytics job panicked",
				zap.String("job_id", job.ID),
				zap.Any("panic", r))

			ae.mu.Lock()
			job.Status = "failed"
			job.Error = fmt.Sprintf("job panicked: %v", r)
			completedAt := time.Now()
			job.CompletedAt = &completedAt
			ae.mu.Unlock()
		}
	}()

	var err error
	switch job.Type {
	case "table_stats":
		err = ae.executeTableStatsJob(ctx, job, request)
	case "data_quality":
		err = ae.executeDataQualityJob(ctx, job, request)
	case "aggregation":
		err = ae.executeAggregationJob(ctx, job, request)
	case "trend_analysis":
		err = ae.executeTrendAnalysisJob(ctx, job, request)
	case "anomaly_detection":
		err = ae.executeAnomalyDetectionJob(ctx, job, request)
	default:
		err = fmt.Errorf("unsupported job type: %s", job.Type)
	}

	ae.mu.Lock()
	if err != nil {
		job.Status = "failed"
		job.Error = err.Error()
		ae.logger.Error("Analytics job failed",
			zap.String("job_id", job.ID),
			zap.Error(err))
	} else {
		job.Status = "completed"
		job.Progress = 100.0
		ae.logger.Info("Analytics job completed",
			zap.String("job_id", job.ID))
	}
	completedAt := time.Now()
	job.CompletedAt = &completedAt
	ae.mu.Unlock()
}

// executeTableStatsJob executes a table statistics job
func (ae *AnalyticsEngine) executeTableStatsJob(ctx context.Context, job *AnalyticsJob, request *AnalyticsRequest) error {
	ae.logger.Debug("Executing table stats job", zap.String("job_id", job.ID))

	results := make(map[string]interface{})

	for _, tableName := range request.Tables {
		// Parse namespace and table name
		namespace := "default"
		name := tableName
		if parts := strings.Split(tableName, "."); len(parts) == 2 {
			namespace = parts[0]
			name = parts[1]
		}

		// Get table statistics
		stats, err := ae.tableManager.GetTableStats(ctx, namespace, name)
		if err != nil {
			return fmt.Errorf("failed to get stats for table %s: %w", tableName, err)
		}

		results[tableName] = map[string]interface{}{
			"record_count":      stats.RecordCount,
			"file_count":        stats.FileCount,
			"total_size":        stats.TotalSize,
			"snapshot_count":    stats.SnapshotCount,
			"partition_count":   stats.PartitionCount,
			"average_file_size": stats.AverageFileSize,
			"last_updated":      stats.LastUpdated,
		}

		// Update progress
		ae.mu.Lock()
		job.Progress = float64(len(results)) / float64(len(request.Tables)) * 100
		ae.mu.Unlock()
	}

	ae.mu.Lock()
	job.Results = results
	ae.mu.Unlock()

	return nil
}

// executeDataQualityJob executes a data quality analysis job
func (ae *AnalyticsEngine) executeDataQualityJob(ctx context.Context, job *AnalyticsJob, request *AnalyticsRequest) error {
	ae.logger.Debug("Executing data quality job", zap.String("job_id", job.ID))

	results := make(map[string]interface{})

	for _, tableName := range request.Tables {
		// Simulate data quality checks
		time.Sleep(1 * time.Second) // Simulate processing time

		qualityMetrics := map[string]interface{}{
			"completeness":    0.95, // 95% complete
			"accuracy":        0.98, // 98% accurate
			"consistency":     0.92, // 92% consistent
			"validity":        0.97, // 97% valid
			"uniqueness":      0.99, // 99% unique
			"null_percentage": 0.05, // 5% null values
			"duplicate_count": 10,   // 10 duplicates found
		}

		results[tableName] = qualityMetrics

		// Update progress
		ae.mu.Lock()
		job.Progress = float64(len(results)) / float64(len(request.Tables)) * 100
		ae.mu.Unlock()
	}

	ae.mu.Lock()
	job.Results = results
	ae.mu.Unlock()

	return nil
}

// executeAggregationJob executes an aggregation job
func (ae *AnalyticsEngine) executeAggregationJob(ctx context.Context, job *AnalyticsJob, request *AnalyticsRequest) error {
	ae.logger.Debug("Executing aggregation job", zap.String("job_id", job.ID))

	// Get aggregation parameters
	groupBy, _ := request.Parameters["group_by"].(string)
	aggregateFunc, _ := request.Parameters["function"].(string)
	column, _ := request.Parameters["column"].(string)

	if groupBy == "" || aggregateFunc == "" || column == "" {
		return fmt.Errorf("missing required parameters: group_by, function, column")
	}

	results := make(map[string]interface{})

	for _, tableName := range request.Tables {
		// Build aggregation query
		sql := fmt.Sprintf("SELECT %s, %s(%s) as result FROM %s GROUP BY %s",
			groupBy, aggregateFunc, column, tableName, groupBy)

		// Execute query
		queryRequest := &QueryRequest{
			SQL: sql,
		}

		queryResult, err := ae.queryEngine.ExecuteQuery(ctx, queryRequest)
		if err != nil {
			return fmt.Errorf("failed to execute aggregation query: %w", err)
		}

		results[tableName] = map[string]interface{}{
			"query":     sql,
			"row_count": queryResult.RowCount,
			"columns":   queryResult.Columns,
			"results":   queryResult.Rows,
		}

		// Update progress
		ae.mu.Lock()
		job.Progress = float64(len(results)) / float64(len(request.Tables)) * 100
		ae.mu.Unlock()
	}

	ae.mu.Lock()
	job.Results = results
	ae.mu.Unlock()

	return nil
}

// executeTrendAnalysisJob executes a trend analysis job
func (ae *AnalyticsEngine) executeTrendAnalysisJob(ctx context.Context, job *AnalyticsJob, request *AnalyticsRequest) error {
	ae.logger.Debug("Executing trend analysis job", zap.String("job_id", job.ID))

	// Simulate trend analysis
	time.Sleep(3 * time.Second) // Simulate processing time

	results := map[string]interface{}{
		"trend_direction":  "increasing",
		"trend_strength":   0.75,
		"seasonal_pattern": true,
		"forecast": map[string]interface{}{
			"next_week":  1250,
			"next_month": 5200,
		},
	}

	ae.mu.Lock()
	job.Results = results
	ae.mu.Unlock()

	return nil
}

// executeAnomalyDetectionJob executes an anomaly detection job
func (ae *AnalyticsEngine) executeAnomalyDetectionJob(ctx context.Context, job *AnalyticsJob, request *AnalyticsRequest) error {
	ae.logger.Debug("Executing anomaly detection job", zap.String("job_id", job.ID))

	// Simulate anomaly detection
	time.Sleep(2 * time.Second) // Simulate processing time

	results := map[string]interface{}{
		"anomalies_detected": 3,
		"anomaly_score":      0.85,
		"anomalies": []map[string]interface{}{
			{
				"timestamp": time.Now().Add(-2 * time.Hour).Unix(),
				"value":     1500,
				"expected":  800,
				"score":     0.92,
			},
			{
				"timestamp": time.Now().Add(-1 * time.Hour).Unix(),
				"value":     50,
				"expected":  750,
				"score":     0.88,
			},
		},
	}

	ae.mu.Lock()
	job.Results = results
	ae.mu.Unlock()

	return nil
}

// jobMonitorWorker monitors running jobs
func (ae *AnalyticsEngine) jobMonitorWorker() {
	defer ae.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ae.ctx.Done():
			return
		case <-ticker.C:
			ae.cleanupCompletedJobs()
		}
	}
}

// cleanupCompletedJobs removes old completed jobs
func (ae *AnalyticsEngine) cleanupCompletedJobs() {
	ae.mu.Lock()
	defer ae.mu.Unlock()

	cutoff := time.Now().Add(-24 * time.Hour) // Keep jobs for 24 hours

	for jobID, job := range ae.jobs {
		if job.CompletedAt != nil && job.CompletedAt.Before(cutoff) {
			delete(ae.jobs, jobID)
			ae.logger.Debug("Cleaned up completed job", zap.String("job_id", jobID))
		}
	}
}

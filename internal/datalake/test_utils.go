package datalake

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// MockStorageClient is a simple mock implementation of storage.Client for testing
type MockStorageClient struct {
	objects map[string][]byte
	mu      sync.RWMutex
}

func NewMockStorageClient() *MockStorageClient {
	return &MockStorageClient{
		objects: make(map[string][]byte),
	}
}

func (m *MockStorageClient) Upload(ctx context.Context, key string, reader io.Reader) error {
	data, err := io.ReadAll(reader)
	if err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()
	m.objects[key] = data
	return nil
}

func (m *MockStorageClient) Download(ctx context.Context, key string) (io.ReadCloser, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	data, exists := m.objects[key]
	if !exists {
		return nil, fmt.Errorf("object not found: %s", key)
	}

	return io.NopCloser(bytes.NewReader(data)), nil
}

func (m *MockStorageClient) Delete(ctx context.Context, key string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	delete(m.objects, key)
	return nil
}

func (m *MockStorageClient) List(ctx context.Context, prefix string) ([]string, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var keys []string
	for key := range m.objects {
		if strings.HasPrefix(key, prefix) {
			keys = append(keys, key)
		}
	}
	return keys, nil
}

func (m *MockStorageClient) Health(ctx context.Context) error {
	return nil
}

// CreateValidTestConfig creates a valid configuration for testing
func CreateValidTestConfig() *config.Config {
	return &config.Config{
		ObjectStorage: config.ObjectStorageConfig{
			Provider:        "s3",
			Endpoint:        "http://localhost:9000",
			AccessKeyID:     "minioadmin",
			SecretAccessKey: "minioadmin",
			Bucket:          "test-bucket",
			Region:          "us-east-1",
			UseSSL:          false,
		},
		Storage: config.StorageConfig{
			Type:   "s3",
			Bucket: "test-bucket",
			Region: "us-east-1",
		},
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
}

// CreateValidCatalogConfig creates a valid catalog configuration for testing
func CreateValidCatalogConfig() *config.Config {
	return &config.Config{
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
}

// CreateValidTableManagerConfig creates a valid table manager configuration for testing
func CreateValidTableManagerConfig() *config.Config {
	return CreateValidCatalogConfig()
}

// CreateValidQueryEngineConfig creates a valid query engine configuration for testing
func CreateValidQueryEngineConfig() *config.Config {
	return CreateValidCatalogConfig()
}

// CreateValidAnalyticsEngineConfig creates a valid analytics engine configuration for testing
func CreateValidAnalyticsEngineConfig() *config.Config {
	return CreateValidCatalogConfig()
}

// ===== COMPONENT INTERFACES FOR MOCKING =====

// CatalogInterface defines the interface for catalog operations
type CatalogInterface interface {
	Start() error
	Stop() error
	Health(ctx context.Context) error
	CreateNamespace(ctx context.Context, namespace string, properties map[string]string) error
	DropNamespace(ctx context.Context, namespace string) error
	ListNamespaces(ctx context.Context) ([]*CatalogNamespace, error)
	CreateTable(ctx context.Context, request *CreateTableRequest) (*Table, error)
	DropTable(ctx context.Context, namespace, tableName string) error
	GetTable(ctx context.Context, namespace, tableName string) (*Table, error)
	ListTables(ctx context.Context, namespace string) ([]*Table, error)
}

// TableManagerInterface defines the interface for table manager operations
type TableManagerInterface interface {
	Start() error
	Stop() error
	CreateTable(ctx context.Context, request *CreateTableRequest) (*Table, error)
	WriteData(ctx context.Context, request *WriteDataRequest) (*WriteResult, error)
	ReadData(ctx context.Context, request *ReadDataRequest) (*ReadResult, error)
	DropTable(ctx context.Context, namespace, tableName string) error
	GetTableStats(ctx context.Context, namespace, tableName string) (*TableStats, error)
}

// QueryEngineInterface defines the interface for query engine operations
type QueryEngineInterface interface {
	Start() error
	Stop() error
	ExecuteQuery(ctx context.Context, request *QueryRequest) (*QueryResult, error)
}

// AnalyticsEngineInterface defines the interface for analytics engine operations
type AnalyticsEngineInterface interface {
	Start() error
	Stop() error
	RunAnalytics(ctx context.Context, request *AnalyticsRequest) (*AnalyticsResult, error)
	GetJob(ctx context.Context, jobID string) (*AnalyticsJob, error)
	ListJobs(ctx context.Context) ([]*AnalyticsJob, error)
	CancelJob(ctx context.Context, jobID string) error
}

// ===== ADVANCED MOCK IMPLEMENTATIONS =====

// MockBehavior defines configurable behavior for mocks
type MockBehavior struct {
	ShouldFail        bool
	FailureMessage    string
	Delay             time.Duration
	CallCount         int
	MaxCalls          int
	SimulateTimeout   bool
	SimulateRateLimit bool
}

// MockCatalog provides an advanced mock implementation of CatalogInterface
type MockCatalog struct {
	mu          sync.RWMutex
	running     bool
	namespaces  map[string]*CatalogNamespace
	tables      map[string]*Table
	behavior    map[string]*MockBehavior
	callHistory []string
	logger      *zap.Logger
}

// NewMockCatalog creates a new mock catalog with configurable behavior
func NewMockCatalog(logger *zap.Logger) *MockCatalog {
	if logger == nil {
		logger, _ = zap.NewDevelopment()
	}

	return &MockCatalog{
		namespaces:  make(map[string]*CatalogNamespace),
		tables:      make(map[string]*Table),
		behavior:    make(map[string]*MockBehavior),
		callHistory: make([]string, 0),
		logger:      logger,
	}
}

// SetBehavior configures the behavior for a specific method
func (m *MockCatalog) SetBehavior(method string, behavior *MockBehavior) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.behavior[method] = behavior
}

// GetCallHistory returns the history of method calls
func (m *MockCatalog) GetCallHistory() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()
	history := make([]string, len(m.callHistory))
	copy(history, m.callHistory)
	return history
}

// recordCall records a method call and applies configured behavior
func (m *MockCatalog) recordCall(method string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.callHistory = append(m.callHistory, method)

	if behavior, exists := m.behavior[method]; exists {
		behavior.CallCount++

		// Check max calls limit
		if behavior.MaxCalls > 0 && behavior.CallCount > behavior.MaxCalls {
			return fmt.Errorf("method %s exceeded max calls limit (%d)", method, behavior.MaxCalls)
		}

		// Simulate delay
		if behavior.Delay > 0 {
			time.Sleep(behavior.Delay)
		}

		// Simulate timeout
		if behavior.SimulateTimeout {
			return context.DeadlineExceeded
		}

		// Simulate rate limiting
		if behavior.SimulateRateLimit {
			return fmt.Errorf("rate limit exceeded for method %s", method)
		}

		// Simulate failure
		if behavior.ShouldFail {
			return fmt.Errorf("mock failure for %s: %s", method, behavior.FailureMessage)
		}
	}

	return nil
}

// Start implements CatalogInterface
func (m *MockCatalog) Start() error {
	if err := m.recordCall("Start"); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("catalog is already running")
	}

	m.running = true
	m.logger.Info("Mock catalog started")
	return nil
}

// Stop implements CatalogInterface
func (m *MockCatalog) Stop() error {
	if err := m.recordCall("Stop"); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	m.running = false
	m.logger.Info("Mock catalog stopped")
	return nil
}

// Health implements CatalogInterface
func (m *MockCatalog) Health(ctx context.Context) error {
	if err := m.recordCall("Health"); err != nil {
		return err
	}

	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.running {
		return fmt.Errorf("catalog is not running")
	}

	return nil
}

// CreateNamespace implements CatalogInterface
func (m *MockCatalog) CreateNamespace(ctx context.Context, namespace string, properties map[string]string) error {
	if err := m.recordCall("CreateNamespace"); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return fmt.Errorf("catalog is not running")
	}

	if _, exists := m.namespaces[namespace]; exists {
		return fmt.Errorf("namespace %s already exists", namespace)
	}

	m.namespaces[namespace] = &CatalogNamespace{
		Name:       namespace,
		Properties: properties,
		CreatedAt:  time.Now(),
	}

	return nil
}

// DropNamespace implements CatalogInterface
func (m *MockCatalog) DropNamespace(ctx context.Context, namespace string) error {
	if err := m.recordCall("DropNamespace"); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return fmt.Errorf("catalog is not running")
	}

	if _, exists := m.namespaces[namespace]; !exists {
		return fmt.Errorf("namespace %s does not exist", namespace)
	}

	delete(m.namespaces, namespace)
	return nil
}

// ListNamespaces implements CatalogInterface
func (m *MockCatalog) ListNamespaces(ctx context.Context) ([]*CatalogNamespace, error) {
	if err := m.recordCall("ListNamespaces"); err != nil {
		return nil, err
	}

	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.running {
		return nil, fmt.Errorf("catalog is not running")
	}

	namespaces := make([]*CatalogNamespace, 0, len(m.namespaces))
	for _, ns := range m.namespaces {
		namespaces = append(namespaces, ns)
	}

	return namespaces, nil
}

// CreateTable implements CatalogInterface
func (m *MockCatalog) CreateTable(ctx context.Context, request *CreateTableRequest) (*Table, error) {
	if err := m.recordCall("CreateTable"); err != nil {
		return nil, err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil, fmt.Errorf("catalog is not running")
	}

	if request == nil {
		return nil, fmt.Errorf("create table request is required")
	}

	tableKey := fmt.Sprintf("%s.%s", request.Namespace, request.TableName)
	if _, exists := m.tables[tableKey]; exists {
		return nil, fmt.Errorf("table %s already exists", tableKey)
	}

	table := &Table{
		ID:         GenerateID(),
		Namespace:  request.Namespace,
		Name:       request.TableName,
		Location:   fmt.Sprintf("s3://test-bucket/%s/%s", request.Namespace, request.TableName),
		Schema:     request.Schema,
		Properties: request.Properties,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Snapshots:  []Snapshot{},
		Metadata:   make(map[string]interface{}),
	}

	m.tables[tableKey] = table
	return table, nil
}

// DropTable implements CatalogInterface
func (m *MockCatalog) DropTable(ctx context.Context, namespace, tableName string) error {
	if err := m.recordCall("DropTable"); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return fmt.Errorf("catalog is not running")
	}

	tableKey := fmt.Sprintf("%s.%s", namespace, tableName)
	if _, exists := m.tables[tableKey]; !exists {
		return fmt.Errorf("table %s does not exist", tableKey)
	}

	delete(m.tables, tableKey)
	return nil
}

// GetTable implements CatalogInterface
func (m *MockCatalog) GetTable(ctx context.Context, namespace, tableName string) (*Table, error) {
	if err := m.recordCall("GetTable"); err != nil {
		return nil, err
	}

	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.running {
		return nil, fmt.Errorf("catalog is not running")
	}

	tableKey := fmt.Sprintf("%s.%s", namespace, tableName)
	table, exists := m.tables[tableKey]
	if !exists {
		return nil, fmt.Errorf("table %s does not exist", tableKey)
	}

	return table, nil
}

// ListTables implements CatalogInterface
func (m *MockCatalog) ListTables(ctx context.Context, namespace string) ([]*Table, error) {
	if err := m.recordCall("ListTables"); err != nil {
		return nil, err
	}

	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.running {
		return nil, fmt.Errorf("catalog is not running")
	}

	tables := make([]*Table, 0)
	for tableKey, table := range m.tables {
		if strings.HasPrefix(tableKey, namespace+".") {
			tables = append(tables, table)
		}
	}

	return tables, nil
}

// MockTableManager provides an advanced mock implementation of TableManagerInterface
type MockTableManager struct {
	mu          sync.RWMutex
	running     bool
	tables      map[string]*Table
	behavior    map[string]*MockBehavior
	callHistory []string
	logger      *zap.Logger
	catalog     CatalogInterface
}

// NewMockTableManager creates a new mock table manager
func NewMockTableManager(logger *zap.Logger, catalog CatalogInterface) *MockTableManager {
	if logger == nil {
		logger, _ = zap.NewDevelopment()
	}

	return &MockTableManager{
		tables:      make(map[string]*Table),
		behavior:    make(map[string]*MockBehavior),
		callHistory: make([]string, 0),
		logger:      logger,
		catalog:     catalog,
	}
}

// SetBehavior configures the behavior for a specific method
func (m *MockTableManager) SetBehavior(method string, behavior *MockBehavior) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.behavior[method] = behavior
}

// GetCallHistory returns the history of method calls
func (m *MockTableManager) GetCallHistory() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()
	history := make([]string, len(m.callHistory))
	copy(history, m.callHistory)
	return history
}

// recordCall records a method call and applies configured behavior
func (m *MockTableManager) recordCall(method string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.callHistory = append(m.callHistory, method)

	if behavior, exists := m.behavior[method]; exists {
		behavior.CallCount++

		if behavior.MaxCalls > 0 && behavior.CallCount > behavior.MaxCalls {
			return fmt.Errorf("method %s exceeded max calls limit (%d)", method, behavior.MaxCalls)
		}

		if behavior.Delay > 0 {
			time.Sleep(behavior.Delay)
		}

		if behavior.SimulateTimeout {
			return context.DeadlineExceeded
		}

		if behavior.SimulateRateLimit {
			return fmt.Errorf("rate limit exceeded for method %s", method)
		}

		if behavior.ShouldFail {
			return fmt.Errorf("mock failure for %s: %s", method, behavior.FailureMessage)
		}
	}

	return nil
}

// Start implements TableManagerInterface
func (m *MockTableManager) Start() error {
	if err := m.recordCall("Start"); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("table manager is already running")
	}

	m.running = true
	m.logger.Info("Mock table manager started")
	return nil
}

// Stop implements TableManagerInterface
func (m *MockTableManager) Stop() error {
	if err := m.recordCall("Stop"); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	m.running = false
	m.logger.Info("Mock table manager stopped")
	return nil
}

// CreateTable implements TableManagerInterface
func (m *MockTableManager) CreateTable(ctx context.Context, request *CreateTableRequest) (*Table, error) {
	if err := m.recordCall("CreateTable"); err != nil {
		return nil, err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil, fmt.Errorf("table manager is not running")
	}

	if request == nil {
		return nil, fmt.Errorf("create table request is required")
	}

	// Delegate to catalog if available
	if m.catalog != nil {
		return m.catalog.CreateTable(ctx, request)
	}

	// Fallback to local implementation
	tableKey := fmt.Sprintf("%s.%s", request.Namespace, request.TableName)
	if _, exists := m.tables[tableKey]; exists {
		return nil, fmt.Errorf("table %s already exists", tableKey)
	}

	table := &Table{
		ID:         GenerateID(),
		Namespace:  request.Namespace,
		Name:       request.TableName,
		Location:   fmt.Sprintf("s3://test-bucket/%s/%s", request.Namespace, request.TableName),
		Schema:     request.Schema,
		Properties: request.Properties,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Snapshots:  []Snapshot{},
		Metadata:   make(map[string]interface{}),
	}

	m.tables[tableKey] = table
	return table, nil
}

// WriteData implements TableManagerInterface
func (m *MockTableManager) WriteData(ctx context.Context, request *WriteDataRequest) (*WriteResult, error) {
	if err := m.recordCall("WriteData"); err != nil {
		return nil, err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil, fmt.Errorf("table manager is not running")
	}

	if request == nil {
		return nil, fmt.Errorf("write data request is required")
	}

	// Simulate write operation
	result := &WriteResult{
		RecordsWritten: int64(len(request.Records)),
		BytesWritten:   int64(len(request.Records) * 100), // Simulate 100 bytes per record
		FilesAdded:     1,
		SnapshotID:     int64(time.Now().Unix()),
		Duration:       time.Millisecond * 100,
		Metadata:       make(map[string]interface{}),
	}

	return result, nil
}

// ReadData implements TableManagerInterface
func (m *MockTableManager) ReadData(ctx context.Context, request *ReadDataRequest) (*ReadResult, error) {
	if err := m.recordCall("ReadData"); err != nil {
		return nil, err
	}

	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.running {
		return nil, fmt.Errorf("table manager is not running")
	}

	if request == nil {
		return nil, fmt.Errorf("read data request is required")
	}

	// Simulate read operation with mock data
	mockRecords := []map[string]interface{}{
		{"id": 1, "name": "test1", "timestamp": time.Now()},
		{"id": 2, "name": "test2", "timestamp": time.Now()},
	}

	result := &ReadResult{
		Records:     mockRecords,
		RecordsRead: int64(len(mockRecords)),
		Duration:    time.Millisecond * 50,
		Metadata:    make(map[string]interface{}),
	}

	return result, nil
}

// DropTable implements TableManagerInterface
func (m *MockTableManager) DropTable(ctx context.Context, namespace, tableName string) error {
	if err := m.recordCall("DropTable"); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return fmt.Errorf("table manager is not running")
	}

	// Delegate to catalog if available
	if m.catalog != nil {
		return m.catalog.DropTable(ctx, namespace, tableName)
	}

	// Fallback to local implementation
	tableKey := fmt.Sprintf("%s.%s", namespace, tableName)
	if _, exists := m.tables[tableKey]; !exists {
		return fmt.Errorf("table %s does not exist", tableKey)
	}

	delete(m.tables, tableKey)
	return nil
}

// GetTableStats implements TableManagerInterface
func (m *MockTableManager) GetTableStats(ctx context.Context, namespace, tableName string) (*TableStats, error) {
	if err := m.recordCall("GetTableStats"); err != nil {
		return nil, err
	}

	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.running {
		return nil, fmt.Errorf("table manager is not running")
	}

	// Return mock statistics
	stats := &TableStats{
		TableName:       tableName,
		Namespace:       namespace,
		RecordCount:     1000,
		FileCount:       5,
		TotalSize:       1024000,
		SnapshotCount:   3,
		LastUpdated:     time.Now(),
		PartitionCount:  10,
		AverageFileSize: 204800,
	}

	return stats, nil
}

// MockQueryEngine provides an advanced mock implementation of QueryEngineInterface
type MockQueryEngine struct {
	mu          sync.RWMutex
	running     bool
	behavior    map[string]*MockBehavior
	callHistory []string
	logger      *zap.Logger
	queryCache  map[string]*QueryResult
}

// NewMockQueryEngine creates a new mock query engine
func NewMockQueryEngine(logger *zap.Logger) *MockQueryEngine {
	if logger == nil {
		logger, _ = zap.NewDevelopment()
	}

	return &MockQueryEngine{
		behavior:    make(map[string]*MockBehavior),
		callHistory: make([]string, 0),
		logger:      logger,
		queryCache:  make(map[string]*QueryResult),
	}
}

// SetBehavior configures the behavior for a specific method
func (m *MockQueryEngine) SetBehavior(method string, behavior *MockBehavior) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.behavior[method] = behavior
}

// GetCallHistory returns the history of method calls
func (m *MockQueryEngine) GetCallHistory() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()
	history := make([]string, len(m.callHistory))
	copy(history, m.callHistory)
	return history
}

// recordCall records a method call and applies configured behavior
func (m *MockQueryEngine) recordCall(method string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.callHistory = append(m.callHistory, method)

	if behavior, exists := m.behavior[method]; exists {
		behavior.CallCount++

		if behavior.MaxCalls > 0 && behavior.CallCount > behavior.MaxCalls {
			return fmt.Errorf("method %s exceeded max calls limit (%d)", method, behavior.MaxCalls)
		}

		if behavior.Delay > 0 {
			time.Sleep(behavior.Delay)
		}

		if behavior.SimulateTimeout {
			return context.DeadlineExceeded
		}

		if behavior.SimulateRateLimit {
			return fmt.Errorf("rate limit exceeded for method %s", method)
		}

		if behavior.ShouldFail {
			return fmt.Errorf("mock failure for %s: %s", method, behavior.FailureMessage)
		}
	}

	return nil
}

// Start implements QueryEngineInterface
func (m *MockQueryEngine) Start() error {
	if err := m.recordCall("Start"); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("query engine is already running")
	}

	m.running = true
	m.logger.Info("Mock query engine started")
	return nil
}

// Stop implements QueryEngineInterface
func (m *MockQueryEngine) Stop() error {
	if err := m.recordCall("Stop"); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	m.running = false
	m.logger.Info("Mock query engine stopped")
	return nil
}

// ExecuteQuery implements QueryEngineInterface
func (m *MockQueryEngine) ExecuteQuery(ctx context.Context, request *QueryRequest) (*QueryResult, error) {
	if err := m.recordCall("ExecuteQuery"); err != nil {
		return nil, err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil, fmt.Errorf("query engine is not running")
	}

	if request == nil {
		return nil, fmt.Errorf("query request is required")
	}

	// Check cache first
	cacheKey := fmt.Sprintf("%s:%v", request.SQL, request.Parameters)
	if cached, exists := m.queryCache[cacheKey]; exists {
		return cached, nil
	}

	// Simulate query execution with different results based on SQL
	var mockRows [][]interface{}
	var columns []string

	if strings.Contains(strings.ToLower(request.SQL), "count") {
		mockRows = [][]interface{}{
			{100, "test_table"},
			{200, "another_table"},
		}
		columns = []string{"count", "table"}
	} else {
		mockRows = [][]interface{}{
			{1, "Alice", "Engineering"},
			{2, "Bob", "Marketing"},
			{3, "Charlie", "Sales"},
		}
		columns = []string{"id", "name", "department"}
	}

	result := &QueryResult{
		Columns:  columns,
		Rows:     mockRows,
		RowCount: int64(len(mockRows)),
		Duration: time.Millisecond * 250,
		Metadata: make(map[string]interface{}),
	}

	// Cache the result
	m.queryCache[cacheKey] = result

	return result, nil
}

// MockAnalyticsEngine provides an advanced mock implementation of AnalyticsEngineInterface
type MockAnalyticsEngine struct {
	mu          sync.RWMutex
	running     bool
	jobs        map[string]*AnalyticsJob
	behavior    map[string]*MockBehavior
	callHistory []string
	logger      *zap.Logger
}

// NewMockAnalyticsEngine creates a new mock analytics engine
func NewMockAnalyticsEngine(logger *zap.Logger) *MockAnalyticsEngine {
	if logger == nil {
		logger, _ = zap.NewDevelopment()
	}

	return &MockAnalyticsEngine{
		jobs:        make(map[string]*AnalyticsJob),
		behavior:    make(map[string]*MockBehavior),
		callHistory: make([]string, 0),
		logger:      logger,
	}
}

// SetBehavior configures the behavior for a specific method
func (m *MockAnalyticsEngine) SetBehavior(method string, behavior *MockBehavior) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.behavior[method] = behavior
}

// GetCallHistory returns the history of method calls
func (m *MockAnalyticsEngine) GetCallHistory() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()
	history := make([]string, len(m.callHistory))
	copy(history, m.callHistory)
	return history
}

// recordCall records a method call and applies configured behavior
func (m *MockAnalyticsEngine) recordCall(method string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.callHistory = append(m.callHistory, method)

	if behavior, exists := m.behavior[method]; exists {
		behavior.CallCount++

		if behavior.MaxCalls > 0 && behavior.CallCount > behavior.MaxCalls {
			return fmt.Errorf("method %s exceeded max calls limit (%d)", method, behavior.MaxCalls)
		}

		if behavior.Delay > 0 {
			time.Sleep(behavior.Delay)
		}

		if behavior.SimulateTimeout {
			return context.DeadlineExceeded
		}

		if behavior.SimulateRateLimit {
			return fmt.Errorf("rate limit exceeded for method %s", method)
		}

		if behavior.ShouldFail {
			return fmt.Errorf("mock failure for %s: %s", method, behavior.FailureMessage)
		}
	}

	return nil
}

// Start implements AnalyticsEngineInterface
func (m *MockAnalyticsEngine) Start() error {
	if err := m.recordCall("Start"); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("analytics engine is already running")
	}

	m.running = true
	m.logger.Info("Mock analytics engine started")
	return nil
}

// Stop implements AnalyticsEngineInterface
func (m *MockAnalyticsEngine) Stop() error {
	if err := m.recordCall("Stop"); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	m.running = false
	m.logger.Info("Mock analytics engine stopped")
	return nil
}

// RunAnalytics implements AnalyticsEngineInterface
func (m *MockAnalyticsEngine) RunAnalytics(ctx context.Context, request *AnalyticsRequest) (*AnalyticsResult, error) {
	if err := m.recordCall("RunAnalytics"); err != nil {
		return nil, err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil, fmt.Errorf("analytics engine is not running")
	}

	if request == nil {
		return nil, fmt.Errorf("analytics request is required")
	}

	// Create mock job
	completedAt := time.Now()
	job := &AnalyticsJob{
		ID:          request.JobID,
		Type:        request.JobType,
		Status:      "completed",
		Progress:    100.0,
		StartedAt:   time.Now().Add(-time.Minute),
		CompletedAt: &completedAt,
		Parameters:  request.Parameters,
		Results:     make(map[string]interface{}),
	}

	// Add mock results based on job type
	switch request.JobType {
	case "table_stats":
		job.Results["total_records"] = 10000
		job.Results["total_size"] = 1024000
		job.Results["file_count"] = 5
	case "data_quality":
		job.Results["null_count"] = 100
		job.Results["duplicate_count"] = 50
		job.Results["quality_score"] = 0.95
	}

	m.jobs[request.JobID] = job

	result := &AnalyticsResult{
		JobID:    request.JobID,
		Status:   "completed",
		Results:  job.Results,
		Duration: time.Minute,
		Metadata: make(map[string]interface{}),
	}

	return result, nil
}

// GetJob implements AnalyticsEngineInterface
func (m *MockAnalyticsEngine) GetJob(ctx context.Context, jobID string) (*AnalyticsJob, error) {
	if err := m.recordCall("GetJob"); err != nil {
		return nil, err
	}

	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.running {
		return nil, fmt.Errorf("analytics engine is not running")
	}

	job, exists := m.jobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job %s not found", jobID)
	}

	return job, nil
}

// ListJobs implements AnalyticsEngineInterface
func (m *MockAnalyticsEngine) ListJobs(ctx context.Context) ([]*AnalyticsJob, error) {
	if err := m.recordCall("ListJobs"); err != nil {
		return nil, err
	}

	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.running {
		return nil, fmt.Errorf("analytics engine is not running")
	}

	jobs := make([]*AnalyticsJob, 0, len(m.jobs))
	for _, job := range m.jobs {
		jobs = append(jobs, job)
	}

	return jobs, nil
}

// CancelJob implements AnalyticsEngineInterface
func (m *MockAnalyticsEngine) CancelJob(ctx context.Context, jobID string) error {
	if err := m.recordCall("CancelJob"); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return fmt.Errorf("analytics engine is not running")
	}

	job, exists := m.jobs[jobID]
	if !exists {
		return fmt.Errorf("job %s not found", jobID)
	}

	job.Status = "cancelled"
	cancelledAt := time.Now()
	job.CompletedAt = &cancelledAt

	return nil
}

// ===== MOCK FACTORY FUNCTIONS =====

// MockComponentSet represents a complete set of mock components
type MockComponentSet struct {
	Storage         *MockStorageClient
	Catalog         *MockCatalog
	TableManager    *MockTableManager
	QueryEngine     *MockQueryEngine
	AnalyticsEngine *MockAnalyticsEngine
	Logger          *zap.Logger
}

// NewMockComponentSet creates a complete set of interconnected mock components
func NewMockComponentSet() *MockComponentSet {
	logger, _ := zap.NewDevelopment()

	storage := NewMockStorageClient()
	catalog := NewMockCatalog(logger)
	tableManager := NewMockTableManager(logger, catalog)
	queryEngine := NewMockQueryEngine(logger)
	analyticsEngine := NewMockAnalyticsEngine(logger)

	return &MockComponentSet{
		Storage:         storage,
		Catalog:         catalog,
		TableManager:    tableManager,
		QueryEngine:     queryEngine,
		AnalyticsEngine: analyticsEngine,
		Logger:          logger,
	}
}

// StartAll starts all mock components
func (mcs *MockComponentSet) StartAll() error {
	if err := mcs.Catalog.Start(); err != nil {
		return fmt.Errorf("failed to start catalog: %w", err)
	}

	if err := mcs.TableManager.Start(); err != nil {
		return fmt.Errorf("failed to start table manager: %w", err)
	}

	if err := mcs.QueryEngine.Start(); err != nil {
		return fmt.Errorf("failed to start query engine: %w", err)
	}

	if err := mcs.AnalyticsEngine.Start(); err != nil {
		return fmt.Errorf("failed to start analytics engine: %w", err)
	}

	return nil
}

// StopAll stops all mock components
func (mcs *MockComponentSet) StopAll() error {
	var errors []string

	if err := mcs.AnalyticsEngine.Stop(); err != nil {
		errors = append(errors, fmt.Sprintf("analytics engine: %v", err))
	}

	if err := mcs.QueryEngine.Stop(); err != nil {
		errors = append(errors, fmt.Sprintf("query engine: %v", err))
	}

	if err := mcs.TableManager.Stop(); err != nil {
		errors = append(errors, fmt.Sprintf("table manager: %v", err))
	}

	if err := mcs.Catalog.Stop(); err != nil {
		errors = append(errors, fmt.Sprintf("catalog: %v", err))
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors stopping components: %s", strings.Join(errors, ", "))
	}

	return nil
}

// ConfigureFailureBehavior configures failure behavior for all components
func (mcs *MockComponentSet) ConfigureFailureBehavior(method string, shouldFail bool, message string) {
	behavior := &MockBehavior{
		ShouldFail:     shouldFail,
		FailureMessage: message,
	}

	mcs.Catalog.SetBehavior(method, behavior)
	mcs.TableManager.SetBehavior(method, behavior)
	mcs.QueryEngine.SetBehavior(method, behavior)
	mcs.AnalyticsEngine.SetBehavior(method, behavior)
}

// ConfigureDelayBehavior configures delay behavior for all components
func (mcs *MockComponentSet) ConfigureDelayBehavior(method string, delay time.Duration) {
	behavior := &MockBehavior{
		Delay: delay,
	}

	mcs.Catalog.SetBehavior(method, behavior)
	mcs.TableManager.SetBehavior(method, behavior)
	mcs.QueryEngine.SetBehavior(method, behavior)
	mcs.AnalyticsEngine.SetBehavior(method, behavior)
}

// GetAllCallHistory returns call history from all components
func (mcs *MockComponentSet) GetAllCallHistory() map[string][]string {
	return map[string][]string{
		"catalog":         mcs.Catalog.GetCallHistory(),
		"tableManager":    mcs.TableManager.GetCallHistory(),
		"queryEngine":     mcs.QueryEngine.GetCallHistory(),
		"analyticsEngine": mcs.AnalyticsEngine.GetCallHistory(),
	}
}

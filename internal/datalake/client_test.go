package datalake

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"go.uber.org/zap/zaptest"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

func TestNewClient(t *testing.T) {
	tests := []struct {
		name        string
		config      *config.Config
		expectError bool
		skipReason  string
	}{
		{
			name:        "nil_config",
			config:      nil,
			expectError: true,
		},
		{
			name:        "nil_logger_should_error",
			config:      CreateValidTestConfig(),
			expectError: true, // Logger is required
		},
		{
			name: "empty_object_storage_provider",
			config: &config.Config{
				ObjectStorage: config.ObjectStorageConfig{
					Provider: "",
					Bucket:   "test-bucket",
				},
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "rest",
						URI:  "http://localhost:8181",
					},
				},
			},
			expectError: true,
		},
		{
			name: "empty_bucket_name",
			config: &config.Config{
				ObjectStorage: config.ObjectStorageConfig{
					Provider: "s3",
					Bucket:   "",
				},
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "rest",
						URI:  "http://localhost:8181",
					},
				},
			},
			expectError: true,
		},
		{
			name: "empty_catalog_type",
			config: &config.Config{
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "",
						URI:  "http://localhost:8181",
					},
				},
			},
			expectError: true,
		},
		{
			name: "empty_catalog_uri",
			config: &config.Config{
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "rest",
						URI:  "",
					},
				},
			},
			expectError: true,
		},
		{
			name: "unsupported_catalog_type",
			config: &config.Config{
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "unsupported",
						URI:  "http://localhost:8181",
					},
				},
			},
			expectError: true,
		},
		{
			name: "valid_rest_catalog",
			config: &config.Config{
				ObjectStorage: config.ObjectStorageConfig{
					Provider:        "s3",
					Endpoint:        "http://localhost:9000",
					AccessKeyID:     "minioadmin",
					SecretAccessKey: "minioadmin",
					Bucket:          "test-bucket",
					Region:          "us-east-1",
					UseSSL:          false,
				},
				Storage: config.StorageConfig{
					Type:   "s3",
					Bucket: "test-bucket",
					Region: "us-east-1",
				},
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "rest",
						URI:  "http://localhost:8181",
						Properties: map[string]string{
							"warehouse": "s3://test-bucket/warehouse",
						},
					},
				},
			},
			expectError: false, // May skip due to external dependencies
		},
		{
			name: "valid_glue_catalog",
			config: &config.Config{
				ObjectStorage: config.ObjectStorageConfig{
					Provider: "s3",
					Region:   "us-east-1",
				},
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "glue",
						URI:  "glue://us-east-1",
						Properties: map[string]string{
							"warehouse": "s3://test-bucket/warehouse",
						},
					},
				},
			},
			expectError: false, // May skip due to external dependencies
		},
		{
			name: "valid_sql_catalog",
			config: &config.Config{
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "sql",
						URI:  "postgresql://localhost:5432/iceberg",
						Properties: map[string]string{
							"warehouse": "s3://test-bucket/warehouse",
						},
					},
				},
			},
			expectError: false, // May skip due to external dependencies
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var logger *zap.Logger
			if tt.name != "nil_logger_should_not_panic" {
				logger = zaptest.NewLogger(t)
			}

			client, err := NewClient(tt.config, logger)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, client)
				return
			}

			// For valid configs, we expect either success or skip due to external dependencies
			if err != nil {
				t.Skipf("Skipping test due to external dependency: %v", err)
				return
			}

			assert.NotNil(t, client)
			assert.Equal(t, tt.config, client.config)
			assert.NotNil(t, client.catalog)
			assert.NotNil(t, client.tableManager)
			assert.NotNil(t, client.queryEngine)
			assert.NotNil(t, client.analytics)
			assert.False(t, client.running) // Should start as not running
		})
	}
}

func TestClient_StartStop(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
	logger := zaptest.NewLogger(t)

	// Skip this test if we can't create a client (no real catalog available)
	client, err := NewClient(cfg, logger)
	if err != nil {
		t.Skip("Skipping test - no real catalog available")
		return
	}

	// Test start
	err = client.Start()
	if err != nil {
		t.Skip("Skipping test - cannot start client without real services")
		return
	}
	assert.True(t, client.running)

	// Test start when already running
	err = client.Start()
	assert.Error(t, err)

	// Test stop
	err = client.Stop()
	assert.NoError(t, err)
	assert.False(t, client.running)

	// Test stop when not running
	err = client.Stop()
	assert.NoError(t, err)
}

func TestClient_CreateTable(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
	logger := zaptest.NewLogger(t)

	client, err := NewClient(cfg, logger)
	if err != nil {
		t.Skip("Skipping test - no real catalog available")
		return
	}

	err = client.Start()
	if err != nil {
		t.Skip("Skipping test - cannot start client without real services")
		return
	}
	defer client.Stop()

	ctx := context.Background()

	// Test create table request validation
	_, err = client.CreateTable(ctx, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "request is required")

	// Test valid create table request
	request := &CreateTableRequest{
		Namespace:  "test",
		TableName:  "test_table",
		Schema:     nil, // Simplified for testing
		Properties: map[string]string{"format-version": "2"},
	}

	result, err := client.CreateTable(ctx, request)
	// May error due to nil schema, but we're testing the validation path
	if err != nil {
		assert.Contains(t, err.Error(), "schema")
	} else {
		assert.NotNil(t, result)
		assert.Equal(t, "test", result.Namespace)
		assert.Equal(t, "test_table", result.Name)
	}
}

func TestClient_WriteData(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Enabled: true,
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
			},
		},
	}
	logger := zaptest.NewLogger(t)

	client, err := NewClient(cfg, logger)
	if err != nil {
		t.Skip("Skipping test - no real catalog available")
		return
	}

	err = client.Start()
	if err != nil {
		t.Skip("Skipping test - cannot start client without real services")
		return
	}
	defer client.Stop()

	ctx := context.Background()

	// Test write data request validation
	_, err = client.WriteData(ctx, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "request is required")

	// Test valid write data request
	records := []map[string]interface{}{
		{"id": 1, "name": "Alice", "created_at": "2023-01-01T00:00:00Z"},
		{"id": 2, "name": "Bob", "created_at": "2023-01-02T00:00:00Z"},
	}

	request := &WriteDataRequest{
		Namespace: "test",
		TableName: "test_table",
		Records:   records,
		WriteMode: WriteModeAppend,
	}

	result, err := client.WriteData(ctx, request)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(2), result.RecordsWritten)
	assert.GreaterOrEqual(t, result.BytesWritten, int64(0))
}

func TestClient_ReadData(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Enabled: true,
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
			},
		},
	}
	logger := zaptest.NewLogger(t)

	client, err := NewClient(cfg, logger)
	if err != nil {
		t.Skip("Skipping test - no real catalog available")
		return
	}

	err = client.Start()
	if err != nil {
		t.Skip("Skipping test - cannot start client without real services")
		return
	}
	defer client.Stop()

	ctx := context.Background()

	// Test read data request validation
	_, err = client.ReadData(ctx, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "request is required")

	// Test valid read data request
	limit := int64(10)
	request := &ReadDataRequest{
		Namespace:  "test",
		TableName:  "test_table",
		Filter:     "id > 0",
		Limit:      &limit,
		Projection: []string{"id", "name"},
	}

	result, err := client.ReadData(ctx, request)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.NotNil(t, result.Records)
	assert.GreaterOrEqual(t, result.RecordsRead, int64(0))
}

func TestClient_ExecuteQuery(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Enabled: true,
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
			},
		},
	}
	logger := zaptest.NewLogger(t)

	client, err := NewClient(cfg, logger)
	if err != nil {
		t.Skip("Skipping test - no real catalog available")
		return
	}

	err = client.Start()
	if err != nil {
		t.Skip("Skipping test - cannot start client without real services")
		return
	}
	defer client.Stop()

	ctx := context.Background()

	// Test query request validation
	_, err = client.ExecuteQuery(ctx, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "request is required")

	// Test valid query request
	request := &QueryRequest{
		SQL: "SELECT * FROM test.table",
	}

	result, err := client.ExecuteQuery(ctx, request)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.NotNil(t, result.Columns)
	assert.NotNil(t, result.Rows)
	assert.GreaterOrEqual(t, result.RowCount, int64(0))
}

func TestClient_RunAnalytics(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Enabled: true,
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
			},
		},
	}
	logger := zaptest.NewLogger(t)

	client, err := NewClient(cfg, logger)
	if err != nil {
		t.Skip("Skipping test - no real catalog available")
		return
	}

	err = client.Start()
	if err != nil {
		t.Skip("Skipping test - cannot start client without real services")
		return
	}
	defer client.Stop()

	ctx := context.Background()

	// Test analytics request validation
	_, err = client.RunAnalytics(ctx, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "request is required")

	// Test valid analytics request
	request := &AnalyticsRequest{
		JobType: "aggregation",
		Tables:  []string{"test.table"},
		Parameters: map[string]interface{}{
			"query": "SELECT COUNT(*) FROM test.table",
		},
	}

	result, err := client.RunAnalytics(ctx, request)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.NotEmpty(t, result.JobID)
}

func TestClient_Health(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Enabled: true,
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
			},
		},
	}
	logger := zaptest.NewLogger(t)

	client, err := NewClient(cfg, logger)
	if err != nil {
		t.Skip("Skipping test - no real catalog available")
		return
	}

	err = client.Start()
	if err != nil {
		t.Skip("Skipping test - cannot start client without real services")
		return
	}
	defer client.Stop()

	ctx := context.Background()

	// Test health check
	err = client.Health(ctx)
	assert.NoError(t, err)
}

func TestClient_ListTables(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Enabled: true,
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
			},
		},
	}
	logger := zaptest.NewLogger(t)

	client, err := NewClient(cfg, logger)
	if err != nil {
		t.Skip("Skipping test - no real catalog available")
		return
	}

	err = client.Start()
	if err != nil {
		t.Skip("Skipping test - cannot start client without real services")
		return
	}
	defer client.Stop()

	ctx := context.Background()

	// Test list tables
	tables, err := client.ListTables(ctx, "test")
	assert.NoError(t, err)
	assert.NotNil(t, tables)
}

func TestClient_GetTable(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Enabled: true,
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
			},
		},
	}
	logger := zaptest.NewLogger(t)

	client, err := NewClient(cfg, logger)
	if err != nil {
		t.Skip("Skipping test - no real catalog available")
		return
	}

	err = client.Start()
	if err != nil {
		t.Skip("Skipping test - cannot start client without real services")
		return
	}
	defer client.Stop()

	ctx := context.Background()

	// Test get table
	table, err := client.GetTable(ctx, "test", "test_table")
	if err != nil {
		// Expected to fail if table doesn't exist
		assert.Contains(t, err.Error(), "not found")
	} else {
		assert.NotNil(t, table)
		assert.Equal(t, "test", table.Namespace)
		assert.Equal(t, "test_table", table.Name)
	}
}

func TestClient_DropTable(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Enabled: true,
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
			},
		},
	}
	logger := zaptest.NewLogger(t)

	client, err := NewClient(cfg, logger)
	if err != nil {
		t.Skip("Skipping test - no real catalog available")
		return
	}

	err = client.Start()
	if err != nil {
		t.Skip("Skipping test - cannot start client without real services")
		return
	}
	defer client.Stop()

	ctx := context.Background()

	// Test drop table
	err = client.DropTable(ctx, "test", "test_table")
	if err != nil {
		// Expected to fail if table doesn't exist
		assert.Contains(t, err.Error(), "not found")
	} else {
		// Success case
		assert.NoError(t, err)
	}
}

// Test client business logic methods that don't require external dependencies
func TestClient_GetMetrics(t *testing.T) {
	// Create a client with valid configuration
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)

	// Create client components manually to avoid external dependencies
	storageClient := NewMockStorageClient()
	catalog := &Catalog{
		config:  cfg,
		logger:  logger,
		storage: storageClient,
	}
	tableManager := &TableManager{
		config:  cfg,
		logger:  logger,
		catalog: catalog,
		storage: storageClient,
	}
	queryEngine := &QueryEngine{
		config:       cfg,
		logger:       logger,
		catalog:      catalog,
		tableManager: tableManager,
	}
	analyticsEngine := &AnalyticsEngine{
		config:       cfg,
		logger:       logger,
		queryEngine:  queryEngine,
		tableManager: tableManager,
	}

	client := &Client{
		config:       cfg,
		logger:       logger,
		storage:      storageClient,
		catalog:      catalog,
		tableManager: tableManager,
		queryEngine:  queryEngine,
		analytics:    analyticsEngine,
		metrics: &ClientMetrics{
			TablesCreated:    5,
			TablesDropped:    2,
			RecordsWritten:   1000,
			RecordsRead:      500,
			QueriesExecuted:  10,
			AnalyticsJobs:    3,
			ErrorCount:       1,
			LastActivity:     time.Now(),
			ProcessingRate:   100.5,
			AverageLatency:   25.3,
			StorageUtilized:  1024000,
			CompressionRatio: 0.7,
		},
	}

	// Test GetMetrics
	metrics := client.GetMetrics()
	assert.NotNil(t, metrics)
	assert.Equal(t, int64(5), metrics.TablesCreated)
	assert.Equal(t, int64(2), metrics.TablesDropped)
	assert.Equal(t, int64(1000), metrics.RecordsWritten)
	assert.Equal(t, int64(500), metrics.RecordsRead)
	assert.Equal(t, int64(10), metrics.QueriesExecuted)
	assert.Equal(t, int64(3), metrics.AnalyticsJobs)
	assert.Equal(t, int64(1), metrics.ErrorCount)
	assert.Equal(t, 100.5, metrics.ProcessingRate)
	assert.Equal(t, 25.3, metrics.AverageLatency)
	assert.Equal(t, int64(1024000), metrics.StorageUtilized)
	assert.Equal(t, 0.7, metrics.CompressionRatio)
}

func TestClient_UpdateMetrics(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)

	client := &Client{
		config: cfg,
		logger: logger,
		metrics: &ClientMetrics{
			RecordsWritten: 100,
			RecordsRead:    50,
			LastActivity:   time.Now().Add(-30 * time.Second), // 30 seconds ago
		},
	}

	// Test updateMetrics method
	client.updateMetrics()

	// Verify metrics were updated
	assert.NotZero(t, client.metrics.ProcessingRate)
	assert.Equal(t, 0.7, client.metrics.CompressionRatio) // Default compression ratio
}

func TestClient_RequestValidation(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)
	ctx := context.Background()

	client := &Client{
		config:  cfg,
		logger:  logger,
		metrics: &ClientMetrics{},
	}

	// Test CreateTable with nil request
	table, err := client.CreateTable(ctx, nil)
	assert.Error(t, err)
	assert.Nil(t, table)
	assert.Contains(t, err.Error(), "create table request is required")

	// Test WriteData with nil request
	result, err := client.WriteData(ctx, nil)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "write data request is required")

	// Test ReadData with nil request
	readResult, err := client.ReadData(ctx, nil)
	assert.Error(t, err)
	assert.Nil(t, readResult)
	assert.Contains(t, err.Error(), "read data request is required")

	// Test ExecuteQuery with nil request
	queryResult, err := client.ExecuteQuery(ctx, nil)
	assert.Error(t, err)
	assert.Nil(t, queryResult)
	assert.Contains(t, err.Error(), "query request is required")

	// Test RunAnalytics with nil request
	analyticsResult, err := client.RunAnalytics(ctx, nil)
	assert.Error(t, err)
	assert.Nil(t, analyticsResult)
	assert.Contains(t, err.Error(), "analytics request is required")
}

func TestClient_RunningStateValidation(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)
	ctx := context.Background()

	client := &Client{
		config:  cfg,
		logger:  logger,
		running: false, // Not running
		metrics: &ClientMetrics{},
	}

	// Test operations that require running state
	err := client.CreateNamespace(ctx, "test", nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "client is not running")

	namespaces, err := client.ListNamespaces(ctx)
	assert.Error(t, err)
	assert.Nil(t, namespaces)
	assert.Contains(t, err.Error(), "client is not running")

	err = client.DropNamespace(ctx, "test")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "client is not running")
}

func TestClient_MetricsUpdating(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)

	client := &Client{
		config: cfg,
		logger: logger,
		metrics: &ClientMetrics{
			RecordsWritten: 1000,
			RecordsRead:    500,
			ErrorCount:     5,
			LastActivity:   time.Now().Add(-30 * time.Second), // 30 seconds ago (within 1 minute)
		},
	}

	// Test metrics calculation
	client.updateMetrics()

	// Verify processing rate calculation (should be > 0 due to records processed over time)
	assert.Greater(t, client.metrics.ProcessingRate, 0.0)

	// Verify compression ratio is set
	assert.Equal(t, 0.7, client.metrics.CompressionRatio)

	// Test with older activity (should not update processing rate)
	client.metrics.LastActivity = time.Now().Add(-2 * time.Minute)
	client.metrics.ProcessingRate = 0 // Reset
	client.updateMetrics()
	assert.Equal(t, 0.0, client.metrics.ProcessingRate) // Should remain 0 for old activity
}

func TestClient_EdgeCases(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)
	ctx := context.Background()

	// Create a mock catalog
	mockCatalog := &Catalog{
		config:     cfg,
		logger:     logger,
		namespaces: make(map[string]*CatalogNamespace),
		tables:     make(map[string]*Table),
		running:    false,
	}

	// Create a mock table manager to avoid nil pointer
	mockTableManager := &TableManager{
		config:  cfg,
		logger:  logger,
		catalog: mockCatalog,
		running: false,
	}

	client := &Client{
		config:       cfg,
		logger:       logger,
		metrics:      &ClientMetrics{},
		catalog:      mockCatalog,
		tableManager: mockTableManager,
	}

	// Test with empty namespace
	err := client.CreateNamespace(ctx, "", nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "client is not running")

	// Test with nil properties
	err = client.CreateNamespace(ctx, "test", nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "client is not running")

	// Test DropTable with empty parameters
	err = client.DropTable(ctx, "", "")
	assert.Error(t, err) // Should fail due to table manager not running

	// Test GetTable with empty parameters
	table, err := client.GetTable(ctx, "", "")
	assert.Error(t, err) // Should fail due to external dependency
	assert.Nil(t, table)
}

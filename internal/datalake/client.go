package datalake

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/storage"
)

// Client provides data lake integration with Iceberg table support
type Client struct {
	config       *config.Config
	logger       *zap.Logger
	storage      storage.Client
	catalog      *Catalog
	tableManager *TableManager
	queryEngine  *QueryEngine
	analytics    *AnalyticsEngine
	running      bool
	ctx          context.Context
	cancel       context.CancelFunc
	wg           sync.WaitGroup
	metrics      *ClientMetrics
}

// ClientMetrics tracks data lake client performance
type ClientMetrics struct {
	TablesCreated    int64     `json:"tables_created"`
	TablesDropped    int64     `json:"tables_dropped"`
	RecordsWritten   int64     `json:"records_written"`
	RecordsRead      int64     `json:"records_read"`
	QueriesExecuted  int64     `json:"queries_executed"`
	AnalyticsJobs    int64     `json:"analytics_jobs"`
	ErrorCount       int64     `json:"error_count"`
	LastActivity     time.Time `json:"last_activity"`
	ProcessingRate   float64   `json:"processing_rate"`
	AverageLatency   float64   `json:"average_latency"`
	StorageUtilized  int64     `json:"storage_utilized"`
	CompressionRatio float64   `json:"compression_ratio"`
}

// NewClient creates a new data lake client
func NewClient(cfg *config.Config, logger *zap.Logger) (*Client, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	// Create storage client
	storageClient, err := storage.NewClient(cfg.ObjectStorage, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create storage client: %w", err)
	}

	// Create catalog
	catalog, err := NewCatalog(cfg, logger, storageClient)
	if err != nil {
		return nil, fmt.Errorf("failed to create catalog: %w", err)
	}

	// Create table manager
	tableManager, err := NewTableManager(cfg, logger, catalog, storageClient)
	if err != nil {
		return nil, fmt.Errorf("failed to create table manager: %w", err)
	}

	// Create query engine
	queryEngine, err := NewQueryEngine(cfg, logger, catalog, tableManager)
	if err != nil {
		return nil, fmt.Errorf("failed to create query engine: %w", err)
	}

	// Create analytics engine
	analytics, err := NewAnalyticsEngine(cfg, logger, queryEngine, tableManager)
	if err != nil {
		return nil, fmt.Errorf("failed to create analytics engine: %w", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	client := &Client{
		config:       cfg,
		logger:       logger,
		storage:      storageClient,
		catalog:      catalog,
		tableManager: tableManager,
		queryEngine:  queryEngine,
		analytics:    analytics,
		ctx:          ctx,
		cancel:       cancel,
		metrics: &ClientMetrics{
			LastActivity: time.Now(),
		},
	}

	return client, nil
}

// Start starts the data lake client
func (c *Client) Start() error {
	if c.running {
		return fmt.Errorf("client is already running")
	}

	c.logger.Info("Starting data lake client")

	// Start catalog
	if err := c.catalog.Start(); err != nil {
		return fmt.Errorf("failed to start catalog: %w", err)
	}

	// Start table manager
	if err := c.tableManager.Start(); err != nil {
		return fmt.Errorf("failed to start table manager: %w", err)
	}

	// Start query engine
	if err := c.queryEngine.Start(); err != nil {
		return fmt.Errorf("failed to start query engine: %w", err)
	}

	// Start analytics engine
	if err := c.analytics.Start(); err != nil {
		return fmt.Errorf("failed to start analytics engine: %w", err)
	}

	// Start background workers
	c.wg.Add(1)
	go c.metricsWorker()

	c.running = true
	c.logger.Info("Data lake client started successfully")

	return nil
}

// Stop stops the data lake client
func (c *Client) Stop() error {
	if !c.running {
		return nil
	}

	c.logger.Info("Stopping data lake client")

	// Cancel context
	c.cancel()

	// Stop components
	if err := c.analytics.Stop(); err != nil {
		c.logger.Error("Failed to stop analytics engine", zap.Error(err))
	}

	if err := c.queryEngine.Stop(); err != nil {
		c.logger.Error("Failed to stop query engine", zap.Error(err))
	}

	if err := c.tableManager.Stop(); err != nil {
		c.logger.Error("Failed to stop table manager", zap.Error(err))
	}

	if err := c.catalog.Stop(); err != nil {
		c.logger.Error("Failed to stop catalog", zap.Error(err))
	}

	// Wait for workers to finish
	c.wg.Wait()

	c.running = false
	c.logger.Info("Data lake client stopped")

	return nil
}

// CreateTable creates a new Iceberg table
func (c *Client) CreateTable(ctx context.Context, request *CreateTableRequest) (*Table, error) {
	if request == nil {
		return nil, fmt.Errorf("create table request is required")
	}

	c.logger.Info("Creating table",
		zap.String("namespace", request.Namespace),
		zap.String("table_name", request.TableName))

	table, err := c.tableManager.CreateTable(ctx, request)
	if err != nil {
		c.metrics.ErrorCount++
		return nil, fmt.Errorf("failed to create table: %w", err)
	}

	c.metrics.TablesCreated++
	c.metrics.LastActivity = time.Now()

	c.logger.Info("Table created successfully",
		zap.String("table_id", table.ID),
		zap.String("namespace", table.Namespace),
		zap.String("name", table.Name))

	return table, nil
}

// DropTable drops an Iceberg table
func (c *Client) DropTable(ctx context.Context, namespace, tableName string) error {
	c.logger.Info("Dropping table",
		zap.String("namespace", namespace),
		zap.String("table_name", tableName))

	err := c.tableManager.DropTable(ctx, namespace, tableName)
	if err != nil {
		c.metrics.ErrorCount++
		return fmt.Errorf("failed to drop table: %w", err)
	}

	c.metrics.TablesDropped++
	c.metrics.LastActivity = time.Now()

	c.logger.Info("Table dropped successfully",
		zap.String("namespace", namespace),
		zap.String("table_name", tableName))

	return nil
}

// WriteData writes data to an Iceberg table
func (c *Client) WriteData(ctx context.Context, request *WriteDataRequest) (*WriteResult, error) {
	if request == nil {
		return nil, fmt.Errorf("write data request is required")
	}

	c.logger.Debug("Writing data to table",
		zap.String("namespace", request.Namespace),
		zap.String("table_name", request.TableName),
		zap.Int("record_count", len(request.Records)))

	result, err := c.tableManager.WriteData(ctx, request)
	if err != nil {
		c.metrics.ErrorCount++
		return nil, fmt.Errorf("failed to write data: %w", err)
	}

	c.metrics.RecordsWritten += int64(len(request.Records))
	c.metrics.LastActivity = time.Now()

	return result, nil
}

// ReadData reads data from an Iceberg table
func (c *Client) ReadData(ctx context.Context, request *ReadDataRequest) (*ReadResult, error) {
	if request == nil {
		return nil, fmt.Errorf("read data request is required")
	}

	c.logger.Debug("Reading data from table",
		zap.String("namespace", request.Namespace),
		zap.String("table_name", request.TableName))

	result, err := c.tableManager.ReadData(ctx, request)
	if err != nil {
		c.metrics.ErrorCount++
		return nil, fmt.Errorf("failed to read data: %w", err)
	}

	c.metrics.RecordsRead += int64(len(result.Records))
	c.metrics.LastActivity = time.Now()

	return result, nil
}

// ExecuteQuery executes a SQL query against the data lake
func (c *Client) ExecuteQuery(ctx context.Context, request *QueryRequest) (*QueryResult, error) {
	if request == nil {
		return nil, fmt.Errorf("query request is required")
	}

	c.logger.Debug("Executing query", zap.String("query", request.SQL))

	result, err := c.queryEngine.ExecuteQuery(ctx, request)
	if err != nil {
		c.metrics.ErrorCount++
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}

	c.metrics.QueriesExecuted++
	c.metrics.LastActivity = time.Now()

	return result, nil
}

// RunAnalytics runs analytics job on the data lake
func (c *Client) RunAnalytics(ctx context.Context, request *AnalyticsRequest) (*AnalyticsResult, error) {
	if request == nil {
		return nil, fmt.Errorf("analytics request is required")
	}

	c.logger.Info("Running analytics job", zap.String("job_type", request.JobType))

	result, err := c.analytics.RunAnalytics(ctx, request)
	if err != nil {
		c.metrics.ErrorCount++
		return nil, fmt.Errorf("failed to run analytics: %w", err)
	}

	c.metrics.AnalyticsJobs++
	c.metrics.LastActivity = time.Now()

	return result, nil
}

// ListTables lists all tables in a namespace
func (c *Client) ListTables(ctx context.Context, namespace string) ([]*Table, error) {
	return c.catalog.ListTables(ctx, namespace)
}

// GetTable gets table metadata
func (c *Client) GetTable(ctx context.Context, namespace, tableName string) (*Table, error) {
	return c.catalog.GetTable(ctx, namespace, tableName)
}

// GetMetrics returns client metrics
func (c *Client) GetMetrics() *ClientMetrics {
	return c.metrics
}

// Health checks the health of the data lake client
func (c *Client) Health(ctx context.Context) error {
	// Check storage health
	if err := c.storage.Health(ctx); err != nil {
		return fmt.Errorf("storage health check failed: %w", err)
	}

	// Check catalog health
	if err := c.catalog.Health(ctx); err != nil {
		return fmt.Errorf("catalog health check failed: %w", err)
	}

	return nil
}

// metricsWorker runs background metrics collection
func (c *Client) metricsWorker() {
	defer c.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.updateMetrics()
		}
	}
}

// updateMetrics updates performance metrics
func (c *Client) updateMetrics() {
	// Calculate processing rate
	if c.metrics.LastActivity.After(time.Now().Add(-time.Minute)) {
		c.metrics.ProcessingRate = float64(c.metrics.RecordsWritten+c.metrics.RecordsRead) / time.Since(c.metrics.LastActivity).Minutes()
	}

	// Update storage utilization
	// This would typically query the storage backend for usage statistics
	// For now, we'll use a placeholder
	c.metrics.StorageUtilized = c.metrics.RecordsWritten * 1024 // Approximate bytes

	// Update compression ratio (placeholder)
	c.metrics.CompressionRatio = 0.7 // Typical Parquet compression ratio
}

// CreateNamespace creates a new namespace
func (c *Client) CreateNamespace(ctx context.Context, name string, properties map[string]string) error {
	if !c.running {
		return fmt.Errorf("client is not running")
	}

	return c.catalog.CreateNamespace(ctx, name, properties)
}

// ListNamespaces lists all namespaces
func (c *Client) ListNamespaces(ctx context.Context) ([]*CatalogNamespace, error) {
	if !c.running {
		return nil, fmt.Errorf("client is not running")
	}

	return c.catalog.ListNamespaces(ctx)
}

// DropNamespace drops a namespace
func (c *Client) DropNamespace(ctx context.Context, name string) error {
	if !c.running {
		return fmt.Errorf("client is not running")
	}

	return c.catalog.DropNamespace(ctx, name)
}

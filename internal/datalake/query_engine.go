package datalake

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// QueryEngine provides SQL query execution capabilities
type QueryEngine struct {
	config       *config.Config
	logger       *zap.Logger
	catalog      *Catalog
	tableManager *TableManager
	running      bool
	ctx          context.Context
	cancel       context.CancelFunc
	wg           sync.WaitGroup
	mu           sync.RWMutex
	queryCache   map[string]*CachedQuery
}

// CachedQuery represents a cached query result
type CachedQuery struct {
	SQL       string
	Result    *QueryResult
	Timestamp time.Time
	TTL       time.Duration
}

// NewQueryEngine creates a new query engine
func NewQueryEngine(cfg *config.Config, logger *zap.Logger, catalog *Catalog, tableManager *TableManager) (*QueryEngine, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}
	if catalog == nil {
		return nil, fmt.Errorf("catalog is required")
	}
	if tableManager == nil {
		return nil, fmt.Errorf("table manager is required")
	}

	ctx, cancel := context.WithCancel(context.Background())

	qe := &QueryEngine{
		config:       cfg,
		logger:       logger,
		catalog:      catalog,
		tableManager: tableManager,
		ctx:          ctx,
		cancel:       cancel,
		queryCache:   make(map[string]*CachedQuery),
	}

	return qe, nil
}

// Start starts the query engine
func (qe *QueryEngine) Start() error {
	qe.mu.Lock()
	defer qe.mu.Unlock()

	if qe.running {
		return fmt.Errorf("query engine is already running")
	}

	qe.logger.Info("Starting query engine")

	// Start background workers
	qe.wg.Add(1)
	go qe.cacheCleanupWorker()

	qe.running = true
	qe.logger.Info("Query engine started successfully")

	return nil
}

// Stop stops the query engine
func (qe *QueryEngine) Stop() error {
	qe.mu.Lock()
	defer qe.mu.Unlock()

	if !qe.running {
		return nil
	}

	qe.logger.Info("Stopping query engine")

	// Cancel context
	qe.cancel()

	// Wait for workers to finish
	qe.wg.Wait()

	qe.running = false
	qe.logger.Info("Query engine stopped")

	return nil
}

// ExecuteQuery executes a SQL query
func (qe *QueryEngine) ExecuteQuery(ctx context.Context, request *QueryRequest) (*QueryResult, error) {
	if request == nil {
		return nil, fmt.Errorf("query request is required")
	}

	if strings.TrimSpace(request.SQL) == "" {
		return nil, fmt.Errorf("SQL query is required")
	}

	// Check if engine is running
	qe.mu.RLock()
	if !qe.running {
		qe.mu.RUnlock()
		return nil, fmt.Errorf("query engine is not running")
	}
	qe.mu.RUnlock()

	startTime := time.Now()

	qe.logger.Debug("Executing query", zap.String("sql", request.SQL))

	// Check cache first
	if cachedResult := qe.getCachedQuery(request.SQL); cachedResult != nil {
		qe.logger.Debug("Returning cached query result")
		return cachedResult, nil
	}

	// Parse and validate the query
	queryInfo, err := qe.parseQuery(request.SQL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse query: %w", err)
	}

	// Execute the query based on its type
	var result *QueryResult
	switch queryInfo.Type {
	case "SELECT":
		result, err = qe.executeSelectQuery(ctx, queryInfo, request)
	case "SHOW":
		result, err = qe.executeShowQuery(ctx, queryInfo, request)
	case "DESCRIBE":
		result, err = qe.executeDescribeQuery(ctx, queryInfo, request)
	default:
		return nil, fmt.Errorf("unsupported query type: %s", queryInfo.Type)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}

	result.Duration = time.Since(startTime)

	// Cache the result
	qe.cacheQuery(request.SQL, result)

	qe.logger.Debug("Query executed successfully",
		zap.String("type", queryInfo.Type),
		zap.Int64("row_count", result.RowCount),
		zap.Duration("duration", result.Duration))

	return result, nil
}

// QueryInfo represents parsed query information
type QueryInfo struct {
	Type    string
	Tables  []string
	Columns []string
	Where   string
	OrderBy string
	Limit   *int64
	Raw     string
}

// parseQuery parses a SQL query and extracts information
func (qe *QueryEngine) parseQuery(sql string) (*QueryInfo, error) {
	sql = strings.TrimSpace(sql)
	upperSQL := strings.ToUpper(sql)

	info := &QueryInfo{
		Raw: sql,
	}

	// Determine query type
	if strings.HasPrefix(upperSQL, "SELECT") {
		info.Type = "SELECT"
		return qe.parseSelectQuery(sql, info)
	} else if strings.HasPrefix(upperSQL, "SHOW") {
		info.Type = "SHOW"
		return qe.parseShowQuery(sql, info)
	} else if strings.HasPrefix(upperSQL, "DESCRIBE") || strings.HasPrefix(upperSQL, "DESC") {
		info.Type = "DESCRIBE"
		return qe.parseDescribeQuery(sql, info)
	}

	return nil, fmt.Errorf("unsupported query type")
}

// parseSelectQuery parses a SELECT query
func (qe *QueryEngine) parseSelectQuery(sql string, info *QueryInfo) (*QueryInfo, error) {
	// This is a simplified parser - in a real implementation, you would use a proper SQL parser
	upperSQL := strings.ToUpper(sql)

	// Extract table names (simplified)
	if fromIndex := strings.Index(upperSQL, "FROM"); fromIndex != -1 {
		remaining := sql[fromIndex+4:]
		parts := strings.Fields(remaining)
		if len(parts) > 0 {
			tableName := strings.TrimSpace(parts[0])
			// Remove any trailing clauses
			if whereIndex := strings.Index(strings.ToUpper(tableName), "WHERE"); whereIndex != -1 {
				tableName = tableName[:whereIndex]
			}
			info.Tables = []string{strings.TrimSpace(tableName)}
		}
	}

	// Extract WHERE clause (simplified)
	if whereIndex := strings.Index(upperSQL, "WHERE"); whereIndex != -1 {
		remaining := sql[whereIndex+5:]
		// Find the end of WHERE clause
		endIndex := len(remaining)
		for _, keyword := range []string{"ORDER BY", "GROUP BY", "HAVING", "LIMIT"} {
			if idx := strings.Index(strings.ToUpper(remaining), keyword); idx != -1 && idx < endIndex {
				endIndex = idx
			}
		}
		info.Where = strings.TrimSpace(remaining[:endIndex])
	}

	return info, nil
}

// parseShowQuery parses a SHOW query
func (qe *QueryEngine) parseShowQuery(sql string, info *QueryInfo) (*QueryInfo, error) {
	// Handle SHOW TABLES, SHOW NAMESPACES, etc.
	return info, nil
}

// parseDescribeQuery parses a DESCRIBE query
func (qe *QueryEngine) parseDescribeQuery(sql string, info *QueryInfo) (*QueryInfo, error) {
	// Extract table name from DESCRIBE statement
	parts := strings.Fields(sql)
	if len(parts) >= 2 {
		info.Tables = []string{parts[1]}
	}
	return info, nil
}

// executeSelectQuery executes a SELECT query
func (qe *QueryEngine) executeSelectQuery(ctx context.Context, queryInfo *QueryInfo, request *QueryRequest) (*QueryResult, error) {
	if len(queryInfo.Tables) == 0 {
		return nil, fmt.Errorf("no tables specified in query")
	}

	tableName := queryInfo.Tables[0]

	// Parse namespace and table name
	parts := strings.Split(tableName, ".")
	var namespace, name string
	if len(parts) == 2 {
		namespace = parts[0]
		name = parts[1]
	} else {
		namespace = "default"
		name = tableName
	}

	// Create read request
	readRequest := &ReadDataRequest{
		Namespace: namespace,
		TableName: name,
		Filter:    queryInfo.Where,
	}

	// Execute read
	readResult, err := qe.tableManager.ReadData(ctx, readRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to read data: %w", err)
	}

	// Convert to query result format
	var columns []string
	var rows [][]interface{}

	if len(readResult.Records) > 0 {
		// Extract columns from first record
		for key := range readResult.Records[0] {
			columns = append(columns, key)
		}

		// Convert records to rows
		for _, record := range readResult.Records {
			row := make([]interface{}, len(columns))
			for i, col := range columns {
				row[i] = record[col]
			}
			rows = append(rows, row)
		}
	}

	result := &QueryResult{
		Columns:  columns,
		Rows:     rows,
		RowCount: int64(len(rows)),
		Metadata: map[string]interface{}{
			"table":     tableName,
			"namespace": namespace,
		},
	}

	return result, nil
}

// executeShowQuery executes a SHOW query
func (qe *QueryEngine) executeShowQuery(ctx context.Context, queryInfo *QueryInfo, request *QueryRequest) (*QueryResult, error) {
	upperSQL := strings.ToUpper(queryInfo.Raw)

	if strings.Contains(upperSQL, "TABLES") {
		// SHOW TABLES
		namespace := "default" // Default namespace

		// Extract namespace if specified
		if strings.Contains(upperSQL, "IN") || strings.Contains(upperSQL, "FROM") {
			// Parse namespace from query
			// This is simplified - in practice, you'd need proper parsing
		}

		tables, err := qe.catalog.ListTables(ctx, namespace)
		if err != nil {
			return nil, fmt.Errorf("failed to list tables: %w", err)
		}

		columns := []string{"table_name"}
		rows := make([][]interface{}, len(tables))
		for i, table := range tables {
			rows[i] = []interface{}{table.Name}
		}

		return &QueryResult{
			Columns:  columns,
			Rows:     rows,
			RowCount: int64(len(rows)),
		}, nil

	} else if strings.Contains(upperSQL, "NAMESPACES") || strings.Contains(upperSQL, "SCHEMAS") {
		// SHOW NAMESPACES
		namespaces, err := qe.catalog.ListNamespaces(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to list namespaces: %w", err)
		}

		columns := []string{"namespace"}
		rows := make([][]interface{}, len(namespaces))
		for i, ns := range namespaces {
			rows[i] = []interface{}{ns.Name}
		}

		return &QueryResult{
			Columns:  columns,
			Rows:     rows,
			RowCount: int64(len(rows)),
		}, nil
	}

	return nil, fmt.Errorf("unsupported SHOW query")
}

// executeDescribeQuery executes a DESCRIBE query
func (qe *QueryEngine) executeDescribeQuery(ctx context.Context, queryInfo *QueryInfo, request *QueryRequest) (*QueryResult, error) {
	if len(queryInfo.Tables) == 0 {
		return nil, fmt.Errorf("no table specified in DESCRIBE query")
	}

	tableName := queryInfo.Tables[0]

	// Parse namespace and table name
	parts := strings.Split(tableName, ".")
	var namespace, name string
	if len(parts) == 2 {
		namespace = parts[0]
		name = parts[1]
	} else {
		namespace = "default"
		name = tableName
	}

	// Get table metadata
	table, err := qe.catalog.GetTable(ctx, namespace, name)
	if err != nil {
		return nil, fmt.Errorf("failed to get table: %w", err)
	}

	// Extract schema information
	columns := []string{"column_name", "data_type", "nullable"}
	rows := make([][]interface{}, 0)

	if table.Schema != nil {
		for _, field := range table.Schema.Fields() {
			rows = append(rows, []interface{}{
				field.Name,
				field.Type.String(),
				!field.Required,
			})
		}
	}

	return &QueryResult{
		Columns:  columns,
		Rows:     rows,
		RowCount: int64(len(rows)),
		Metadata: map[string]interface{}{
			"table":     tableName,
			"namespace": namespace,
		},
	}, nil
}

// getCachedQuery retrieves a cached query result
func (qe *QueryEngine) getCachedQuery(sql string) *QueryResult {
	qe.mu.RLock()
	defer qe.mu.RUnlock()

	cached, exists := qe.queryCache[sql]
	if !exists {
		return nil
	}

	// Check if cache is still valid
	if time.Since(cached.Timestamp) > cached.TTL {
		return nil
	}

	return cached.Result
}

// cacheQuery caches a query result
func (qe *QueryEngine) cacheQuery(sql string, result *QueryResult) {
	qe.mu.Lock()
	defer qe.mu.Unlock()

	qe.queryCache[sql] = &CachedQuery{
		SQL:       sql,
		Result:    result,
		Timestamp: time.Now(),
		TTL:       5 * time.Minute, // Default TTL
	}
}

// cacheCleanupWorker runs background cache cleanup
func (qe *QueryEngine) cacheCleanupWorker() {
	defer qe.wg.Done()

	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-qe.ctx.Done():
			return
		case <-ticker.C:
			qe.cleanupCache()
		}
	}
}

// cleanupCache removes expired cache entries
func (qe *QueryEngine) cleanupCache() {
	qe.mu.Lock()
	defer qe.mu.Unlock()

	now := time.Now()
	for sql, cached := range qe.queryCache {
		if now.Sub(cached.Timestamp) > cached.TTL {
			delete(qe.queryCache, sql)
		}
	}
}

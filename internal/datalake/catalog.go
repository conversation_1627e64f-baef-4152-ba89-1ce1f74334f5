package datalake

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/apache/iceberg-go/catalog"
	"github.com/apache/iceberg-go/catalog/rest"
	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/storage"
)

// Catalog provides Iceberg catalog operations
type Catalog struct {
	config     *config.Config
	logger     *zap.Logger
	storage    storage.Client
	catalog    catalog.Catalog
	namespaces map[string]*CatalogNamespace
	tables     map[string]*Table
	running    bool
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
	mu         sync.RWMutex
}

// NewCatalog creates a new catalog instance
func NewCatalog(cfg *config.Config, logger *zap.Logger, storageClient storage.Client) (*Catalog, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}
	if storageClient == nil {
		return nil, fmt.Errorf("storage client is required")
	}

	// Create Iceberg catalog based on configuration
	var icebergCatalog catalog.Catalog
	var err error

	ctx := context.Background()
	catalogConfig := cfg.DataLake.Catalog
	switch catalogConfig.Type {
	case "rest":
		icebergCatalog, err = rest.NewCatalog(
			ctx,
			"rest",
			catalogConfig.URI,
		)
	case "glue":
		// TODO: Implement Glue catalog
		return nil, fmt.Errorf("glue catalog not yet implemented")
	case "sql":
		// TODO: Implement SQL catalog
		return nil, fmt.Errorf("sql catalog not yet implemented")
	default:
		return nil, fmt.Errorf("unsupported catalog type: %s", catalogConfig.Type)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create iceberg catalog: %w", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	c := &Catalog{
		config:     cfg,
		logger:     logger,
		storage:    storageClient,
		catalog:    icebergCatalog,
		namespaces: make(map[string]*CatalogNamespace),
		tables:     make(map[string]*Table),
		ctx:        ctx,
		cancel:     cancel,
	}

	return c, nil
}

// Start starts the catalog
func (c *Catalog) Start() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.running {
		return fmt.Errorf("catalog is already running")
	}

	c.logger.Info("Starting catalog")

	// Load existing namespaces and tables
	if err := c.loadNamespaces(); err != nil {
		return fmt.Errorf("failed to load namespaces: %w", err)
	}

	if err := c.loadTables(); err != nil {
		return fmt.Errorf("failed to load tables: %w", err)
	}

	// Start background workers
	c.wg.Add(1)
	go c.syncWorker()

	c.running = true
	c.logger.Info("Catalog started successfully")

	return nil
}

// Stop stops the catalog
func (c *Catalog) Stop() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.running {
		return nil
	}

	c.logger.Info("Stopping catalog")

	// Cancel context
	c.cancel()

	// Wait for workers to finish
	c.wg.Wait()

	c.running = false
	c.logger.Info("Catalog stopped")

	return nil
}

// CreateNamespace creates a new namespace
func (c *Catalog) CreateNamespace(ctx context.Context, name string, properties map[string]string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.logger.Info("Creating namespace", zap.String("name", name))

	// Check if namespace already exists
	if _, exists := c.namespaces[name]; exists {
		return fmt.Errorf("namespace %s already exists", name)
	}

	// Create namespace in Iceberg catalog
	err := c.catalog.CreateNamespace(ctx, []string{name}, properties)
	if err != nil {
		return fmt.Errorf("failed to create namespace in catalog: %w", err)
	}

	// Add to local cache
	namespace := &CatalogNamespace{
		Name:       name,
		Properties: properties,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}
	c.namespaces[name] = namespace

	c.logger.Info("Namespace created successfully", zap.String("name", name))
	return nil
}

// DropNamespace drops a namespace
func (c *Catalog) DropNamespace(ctx context.Context, name string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.logger.Info("Dropping namespace", zap.String("name", name))

	// Check if namespace exists
	if _, exists := c.namespaces[name]; !exists {
		return fmt.Errorf("namespace %s does not exist", name)
	}

	// Drop namespace from Iceberg catalog
	err := c.catalog.DropNamespace(ctx, []string{name})
	if err != nil {
		return fmt.Errorf("failed to drop namespace from catalog: %w", err)
	}

	// Remove from local cache
	delete(c.namespaces, name)

	// Remove all tables in this namespace
	for key, table := range c.tables {
		if table.Namespace == name {
			delete(c.tables, key)
		}
	}

	c.logger.Info("Namespace dropped successfully", zap.String("name", name))
	return nil
}

// ListNamespaces lists all namespaces
func (c *Catalog) ListNamespaces(ctx context.Context) ([]*CatalogNamespace, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	namespaces := make([]*CatalogNamespace, 0, len(c.namespaces))
	for _, ns := range c.namespaces {
		namespaces = append(namespaces, ns)
	}

	return namespaces, nil
}

// CreateTable creates a new table
func (c *Catalog) CreateTable(ctx context.Context, request *CreateTableRequest) (*Table, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.logger.Info("Creating table",
		zap.String("namespace", request.Namespace),
		zap.String("table_name", request.TableName))

	// Check if namespace exists
	if _, exists := c.namespaces[request.Namespace]; !exists {
		return nil, fmt.Errorf("namespace %s does not exist", request.Namespace)
	}

	tableKey := fmt.Sprintf("%s.%s", request.Namespace, request.TableName)

	// Check if table already exists
	if _, exists := c.tables[tableKey]; exists {
		return nil, fmt.Errorf("table %s already exists", tableKey)
	}

	// Create table in Iceberg catalog
	tableID := catalog.ToIdentifier(request.Namespace, request.TableName)

	// Create table with options
	var opts []catalog.CreateTableOpt
	if request.PartitionSpec != nil {
		opts = append(opts, catalog.WithPartitionSpec(request.PartitionSpec))
	}
	if request.Properties != nil {
		opts = append(opts, catalog.WithProperties(request.Properties))
	}

	icebergTable, err := c.catalog.CreateTable(ctx, tableID, request.Schema, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to create table in catalog: %w", err)
	}

	// Create table object
	table := &Table{
		ID:         GenerateID(),
		Namespace:  request.Namespace,
		Name:       request.TableName,
		Location:   icebergTable.Location(),
		Schema:     request.Schema,
		Properties: request.Properties,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Snapshots:  []Snapshot{},
		Metadata:   make(map[string]interface{}),
	}

	// Add to local cache
	c.tables[tableKey] = table

	c.logger.Info("Table created successfully",
		zap.String("table_id", table.ID),
		zap.String("namespace", table.Namespace),
		zap.String("name", table.Name))

	return table, nil
}

// DropTable drops a table
func (c *Catalog) DropTable(ctx context.Context, namespace, tableName string) error {
	if !c.running {
		return fmt.Errorf("catalog is not running")
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	c.logger.Info("Dropping table",
		zap.String("namespace", namespace),
		zap.String("table_name", tableName))

	tableKey := fmt.Sprintf("%s.%s", namespace, tableName)

	// Check if table exists
	if _, exists := c.tables[tableKey]; !exists {
		return fmt.Errorf("table %s does not exist", tableKey)
	}

	// Drop table from Iceberg catalog
	tableID := catalog.ToIdentifier(namespace, tableName)

	err := c.catalog.DropTable(ctx, tableID)
	if err != nil {
		return fmt.Errorf("failed to drop table from catalog: %w", err)
	}

	// Remove from local cache
	delete(c.tables, tableKey)

	c.logger.Info("Table dropped successfully",
		zap.String("namespace", namespace),
		zap.String("table_name", tableName))

	return nil
}

// GetTable gets a table by namespace and name
func (c *Catalog) GetTable(ctx context.Context, namespace, tableName string) (*Table, error) {
	if !c.running {
		return nil, fmt.Errorf("catalog is not running")
	}

	c.mu.RLock()
	defer c.mu.RUnlock()

	tableKey := fmt.Sprintf("%s.%s", namespace, tableName)
	table, exists := c.tables[tableKey]
	if !exists {
		return nil, fmt.Errorf("table %s does not exist", tableKey)
	}

	return table, nil
}

// ListTables lists all tables in a namespace
func (c *Catalog) ListTables(ctx context.Context, namespace string) ([]*Table, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	tables := make([]*Table, 0)
	for _, table := range c.tables {
		if table.Namespace == namespace {
			tables = append(tables, table)
		}
	}

	return tables, nil
}

// Health checks the health of the catalog
func (c *Catalog) Health(ctx context.Context) error {
	// Check if catalog is running
	if !c.running {
		return fmt.Errorf("catalog is not running")
	}

	// Check if catalog is initialized
	if c.catalog == nil {
		return fmt.Errorf("catalog health check failed: catalog not initialized")
	}

	// Try to list namespaces to verify connectivity
	_, err := c.catalog.ListNamespaces(ctx, []string{})
	if err != nil {
		return fmt.Errorf("catalog health check failed: %w", err)
	}

	return nil
}

// loadNamespaces loads namespaces from the catalog
func (c *Catalog) loadNamespaces() error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	namespaces, err := c.catalog.ListNamespaces(ctx, []string{})
	if err != nil {
		return fmt.Errorf("failed to list namespaces: %w", err)
	}

	for _, ns := range namespaces {
		if len(ns) > 0 {
			name := ns[0] // Assuming single-level namespaces
			c.namespaces[name] = &CatalogNamespace{
				Name:       name,
				Properties: make(map[string]string),
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
			}
		}
	}

	c.logger.Info("Loaded namespaces", zap.Int("count", len(c.namespaces)))
	return nil
}

// loadTables loads tables from the catalog
func (c *Catalog) loadTables() error {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	for nsName := range c.namespaces {
		tables := c.catalog.ListTables(ctx, catalog.ToIdentifier(nsName))
		for tableIdent, err := range tables {
			if err != nil {
				c.logger.Warn("Failed to list table",
					zap.String("namespace", nsName),
					zap.Error(err))
				continue
			}

			namespace := tableIdent[0] // Assuming single-level namespace
			tableName := tableIdent[1]
			tableKey := fmt.Sprintf("%s.%s", namespace, tableName)

			// Load table metadata
			icebergTable, err := c.catalog.LoadTable(ctx, tableIdent)
			if err != nil {
				c.logger.Warn("Failed to load table",
					zap.String("table", tableKey),
					zap.Error(err))
				continue
			}

			table := &Table{
				ID:         GenerateID(),
				Namespace:  namespace,
				Name:       tableName,
				Location:   icebergTable.Location(),
				Schema:     icebergTable.Schema(),
				Properties: make(map[string]string),
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
				Snapshots:  []Snapshot{},
				Metadata:   make(map[string]interface{}),
			}

			c.tables[tableKey] = table
		}
	}

	c.logger.Info("Loaded tables", zap.Int("count", len(c.tables)))
	return nil
}

// syncWorker runs background synchronization
func (c *Catalog) syncWorker() {
	defer c.wg.Done()

	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.syncCatalog()
		}
	}
}

// syncCatalog synchronizes the local cache with the remote catalog
func (c *Catalog) syncCatalog() {
	c.logger.Debug("Synchronizing catalog")

	if err := c.loadNamespaces(); err != nil {
		c.logger.Error("Failed to sync namespaces", zap.Error(err))
	}

	if err := c.loadTables(); err != nil {
		c.logger.Error("Failed to sync tables", zap.Error(err))
	}
}

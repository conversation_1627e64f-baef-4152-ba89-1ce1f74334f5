# Data Lake Integration

This package provides comprehensive data lake integration capabilities using Apache Iceberg table format. It offers a high-level client interface for managing Iceberg tables, executing queries, and performing analytics operations.

## Features

- **Iceberg Table Management**: Create, read, update, and delete Iceberg tables
- **Multiple Catalog Support**: REST, AWS Glue, and SQL catalogs
- **Query Engine**: SQL query execution with caching
- **Analytics Engine**: Built-in analytics jobs for data quality, aggregation, and trend analysis
- **Object Storage Integration**: S3, MinIO, and other S3-compatible storage backends
- **CLI Tool**: Command-line interface for data lake operations
- **Comprehensive Testing**: Unit tests, integration tests, and performance tests

## Architecture

The data lake integration consists of several key components:

### Client
The main entry point that orchestrates all data lake operations. It manages the lifecycle of other components and provides a unified interface.

### Catalog
Manages Iceberg catalog operations including namespace and table management. Supports multiple catalog types:
- **REST Catalog**: HTTP-based catalog service
- **AWS Glue Catalog**: AWS Glue Data Catalog integration
- **SQL Catalog**: Database-backed catalog

### Table Manager
Handles table-level operations including:
- Table creation and deletion
- Data reading and writing
- Table statistics and metadata
- Compaction operations

### Query Engine
Provides SQL query execution capabilities:
- SELECT, SHOW, DESCRIBE queries
- Query result caching
- Query parsing and optimization

### Analytics Engine
Offers advanced analytics capabilities:
- Table statistics analysis
- Data quality assessment
- Aggregation operations
- Trend analysis
- Anomaly detection

## Usage

### Basic Setup

```go
import (
    "github.com/gollm/core-gollmslake-go/internal/datalake"
    "github.com/gollm/core-gollmslake-go/pkg/config"
    "github.com/gollm/core-gollmslake-go/pkg/logger"
)

// Load configuration
cfg, err := config.LoadConfig("configs/datalake.yaml")
if err != nil {
    log.Fatal(err)
}

// Create logger
logger, err := logger.NewLogger("info")
if err != nil {
    log.Fatal(err)
}

// Create and start client
client, err := datalake.NewClient(cfg, logger)
if err != nil {
    log.Fatal(err)
}

err = client.Start()
if err != nil {
    log.Fatal(err)
}
defer client.Stop()
```

### Namespace Operations

```go
// Create namespace
err := client.CreateNamespace(ctx, "analytics", map[string]string{
    "description": "Analytics namespace",
})

// List namespaces
namespaces, err := client.ListNamespaces(ctx)

// Drop namespace
err := client.DropNamespace(ctx, "analytics")
```

### Table Operations

```go
// Create table
request := &datalake.CreateTableRequest{
    Namespace:  "analytics",
    TableName:  "events",
    Schema:     schema, // Iceberg schema
    Properties: map[string]string{"format": "parquet"},
}
table, err := client.CreateTable(ctx, request)

// Write data
writeRequest := &datalake.WriteDataRequest{
    Namespace: "analytics",
    TableName: "events",
    Records:   records,
    WriteMode: datalake.WriteAppend,
}
result, err := client.WriteData(ctx, writeRequest)

// Read data
readRequest := &datalake.ReadDataRequest{
    Namespace: "analytics",
    TableName: "events",
    Filter:    "timestamp > '2023-01-01'",
}
result, err := client.ReadData(ctx, readRequest)
```

### Query Operations

```go
// Execute SQL query
queryRequest := &datalake.QueryRequest{
    SQL: "SELECT * FROM analytics.events WHERE event_type = 'login'",
}
result, err := client.ExecuteQuery(ctx, queryRequest)
```

### Analytics Operations

```go
// Run analytics job
analyticsRequest := &datalake.AnalyticsRequest{
    JobID:   "stats-job-1",
    JobType: "table_stats",
    Tables:  []string{"analytics.events"},
    Parameters: map[string]interface{}{
        "include_histograms": true,
    },
}
result, err := client.RunAnalytics(ctx, analyticsRequest)
```

## CLI Tool

The package includes a comprehensive CLI tool for data lake operations:

```bash
# Create namespace
datalake-cli create-namespace analytics -p description="Analytics namespace"

# List tables
datalake-cli list-tables analytics

# Create table
datalake-cli create-table analytics.events -s schema.json -l s3://bucket/path/

# Write data
datalake-cli write analytics.events -f data.json -m append

# Read data
datalake-cli read analytics.events -f "timestamp > '2023-01-01'" -l 100

# Execute query
datalake-cli query "SELECT COUNT(*) FROM analytics.events"

# Run analytics
datalake-cli analytics -t table_stats --tables analytics.events

# Check status
datalake-cli status
```

## Configuration

The data lake integration is configured through the main application configuration file:

```yaml
data_lake:
  enabled: true
  catalog:
    type: "rest"  # Options: rest, glue, sql
    uri: "http://localhost:8181"
    properties:
      warehouse: "s3://data-lake-warehouse/"
      io-impl: "org.apache.iceberg.aws.s3.S3FileIO"
      s3.endpoint: "http://localhost:9000"
      s3.access-key-id: "minioadmin"
      s3.secret-access-key: "minioadmin"
```

## Testing

The package includes comprehensive tests:

### Unit Tests
```bash
go test ./internal/datalake/... -v
```

### Integration Tests
```bash
go test ./internal/datalake/... -v -tags=integration
```

### Performance Tests
```bash
go test ./internal/datalake/... -v -bench=. -benchmem
```

## Dependencies

- **Apache Iceberg Go**: Official Go implementation of Iceberg
- **Object Storage**: S3-compatible storage (S3, MinIO, etc.)
- **Catalog Service**: REST catalog or AWS Glue
- **Arrow**: Apache Arrow for columnar data processing

## Performance Considerations

- **Caching**: Query results are cached for improved performance
- **Batching**: Write operations are batched for efficiency
- **Compression**: Parquet format with compression for storage optimization
- **Partitioning**: Support for table partitioning strategies
- **Concurrent Operations**: Thread-safe operations with proper synchronization

## Monitoring and Observability

The client provides comprehensive metrics:
- Tables created/dropped
- Records written/read
- Query execution statistics
- Analytics job metrics
- Storage utilization
- Performance metrics

## Error Handling

The package implements robust error handling:
- Retry mechanisms for transient failures
- Circuit breaker patterns for external dependencies
- Comprehensive error logging and reporting
- Graceful degradation for non-critical operations

## Security

- **Authentication**: Support for various authentication mechanisms
- **Authorization**: Role-based access control integration
- **Encryption**: Data encryption at rest and in transit
- **Audit Logging**: Comprehensive audit trail for all operations

## Future Enhancements

- **Schema Evolution**: Advanced schema evolution capabilities
- **Time Travel**: Historical data querying
- **Streaming Integration**: Real-time data ingestion
- **Advanced Analytics**: Machine learning integration
- **Multi-Cloud Support**: Enhanced multi-cloud capabilities

package datalake

import (
	"context"
	"testing"
	"time"

	"github.com/apache/iceberg-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"
)

// TestMockComponentSet_Creation tests the creation and basic functionality of mock component set
func TestMockComponentSet_Creation(t *testing.T) {
	mcs := NewMockComponentSet()

	// Verify all components are created
	assert.NotNil(t, mcs.Storage)
	assert.NotNil(t, mcs.Catalog)
	assert.NotNil(t, mcs.TableManager)
	assert.NotNil(t, mcs.QueryEngine)
	assert.NotNil(t, mcs.AnalyticsEngine)
	assert.NotNil(t, mcs.Logger)

	// Test starting all components
	err := mcs.StartAll()
	assert.NoError(t, err)

	// Test stopping all components
	err = mcs.StopAll()
	assert.NoError(t, err)
}

// TestMockCatalog_BehaviorConfiguration tests configurable behavior for mock catalog
func TestMockCatalog_BehaviorConfiguration(t *testing.T) {
	logger := zaptest.NewLogger(t)
	catalog := NewMockCatalog(logger)
	ctx := context.Background()

	// Test normal operation
	err := catalog.Start()
	assert.NoError(t, err)

	err = catalog.Health(ctx)
	assert.NoError(t, err)

	// Configure failure behavior
	catalog.SetBehavior("Health", &MockBehavior{
		ShouldFail:     true,
		FailureMessage: "simulated health check failure",
	})

	// Test failure behavior
	err = catalog.Health(ctx)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "simulated health check failure")

	// Test call history
	history := catalog.GetCallHistory()
	assert.Contains(t, history, "Start")
	assert.Contains(t, history, "Health")
	assert.Len(t, history, 3) // Start + Health (success) + Health (failure)
}

// TestMockCatalog_DelaySimulation tests delay simulation in mock catalog
func TestMockCatalog_DelaySimulation(t *testing.T) {
	logger := zaptest.NewLogger(t)
	catalog := NewMockCatalog(logger)
	ctx := context.Background()

	err := catalog.Start()
	require.NoError(t, err)

	// Configure delay behavior
	delay := 100 * time.Millisecond
	catalog.SetBehavior("Health", &MockBehavior{
		Delay: delay,
	})

	// Measure execution time
	start := time.Now()
	err = catalog.Health(ctx)
	duration := time.Since(start)

	assert.NoError(t, err)
	assert.GreaterOrEqual(t, duration, delay)
}

// TestMockCatalog_TimeoutSimulation tests timeout simulation in mock catalog
func TestMockCatalog_TimeoutSimulation(t *testing.T) {
	logger := zaptest.NewLogger(t)
	catalog := NewMockCatalog(logger)
	ctx := context.Background()

	err := catalog.Start()
	require.NoError(t, err)

	// Configure timeout behavior
	catalog.SetBehavior("Health", &MockBehavior{
		SimulateTimeout: true,
	})

	// Test timeout behavior
	err = catalog.Health(ctx)
	assert.Error(t, err)
	assert.Equal(t, context.DeadlineExceeded, err)
}

// TestMockCatalog_RateLimitSimulation tests rate limiting simulation in mock catalog
func TestMockCatalog_RateLimitSimulation(t *testing.T) {
	logger := zaptest.NewLogger(t)
	catalog := NewMockCatalog(logger)
	ctx := context.Background()

	err := catalog.Start()
	require.NoError(t, err)

	// Configure rate limit behavior
	catalog.SetBehavior("Health", &MockBehavior{
		SimulateRateLimit: true,
	})

	// Test rate limit behavior
	err = catalog.Health(ctx)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "rate limit exceeded")
}

// TestMockCatalog_MaxCallsLimit tests maximum calls limit in mock catalog
func TestMockCatalog_MaxCallsLimit(t *testing.T) {
	logger := zaptest.NewLogger(t)
	catalog := NewMockCatalog(logger)
	ctx := context.Background()

	err := catalog.Start()
	require.NoError(t, err)

	// Configure max calls behavior
	catalog.SetBehavior("Health", &MockBehavior{
		MaxCalls: 2,
	})

	// First two calls should succeed
	err = catalog.Health(ctx)
	assert.NoError(t, err)

	err = catalog.Health(ctx)
	assert.NoError(t, err)

	// Third call should fail
	err = catalog.Health(ctx)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "exceeded max calls limit")
}

// TestMockCatalog_NamespaceOperations tests namespace operations with mock catalog
func TestMockCatalog_NamespaceOperations(t *testing.T) {
	logger := zaptest.NewLogger(t)
	catalog := NewMockCatalog(logger)
	ctx := context.Background()

	err := catalog.Start()
	require.NoError(t, err)

	// Test create namespace
	err = catalog.CreateNamespace(ctx, "test_namespace", map[string]string{
		"description": "Test namespace",
	})
	assert.NoError(t, err)

	// Test list namespaces
	namespaces, err := catalog.ListNamespaces(ctx)
	assert.NoError(t, err)
	assert.Len(t, namespaces, 1)
	assert.Equal(t, "test_namespace", namespaces[0].Name)

	// Test duplicate namespace creation
	err = catalog.CreateNamespace(ctx, "test_namespace", nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already exists")

	// Test drop namespace
	err = catalog.DropNamespace(ctx, "test_namespace")
	assert.NoError(t, err)

	// Test list namespaces after drop
	namespaces, err = catalog.ListNamespaces(ctx)
	assert.NoError(t, err)
	assert.Len(t, namespaces, 0)
}

// TestMockCatalog_TableOperations tests table operations with mock catalog
func TestMockCatalog_TableOperations(t *testing.T) {
	logger := zaptest.NewLogger(t)
	catalog := NewMockCatalog(logger)
	ctx := context.Background()

	err := catalog.Start()
	require.NoError(t, err)

	// Create test schema
	schema := iceberg.NewSchema(0,
		iceberg.NestedField{
			ID:       1,
			Name:     "id",
			Type:     iceberg.PrimitiveTypes.Int64,
			Required: true,
		},
		iceberg.NestedField{
			ID:       2,
			Name:     "name",
			Type:     iceberg.PrimitiveTypes.String,
			Required: false,
		},
	)

	// Test create table
	request := &CreateTableRequest{
		Namespace: "test_namespace",
		TableName: "test_table",
		Schema:    schema,
		Properties: map[string]string{
			"description": "Test table",
		},
	}

	table, err := catalog.CreateTable(ctx, request)
	assert.NoError(t, err)
	assert.NotNil(t, table)
	assert.Equal(t, "test_namespace", table.Namespace)
	assert.Equal(t, "test_table", table.Name)
	assert.NotEmpty(t, table.ID)

	// Test get table
	retrievedTable, err := catalog.GetTable(ctx, "test_namespace", "test_table")
	assert.NoError(t, err)
	assert.Equal(t, table.ID, retrievedTable.ID)

	// Test list tables
	tables, err := catalog.ListTables(ctx, "test_namespace")
	assert.NoError(t, err)
	assert.Len(t, tables, 1)
	assert.Equal(t, table.ID, tables[0].ID)

	// Test drop table
	err = catalog.DropTable(ctx, "test_namespace", "test_table")
	assert.NoError(t, err)

	// Test get table after drop
	_, err = catalog.GetTable(ctx, "test_namespace", "test_table")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "does not exist")
}

// TestMockTableManager_WriteReadOperations tests write and read operations with mock table manager
func TestMockTableManager_WriteReadOperations(t *testing.T) {
	logger := zaptest.NewLogger(t)
	catalog := NewMockCatalog(logger)
	tableManager := NewMockTableManager(logger, catalog)
	ctx := context.Background()

	err := catalog.Start()
	require.NoError(t, err)

	err = tableManager.Start()
	require.NoError(t, err)

	// Test write data
	writeRequest := &WriteDataRequest{
		Namespace: "test_namespace",
		TableName: "test_table",
		Records: []map[string]interface{}{
			{"id": 1, "name": "test1"},
			{"id": 2, "name": "test2"},
		},
		WriteMode: WriteModeAppend,
	}

	writeResult, err := tableManager.WriteData(ctx, writeRequest)
	assert.NoError(t, err)
	assert.NotNil(t, writeResult)
	assert.Equal(t, int64(2), writeResult.RecordsWritten)
	assert.Equal(t, int64(200), writeResult.BytesWritten) // 2 records * 100 bytes each
	assert.Equal(t, 1, writeResult.FilesAdded)

	// Test read data
	readRequest := &ReadDataRequest{
		Namespace: "test_namespace",
		TableName: "test_table",
		Filter:    "id > 0",
	}

	readResult, err := tableManager.ReadData(ctx, readRequest)
	assert.NoError(t, err)
	assert.NotNil(t, readResult)
	assert.Len(t, readResult.Records, 2)
	assert.Equal(t, int64(2), readResult.RecordsRead)

	// Test get table stats
	stats, err := tableManager.GetTableStats(ctx, "test_namespace", "test_table")
	assert.NoError(t, err)
	assert.NotNil(t, stats)
	assert.Equal(t, "test_table", stats.TableName)
	assert.Equal(t, "test_namespace", stats.Namespace)
	assert.Equal(t, int64(1000), stats.RecordCount)
}

// TestMockQueryEngine_QueryExecution tests query execution with mock query engine
func TestMockQueryEngine_QueryExecution(t *testing.T) {
	logger := zaptest.NewLogger(t)
	queryEngine := NewMockQueryEngine(logger)
	ctx := context.Background()

	err := queryEngine.Start()
	require.NoError(t, err)

	// Test query execution
	request := &QueryRequest{
		SQL: "SELECT COUNT(*) FROM test_table",
		Parameters: map[string]interface{}{
			"limit": 100,
		},
	}

	result, err := queryEngine.ExecuteQuery(ctx, request)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, []string{"count", "table"}, result.Columns)
	assert.Len(t, result.Rows, 2)
	assert.Equal(t, int64(2), result.RowCount)
	assert.Greater(t, result.Duration, time.Duration(0))

	// Test query caching - same query should return cached result
	result2, err := queryEngine.ExecuteQuery(ctx, request)
	assert.NoError(t, err)
	assert.Equal(t, result, result2) // Should be the same cached result

	// Test different query
	request2 := &QueryRequest{
		SQL: "SELECT * FROM another_table",
	}

	result3, err := queryEngine.ExecuteQuery(ctx, request2)
	assert.NoError(t, err)
	assert.NotEqual(t, result, result3) // Should be different result
}

// TestMockAnalyticsEngine_JobManagement tests job management with mock analytics engine
func TestMockAnalyticsEngine_JobManagement(t *testing.T) {
	logger := zaptest.NewLogger(t)
	analyticsEngine := NewMockAnalyticsEngine(logger)
	ctx := context.Background()

	err := analyticsEngine.Start()
	require.NoError(t, err)

	// Test run analytics job
	request := &AnalyticsRequest{
		JobID:   "test-job-1",
		JobType: "table_stats",
		Tables:  []string{"test_namespace.test_table"},
		Parameters: map[string]interface{}{
			"include_histograms": true,
		},
	}

	result, err := analyticsEngine.RunAnalytics(ctx, request)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "test-job-1", result.JobID)
	assert.Equal(t, "completed", result.Status)
	assert.Contains(t, result.Results, "total_records")
	assert.Equal(t, 10000, result.Results["total_records"])

	// Test get job
	job, err := analyticsEngine.GetJob(ctx, "test-job-1")
	assert.NoError(t, err)
	assert.NotNil(t, job)
	assert.Equal(t, "test-job-1", job.ID)
	assert.Equal(t, "table_stats", job.Type)
	assert.Equal(t, "completed", job.Status)
	assert.Equal(t, 100.0, job.Progress)

	// Test list jobs
	jobs, err := analyticsEngine.ListJobs(ctx)
	assert.NoError(t, err)
	assert.Len(t, jobs, 1)
	assert.Equal(t, "test-job-1", jobs[0].ID)

	// Test run data quality job
	request2 := &AnalyticsRequest{
		JobID:   "test-job-2",
		JobType: "data_quality",
		Tables:  []string{"test_namespace.test_table"},
	}

	result2, err := analyticsEngine.RunAnalytics(ctx, request2)
	assert.NoError(t, err)
	assert.Contains(t, result2.Results, "quality_score")
	assert.Equal(t, 0.95, result2.Results["quality_score"])

	// Test cancel job
	err = analyticsEngine.CancelJob(ctx, "test-job-2")
	assert.NoError(t, err)

	// Verify job was cancelled
	cancelledJob, err := analyticsEngine.GetJob(ctx, "test-job-2")
	assert.NoError(t, err)
	assert.Equal(t, "cancelled", cancelledJob.Status)
	assert.NotNil(t, cancelledJob.CompletedAt)
}

// TestMockComponentSet_IntegratedBehavior tests integrated behavior of all mock components
func TestMockComponentSet_IntegratedBehavior(t *testing.T) {
	mcs := NewMockComponentSet()
	ctx := context.Background()

	// Start all components
	err := mcs.StartAll()
	require.NoError(t, err)

	// Configure delay for all components
	mcs.ConfigureDelayBehavior("Health", 50*time.Millisecond)

	// Test health checks with delay
	start := time.Now()
	err = mcs.Catalog.Health(ctx)
	duration := time.Since(start)
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, duration, 50*time.Millisecond)

	// Configure failure for specific method
	mcs.ConfigureFailureBehavior("CreateTable", true, "simulated create table failure")

	// Test create table failure
	schema := iceberg.NewSchema(0,
		iceberg.NestedField{
			ID:       1,
			Name:     "id",
			Type:     iceberg.PrimitiveTypes.Int64,
			Required: true,
		},
	)

	request := &CreateTableRequest{
		Namespace: "test_namespace",
		TableName: "test_table",
		Schema:    schema,
	}

	_, err = mcs.TableManager.CreateTable(ctx, request)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "simulated create table failure")

	// Test call history
	history := mcs.GetAllCallHistory()
	assert.Contains(t, history["catalog"], "Health")
	assert.Contains(t, history["tableManager"], "CreateTable")

	// Stop all components
	err = mcs.StopAll()
	assert.NoError(t, err)
}

// TestMockComponentSet_ConcurrentAccess tests concurrent access to mock components
func TestMockComponentSet_ConcurrentAccess(t *testing.T) {
	mcs := NewMockComponentSet()
	ctx := context.Background()

	err := mcs.StartAll()
	require.NoError(t, err)
	defer mcs.StopAll()

	// Configure small delay to increase chance of race conditions
	mcs.ConfigureDelayBehavior("Health", 10*time.Millisecond)

	// Run concurrent health checks
	const numGoroutines = 10
	const numCallsPerGoroutine = 5

	done := make(chan bool, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer func() { done <- true }()

			for j := 0; j < numCallsPerGoroutine; j++ {
				err := mcs.Catalog.Health(ctx)
				assert.NoError(t, err)

				_, err = mcs.QueryEngine.ExecuteQuery(ctx, &QueryRequest{
					SQL: "SELECT 1",
				})
				assert.NoError(t, err)
			}
		}()
	}

	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// Verify call history
	history := mcs.GetAllCallHistory()
	assert.Len(t, history["catalog"], numGoroutines*numCallsPerGoroutine+1)     // +1 for Start
	assert.Len(t, history["queryEngine"], numGoroutines*numCallsPerGoroutine+1) // +1 for Start
}

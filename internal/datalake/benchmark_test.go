package datalake

import (
	"testing"
	"time"
)

// BenchmarkGenerateID tests the performance of ID generation
func BenchmarkGenerateID(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GenerateID()
	}
}

// BenchmarkTableExists tests the performance of table existence checks
func BenchmarkTableExists(b *testing.B) {
	table := &Table{
		ID:        "test-table-123",
		Namespace: "test_namespace",
		Name:      "test_table",
		Snapshots: []Snapshot{
			{
				ID:          1,
				TimestampMs: time.Now().UnixMilli(),
				Summary:     map[string]string{"operation": "append"},
			},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		table.Exists()
	}
}

// BenchmarkTableIsEmpty tests the performance of empty table checks
func BenchmarkTableIsEmpty(b *testing.B) {
	table := &Table{
		ID:        "test-table-123",
		Namespace: "test_namespace",
		Name:      "test_table",
		Snapshots: []Snapshot{
			{
				ID:          1,
				TimestampMs: time.Now().UnixMilli(),
				Summary:     map[string]string{"operation": "append"},
			},
		},
	}

	b.<PERSON>setTimer()
	for i := 0; i < b.N; i++ {
		table.IsEmpty()
	}
}

// BenchmarkTableGetCurrentSnapshot tests the performance of current snapshot retrieval
func BenchmarkTableGetCurrentSnapshot(b *testing.B) {
	snapshots := make([]Snapshot, 100)
	for i := 0; i < 100; i++ {
		snapshots[i] = Snapshot{
			ID:          int64(i + 1),
			TimestampMs: time.Now().UnixMilli() + int64(i),
			Summary:     map[string]string{"operation": "append"},
		}
	}

	table := &Table{
		ID:        "test-table-123",
		Namespace: "test_namespace",
		Name:      "test_table",
		Snapshots: snapshots,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		table.GetCurrentSnapshot()
	}
}

// BenchmarkTableGetSnapshotByID tests the performance of snapshot lookup by ID
func BenchmarkTableGetSnapshotByID(b *testing.B) {
	snapshots := make([]Snapshot, 1000)
	for i := 0; i < 1000; i++ {
		snapshots[i] = Snapshot{
			ID:          int64(i + 1),
			TimestampMs: time.Now().UnixMilli() + int64(i),
			Summary:     map[string]string{"operation": "append"},
		}
	}

	table := &Table{
		ID:        "test-table-123",
		Namespace: "test_namespace",
		Name:      "test_table",
		Snapshots: snapshots,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Search for a snapshot in the middle of the list
		table.GetSnapshotByID(500)
	}
}

// BenchmarkTableIdentifierString tests the performance of TableIdentifier string conversion
func BenchmarkTableIdentifierString(b *testing.B) {
	identifier := NewTableIdentifier("test_namespace", "test_table")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = identifier.String()
	}
}

// BenchmarkNewTableIdentifier tests the performance of TableIdentifier creation
func BenchmarkNewTableIdentifier(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		NewTableIdentifier("test_namespace", "test_table")
	}
}

// BenchmarkWriteDataRequestCreation tests the performance of write request creation
func BenchmarkWriteDataRequestCreation(b *testing.B) {
	records := make([]map[string]interface{}, 100)
	for i := 0; i < 100; i++ {
		records[i] = map[string]interface{}{
			"id":        i,
			"name":      "test",
			"timestamp": time.Now().Unix(),
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = &WriteDataRequest{
			Namespace: "test_namespace",
			TableName: "test_table",
			Records:   records,
			WriteMode: WriteModeAppend,
		}
	}
}

// BenchmarkReadDataRequestCreation tests the performance of read request creation
func BenchmarkReadDataRequestCreation(b *testing.B) {
	limit := int64(100)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = &ReadDataRequest{
			Namespace: "test_namespace",
			TableName: "test_table",
			Filter:    "id > 0",
			Limit:     &limit,
		}
	}
}

// BenchmarkQueryRequestCreation tests the performance of query request creation
func BenchmarkQueryRequestCreation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = &QueryRequest{
			SQL: "SELECT * FROM test_namespace.test_table WHERE id > 0",
			Parameters: map[string]interface{}{
				"limit": 100,
			},
		}
	}
}

// BenchmarkAnalyticsRequestCreation tests the performance of analytics request creation
func BenchmarkAnalyticsRequestCreation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = &AnalyticsRequest{
			JobID:   "test-job-123",
			JobType: "table_stats",
			Tables:  []string{"test_namespace.test_table", "test_namespace.other_table"},
			Parameters: map[string]interface{}{
				"include_histograms": true,
				"sample_size":        1000,
			},
		}
	}
}

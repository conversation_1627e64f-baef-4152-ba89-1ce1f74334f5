package datalake

import (
	"time"

	"github.com/apache/iceberg-go"
	"github.com/google/uuid"
)

// Table represents an Iceberg table
type Table struct {
	ID         string                 `json:"id"`
	Namespace  string                 `json:"namespace"`
	Name       string                 `json:"name"`
	Location   string                 `json:"location"`
	Schema     *iceberg.Schema        `json:"schema"`
	Properties map[string]string      `json:"properties"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
	Snapshots  []Snapshot             `json:"snapshots,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// Snapshot represents an Iceberg table snapshot
type Snapshot struct {
	ID               int64                  `json:"id"`
	TimestampMs      int64                  `json:"timestamp_ms"`
	SequenceNumber   int64                  `json:"sequence_number"`
	SchemaID         int                    `json:"schema_id"`
	ManifestList     string                 `json:"manifest_list"`
	Summary          map[string]string      `json:"summary"`
	Operation        string                 `json:"operation"`
	ParentSnapshotID *int64                 `json:"parent_snapshot_id,omitempty"`
	Metadata         map[string]interface{} `json:"metadata,omitempty"`
}

// CreateTableRequest represents a request to create a new table
type CreateTableRequest struct {
	Namespace     string                 `json:"namespace"`
	TableName     string                 `json:"table_name"`
	Schema        *iceberg.Schema        `json:"schema"`
	Location      string                 `json:"location,omitempty"`
	Properties    map[string]string      `json:"properties,omitempty"`
	PartitionSpec *iceberg.PartitionSpec `json:"partition_spec,omitempty"`
	// SortOrder   *iceberg.SortOrder     `json:"sort_order,omitempty"` // TODO: Add when available in API
}

// WriteDataRequest represents a request to write data to a table
type WriteDataRequest struct {
	Namespace string                   `json:"namespace"`
	TableName string                   `json:"table_name"`
	Records   []map[string]interface{} `json:"records"`
	WriteMode WriteMode                `json:"write_mode"`
	Options   map[string]interface{}   `json:"options,omitempty"`
}

// WriteMode defines how data should be written to the table
type WriteMode string

const (
	WriteModeAppend    WriteMode = "append"
	WriteModeOverwrite WriteMode = "overwrite"
	WriteModeMerge     WriteMode = "merge"
)

// WriteResult represents the result of a write operation
type WriteResult struct {
	SnapshotID     int64                  `json:"snapshot_id"`
	RecordsWritten int64                  `json:"records_written"`
	FilesAdded     int                    `json:"files_added"`
	BytesWritten   int64                  `json:"bytes_written"`
	Duration       time.Duration          `json:"duration"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// ReadDataRequest represents a request to read data from a table
type ReadDataRequest struct {
	Namespace  string                 `json:"namespace"`
	TableName  string                 `json:"table_name"`
	SnapshotID *int64                 `json:"snapshot_id,omitempty"`
	Filter     string                 `json:"filter,omitempty"`
	Projection []string               `json:"projection,omitempty"`
	Limit      *int64                 `json:"limit,omitempty"`
	Options    map[string]interface{} `json:"options,omitempty"`
}

// ReadResult represents the result of a read operation
type ReadResult struct {
	Records     []map[string]interface{} `json:"records"`
	RecordsRead int64                    `json:"records_read"`
	Duration    time.Duration            `json:"duration"`
	Metadata    map[string]interface{}   `json:"metadata,omitempty"`
}

// QueryRequest represents a SQL query request
type QueryRequest struct {
	SQL        string                 `json:"sql"`
	Parameters map[string]interface{} `json:"parameters,omitempty"`
	Options    map[string]interface{} `json:"options,omitempty"`
}

// QueryResult represents the result of a SQL query
type QueryResult struct {
	Columns  []string               `json:"columns"`
	Rows     [][]interface{}        `json:"rows"`
	RowCount int64                  `json:"row_count"`
	Duration time.Duration          `json:"duration"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// AnalyticsRequest represents an analytics job request
type AnalyticsRequest struct {
	JobID      string                 `json:"job_id"`
	JobType    string                 `json:"job_type"`
	Tables     []string               `json:"tables"`
	Parameters map[string]interface{} `json:"parameters"`
	Options    map[string]interface{} `json:"options,omitempty"`
}

// AnalyticsResult represents the result of an analytics job
type AnalyticsResult struct {
	JobID    string                 `json:"job_id"`
	Status   string                 `json:"status"`
	Results  map[string]interface{} `json:"results"`
	Duration time.Duration          `json:"duration"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// CatalogConfig represents catalog configuration
type CatalogConfig struct {
	Type       string            `json:"type"`
	URI        string            `json:"uri"`
	Properties map[string]string `json:"properties"`
	Warehouse  string            `json:"warehouse"`
}

// TableScan represents a table scan operation
type TableScan struct {
	Table      *Table                    `json:"table"`
	SnapshotID *int64                    `json:"snapshot_id,omitempty"`
	Filter     iceberg.BooleanExpression `json:"filter,omitempty"`
	Projection []string                  `json:"projection,omitempty"`
	Options    map[string]interface{}    `json:"options,omitempty"`
}

// DataFile represents a data file in an Iceberg table
type DataFile struct {
	Path            string         `json:"path"`
	Format          string         `json:"format"`
	RecordCount     int64          `json:"record_count"`
	FileSizeInBytes int64          `json:"file_size_in_bytes"`
	ColumnSizes     map[int]int64  `json:"column_sizes,omitempty"`
	ValueCounts     map[int]int64  `json:"value_counts,omitempty"`
	NullValueCounts map[int]int64  `json:"null_value_counts,omitempty"`
	NaNValueCounts  map[int]int64  `json:"nan_value_counts,omitempty"`
	LowerBounds     map[int][]byte `json:"lower_bounds,omitempty"`
	UpperBounds     map[int][]byte `json:"upper_bounds,omitempty"`
	KeyMetadata     []byte         `json:"key_metadata,omitempty"`
	SplitOffsets    []int64        `json:"split_offsets,omitempty"`
	SortOrderID     *int           `json:"sort_order_id,omitempty"`
}

// ManifestFile represents a manifest file in an Iceberg table
type ManifestFile struct {
	Path                   string         `json:"path"`
	Length                 int64          `json:"length"`
	PartitionSpecID        int            `json:"partition_spec_id"`
	Content                int            `json:"content"`
	SequenceNumber         int64          `json:"sequence_number"`
	MinSequenceNumber      int64          `json:"min_sequence_number"`
	AddedSnapshotID        int64          `json:"added_snapshot_id"`
	AddedDataFilesCount    int            `json:"added_data_files_count"`
	ExistingDataFilesCount int            `json:"existing_data_files_count"`
	DeletedDataFilesCount  int            `json:"deleted_data_files_count"`
	AddedRowsCount         int64          `json:"added_rows_count"`
	ExistingRowsCount      int64          `json:"existing_rows_count"`
	DeletedRowsCount       int64          `json:"deleted_rows_count"`
	Partitions             []FieldSummary `json:"partitions,omitempty"`
	KeyMetadata            []byte         `json:"key_metadata,omitempty"`
}

// FieldSummary represents summary statistics for a field
type FieldSummary struct {
	ContainsNull bool   `json:"contains_null"`
	ContainsNaN  *bool  `json:"contains_nan,omitempty"`
	LowerBound   []byte `json:"lower_bound,omitempty"`
	UpperBound   []byte `json:"upper_bound,omitempty"`
}

// CompactionJob represents a table compaction job
type CompactionJob struct {
	ID          string                 `json:"id"`
	TableName   string                 `json:"table_name"`
	Namespace   string                 `json:"namespace"`
	Status      string                 `json:"status"`
	Strategy    string                 `json:"strategy"`
	Parameters  map[string]interface{} `json:"parameters"`
	Progress    float64                `json:"progress"`
	StartedAt   time.Time              `json:"started_at"`
	CompletedAt *time.Time             `json:"completed_at,omitempty"`
	Error       string                 `json:"error,omitempty"`
}

// OptimizationJob represents a table optimization job
type OptimizationJob struct {
	ID          string                 `json:"id"`
	TableName   string                 `json:"table_name"`
	Namespace   string                 `json:"namespace"`
	JobType     string                 `json:"job_type"`
	Status      string                 `json:"status"`
	Parameters  map[string]interface{} `json:"parameters"`
	Progress    float64                `json:"progress"`
	StartedAt   time.Time              `json:"started_at"`
	CompletedAt *time.Time             `json:"completed_at,omitempty"`
	Error       string                 `json:"error,omitempty"`
}

// TableStats represents table statistics
type TableStats struct {
	TableName       string    `json:"table_name"`
	Namespace       string    `json:"namespace"`
	RecordCount     int64     `json:"record_count"`
	FileCount       int       `json:"file_count"`
	TotalSize       int64     `json:"total_size"`
	SnapshotCount   int       `json:"snapshot_count"`
	LastUpdated     time.Time `json:"last_updated"`
	PartitionCount  int       `json:"partition_count"`
	AverageFileSize int64     `json:"average_file_size"`
}

// CatalogNamespace represents a catalog namespace
type CatalogNamespace struct {
	Name       string            `json:"name"`
	Properties map[string]string `json:"properties"`
	CreatedAt  time.Time         `json:"created_at"`
	UpdatedAt  time.Time         `json:"updated_at"`
}

// TableIdentifier represents a table identifier
type TableIdentifier struct {
	Namespace string `json:"namespace"`
	Name      string `json:"name"`
}

// String returns the string representation of the table identifier
func (t TableIdentifier) String() string {
	return t.Namespace + "." + t.Name
}

// NewTableIdentifier creates a new table identifier
func NewTableIdentifier(namespace, name string) TableIdentifier {
	return TableIdentifier{
		Namespace: namespace,
		Name:      name,
	}
}

// GenerateID generates a new UUID string
func GenerateID() string {
	return uuid.New().String()
}

// TableExists checks if a table exists
func (t *Table) Exists() bool {
	return t.ID != ""
}

// IsEmpty checks if a table is empty
func (t *Table) IsEmpty() bool {
	return len(t.Snapshots) == 0
}

// GetCurrentSnapshot returns the current snapshot
func (t *Table) GetCurrentSnapshot() *Snapshot {
	if len(t.Snapshots) == 0 {
		return nil
	}
	return &t.Snapshots[len(t.Snapshots)-1]
}

// GetSnapshotByID returns a snapshot by ID
func (t *Table) GetSnapshotByID(id int64) *Snapshot {
	for _, snapshot := range t.Snapshots {
		if snapshot.ID == id {
			return &snapshot
		}
	}
	return nil
}

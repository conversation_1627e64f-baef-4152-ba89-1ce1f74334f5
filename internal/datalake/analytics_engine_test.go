package datalake

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"go.uber.org/zap/zaptest"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

func TestNewAnalyticsEngine(t *testing.T) {
	tests := []struct {
		name         string
		config       *config.Config
		logger       *zap.Logger
		queryEngine  *QueryEngine
		tableManager *TableManager
		expectError  bool
	}{
		{
			name:         "nil_config",
			config:       nil,
			logger:       zaptest.NewLogger(t),
			queryEngine:  &QueryEngine{},
			tableManager: &TableManager{},
			expectError:  true,
		},
		{
			name:         "nil_logger",
			config:       CreateValidAnalyticsEngineConfig(),
			logger:       nil,
			queryEngine:  &QueryEngine{},
			tableManager: &TableManager{},
			expectError:  true,
		},
		{
			name:         "nil_query_engine",
			config:       CreateValidAnalyticsEngineConfig(),
			logger:       zaptest.NewLogger(t),
			queryEngine:  nil,
			tableManager: &TableManager{},
			expectError:  true,
		},
		{
			name:         "nil_table_manager",
			config:       CreateValidAnalyticsEngineConfig(),
			logger:       zaptest.NewLogger(t),
			queryEngine:  &QueryEngine{},
			tableManager: nil,
			expectError:  true,
		},
		{
			name:         "valid_configuration",
			config:       CreateValidAnalyticsEngineConfig(),
			logger:       zaptest.NewLogger(t),
			queryEngine:  &QueryEngine{},
			tableManager: &TableManager{},
			expectError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			analyticsEngine, err := NewAnalyticsEngine(tt.config, tt.logger, tt.queryEngine, tt.tableManager)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, analyticsEngine)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, analyticsEngine)
			assert.Equal(t, tt.config, analyticsEngine.config)
			assert.Equal(t, tt.logger, analyticsEngine.logger)
			assert.Equal(t, tt.queryEngine, analyticsEngine.queryEngine)
			assert.Equal(t, tt.tableManager, analyticsEngine.tableManager)
		})
	}
}

func TestAnalyticsEngine_StartStop(t *testing.T) {
	// Create basic config
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Enabled: true,
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
			},
		},
	}

	logger, _ := zap.NewDevelopment()
	mockStorage := NewMockStorageClient()

	// Create real components with mock storage
	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skipf("Cannot create catalog for test: %v", err)
		return
	}

	tableManager, err := NewTableManager(cfg, logger, catalog, mockStorage)
	if err != nil {
		t.Skipf("Cannot create table manager for test: %v", err)
		return
	}

	queryEngine, err := NewQueryEngine(cfg, logger, catalog, tableManager)
	if err != nil {
		t.Skipf("Cannot create query engine for test: %v", err)
		return
	}

	// Create analytics engine
	engine, err := NewAnalyticsEngine(cfg, logger, queryEngine, tableManager)
	require.NoError(t, err)
	assert.NotNil(t, engine)

	// Test start
	err = engine.Start()
	assert.NoError(t, err)

	// Test double start (should return error)
	err = engine.Start()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already running")

	// Test stop
	err = engine.Stop()
	assert.NoError(t, err)

	// Test double stop (should be idempotent)
	err = engine.Stop()
	assert.NoError(t, err)
}

func TestAnalyticsEngine_RunAnalytics(t *testing.T) {
	// Create test setup
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Enabled: true,
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
			},
		},
	}

	logger, _ := zap.NewDevelopment()
	mockStorage := NewMockStorageClient()

	// Create components
	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skipf("Cannot create catalog for test: %v", err)
		return
	}

	tableManager, err := NewTableManager(cfg, logger, catalog, mockStorage)
	if err != nil {
		t.Skipf("Cannot create table manager for test: %v", err)
		return
	}

	queryEngine, err := NewQueryEngine(cfg, logger, catalog, tableManager)
	if err != nil {
		t.Skipf("Cannot create query engine for test: %v", err)
		return
	}

	engine, err := NewAnalyticsEngine(cfg, logger, queryEngine, tableManager)
	require.NoError(t, err)

	// Start engine
	err = engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	// Test analytics request validation
	ctx := context.Background()

	// Test nil request
	_, err = engine.RunAnalytics(ctx, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "request is required")

	// Test invalid request (missing job ID)
	invalidRequest := &AnalyticsRequest{
		JobType: "table_stats",
		Tables:  []string{"test.table"},
	}
	_, err = engine.RunAnalytics(ctx, invalidRequest)
	assert.Error(t, err)

	// Test valid request
	validRequest := &AnalyticsRequest{
		JobID:   "test-job-1",
		JobType: "table_stats",
		Tables:  []string{"test.table"},
		Parameters: map[string]interface{}{
			"include_histograms": true,
		},
	}

	result, err := engine.RunAnalytics(ctx, validRequest)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "test-job-1", result.JobID)
	assert.Equal(t, "running", result.Status)
}

func TestAnalyticsEngine_GetJob(t *testing.T) {
	// Create test engine
	engine := createTestAnalyticsEngine(t)
	if engine == nil {
		return // Skip if creation failed
	}

	err := engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	ctx := context.Background()

	// Test get non-existent job
	_, err = engine.GetJobStatus("non-existent-job")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")

	// Create a job first
	request := &AnalyticsRequest{
		JobID:   "test-job-1",
		JobType: "table_stats",
		Tables:  []string{"test.table"},
	}

	_, err = engine.RunAnalytics(ctx, request)
	require.NoError(t, err)

	// Now get the job
	job, err := engine.GetJobStatus("test-job-1")
	assert.NoError(t, err)
	assert.NotNil(t, job)
	assert.Equal(t, "test-job-1", job.ID)
	assert.Equal(t, "table_stats", job.Type)
}

func TestAnalyticsEngine_ListJobs(t *testing.T) {
	// Create test engine
	engine := createTestAnalyticsEngine(t)
	if engine == nil {
		return // Skip if creation failed
	}

	err := engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	ctx := context.Background()

	// Test list empty jobs
	jobs := engine.ListJobs()
	assert.Empty(t, jobs)

	// Create some jobs
	for i := 1; i <= 3; i++ {
		request := &AnalyticsRequest{
			JobID:   fmt.Sprintf("test-job-%d", i),
			JobType: "table_stats",
			Tables:  []string{"test.table"},
		}

		_, err = engine.RunAnalytics(ctx, request)
		require.NoError(t, err)
	}

	// List jobs
	jobs = engine.ListJobs()
	assert.Len(t, jobs, 3)
}

func TestAnalyticsEngine_CancelJob(t *testing.T) {
	// Create test engine
	engine := createTestAnalyticsEngine(t)
	if engine == nil {
		return // Skip if creation failed
	}

	err := engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	ctx := context.Background()

	// Test get non-existent job status
	_, err = engine.GetJobStatus("non-existent-job")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")

	// Create a job first
	request := &AnalyticsRequest{
		JobID:   "test-job-1",
		JobType: "table_stats",
		Tables:  []string{"test.table"},
	}

	result, err := engine.RunAnalytics(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, "test-job-1", result.JobID)

	// Verify job status
	job, err := engine.GetJobStatus("test-job-1")
	assert.NoError(t, err)
	assert.Equal(t, "test-job-1", job.ID)
	assert.Equal(t, "table_stats", job.Type)
	assert.Contains(t, []string{"running", "completed"}, job.Status)
}

func TestAnalyticsEngine_ConcurrentJobs(t *testing.T) {
	// Create test engine
	engine := createTestAnalyticsEngine(t)
	if engine == nil {
		return // Skip if creation failed
	}

	err := engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	ctx := context.Background()

	// Create multiple concurrent jobs
	numJobs := 5
	jobIDs := make([]string, numJobs)

	for i := 0; i < numJobs; i++ {
		jobID := fmt.Sprintf("concurrent-job-%d", i)
		jobIDs[i] = jobID

		request := &AnalyticsRequest{
			JobID:   jobID,
			JobType: "table_stats",
			Tables:  []string{"test.table"},
		}

		result, err := engine.RunAnalytics(ctx, request)
		assert.NoError(t, err)
		assert.Equal(t, jobID, result.JobID)
	}

	// Verify all jobs are tracked
	jobs := engine.ListJobs()
	assert.Len(t, jobs, numJobs)

	// Verify each job can be retrieved
	for _, jobID := range jobIDs {
		job, err := engine.GetJobStatus(jobID)
		assert.NoError(t, err)
		assert.Equal(t, jobID, job.ID)
	}
}

func TestAnalyticsEngine_ErrorHandling(t *testing.T) {
	// Create test engine
	engine := createTestAnalyticsEngine(t)
	if engine == nil {
		return // Skip if creation failed
	}

	err := engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	ctx := context.Background()

	// Test nil request
	_, err = engine.RunAnalytics(ctx, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "request is required")

	// Test empty job ID
	request := &AnalyticsRequest{
		JobID:   "",
		JobType: "table_stats",
		Tables:  []string{"test.table"},
	}
	_, err = engine.RunAnalytics(ctx, request)
	assert.Error(t, err)

	// Test invalid job type
	request = &AnalyticsRequest{
		JobID:   "test-job-1",
		JobType: "invalid_type",
		Tables:  []string{"test.table"},
	}
	result, err := engine.RunAnalytics(ctx, request)
	// Should not error on creation but job will fail during execution
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// Test duplicate job ID
	validRequest := &AnalyticsRequest{
		JobID:   "duplicate-job",
		JobType: "table_stats",
		Tables:  []string{"test.table"},
	}

	_, err = engine.RunAnalytics(ctx, validRequest)
	assert.NoError(t, err)

	// Try to create another job with same ID
	_, err = engine.RunAnalytics(ctx, validRequest)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already exists")
}

// Test analytics engine business logic that doesn't require external dependencies
func TestAnalyticsEngine_BusinessLogic(t *testing.T) {
	// Test analytics engine creation with valid configuration
	cfg := CreateValidAnalyticsEngineConfig()
	logger := zaptest.NewLogger(t)
	queryEngine := &QueryEngine{}
	tableManager := &TableManager{}

	ae, err := NewAnalyticsEngine(cfg, logger, queryEngine, tableManager)
	assert.NoError(t, err)
	assert.NotNil(t, ae)
	assert.Equal(t, cfg, ae.config)
	assert.Equal(t, logger, ae.logger)
	assert.Equal(t, queryEngine, ae.queryEngine)
	assert.Equal(t, tableManager, ae.tableManager)
	assert.False(t, ae.running)
	assert.NotNil(t, ae.jobs)
	assert.NotNil(t, ae.ctx)
	assert.NotNil(t, ae.cancel)
}

func TestAnalyticsEngine_ConfigValidation(t *testing.T) {
	tests := []struct {
		name         string
		config       *config.Config
		logger       *zap.Logger
		queryEngine  *QueryEngine
		tableManager *TableManager
		expectError  bool
		errorMsg     string
	}{
		{
			name:         "nil_config",
			config:       nil,
			logger:       zaptest.NewLogger(t),
			queryEngine:  &QueryEngine{},
			tableManager: &TableManager{},
			expectError:  true,
			errorMsg:     "config is required",
		},
		{
			name:         "nil_logger",
			config:       CreateValidAnalyticsEngineConfig(),
			logger:       nil,
			queryEngine:  &QueryEngine{},
			tableManager: &TableManager{},
			expectError:  true,
			errorMsg:     "logger is required",
		},
		{
			name:         "nil_query_engine",
			config:       CreateValidAnalyticsEngineConfig(),
			logger:       zaptest.NewLogger(t),
			queryEngine:  nil,
			tableManager: &TableManager{},
			expectError:  true,
			errorMsg:     "query engine is required",
		},
		{
			name:         "nil_table_manager",
			config:       CreateValidAnalyticsEngineConfig(),
			logger:       zaptest.NewLogger(t),
			queryEngine:  &QueryEngine{},
			tableManager: nil,
			expectError:  true,
			errorMsg:     "table manager is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ae, err := NewAnalyticsEngine(tt.config, tt.logger, tt.queryEngine, tt.tableManager)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, ae)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, ae)
			}
		})
	}
}

// Helper function to create a test analytics engine
func createTestAnalyticsEngine(t *testing.T) *AnalyticsEngine {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Enabled: true,
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
			},
		},
	}

	logger, _ := zap.NewDevelopment()
	mockStorage := NewMockStorageClient()

	// Create components
	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skipf("Cannot create catalog for test: %v", err)
		return nil
	}

	tableManager, err := NewTableManager(cfg, logger, catalog, mockStorage)
	if err != nil {
		t.Skipf("Cannot create table manager for test: %v", err)
		return nil
	}

	queryEngine, err := NewQueryEngine(cfg, logger, catalog, tableManager)
	if err != nil {
		t.Skipf("Cannot create query engine for test: %v", err)
		return nil
	}

	engine, err := NewAnalyticsEngine(cfg, logger, queryEngine, tableManager)
	if err != nil {
		t.Skipf("Cannot create analytics engine for test: %v", err)
		return nil
	}

	return engine
}

// Phase 5.2: Edge Case Coverage - Advanced edge case testing using mock behavior simulation
func TestAnalyticsEngine_EdgeCases_Advanced(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)

	// Create mock components with advanced behavior simulation
	mockSet := NewMockComponentSet()

	// Create analytics engine with mock components (using interface compatibility)
	queryEngine := &QueryEngine{
		config:       cfg,
		logger:       logger,
		catalog:      &Catalog{},
		tableManager: &TableManager{},
		running:      false,
	}

	tableManager := &TableManager{
		config:  cfg,
		logger:  logger,
		catalog: &Catalog{},
		storage: mockSet.Storage,
		running: false,
	}

	// Test with nil request edge cases
	engine := &AnalyticsEngine{
		config:       cfg,
		logger:       logger,
		queryEngine:  queryEngine,
		tableManager: tableManager,
		jobs:         make(map[string]*AnalyticsJob),
		running:      true,
	}

	ctx := context.Background()

	// Test RunAnalytics with various edge cases
	tests := []struct {
		name        string
		request     *AnalyticsRequest
		expectError bool
		errorMsg    string
	}{
		{
			name:        "nil_request",
			request:     nil,
			expectError: true,
			errorMsg:    "request is required",
		},
		{
			name: "empty_job_type",
			request: &AnalyticsRequest{
				JobID:   "test-job-1",
				JobType: "",
				Tables:  []string{"test.table"},
			},
			expectError: true,
			errorMsg:    "job type is required",
		},
		{
			name: "empty_tables",
			request: &AnalyticsRequest{
				JobID:   "test-job-2",
				JobType: "table_stats",
				Tables:  []string{},
			},
			expectError: true,
			errorMsg:    "at least one table is required",
		},
		{
			name: "valid_table_stats_job",
			request: &AnalyticsRequest{
				JobID:   "test-job-3",
				JobType: "table_stats",
				Tables:  []string{"test.table1", "test.table2"},
			},
			expectError: false,
		},
		{
			name: "valid_data_quality_job",
			request: &AnalyticsRequest{
				JobID:   "test-job-4",
				JobType: "data_quality",
				Tables:  []string{"test.table"},
				Parameters: map[string]interface{}{
					"checks": []string{"null_check", "duplicate_check"},
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := engine.RunAnalytics(ctx, tt.request)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.NotEmpty(t, result.JobID)
				assert.Equal(t, tt.request.JobID, result.JobID)
			}
		})
	}
}

func TestAnalyticsEngine_ConcurrencyEdgeCases(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)
	mockSet := NewMockComponentSet()

	queryEngine := &QueryEngine{
		config:       cfg,
		logger:       logger,
		catalog:      &Catalog{},
		tableManager: &TableManager{},
		running:      false,
	}

	tableManager := &TableManager{
		config:  cfg,
		logger:  logger,
		catalog: &Catalog{},
		storage: mockSet.Storage,
		running: false,
	}

	engine := &AnalyticsEngine{
		config:       cfg,
		logger:       logger,
		queryEngine:  queryEngine,
		tableManager: tableManager,
		jobs:         make(map[string]*AnalyticsJob),
		running:      true,
	}

	ctx := context.Background()

	// Test concurrent job submissions with same job ID
	jobID := "concurrent-test-job"
	request := &AnalyticsRequest{
		JobID:   jobID,
		JobType: "table_stats",
		Tables:  []string{"test.table"},
	}

	// Submit multiple jobs concurrently with same ID
	var wg sync.WaitGroup
	results := make([]*AnalyticsResult, 5)
	errors := make([]error, 5)

	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			results[index], errors[index] = engine.RunAnalytics(ctx, request)
		}(i)
	}

	wg.Wait()

	// Only one should succeed, others should fail due to duplicate job ID
	successCount := 0
	for i := 0; i < 5; i++ {
		if errors[i] == nil {
			successCount++
			assert.NotNil(t, results[i])
			assert.Equal(t, jobID, results[i].JobID)
		} else {
			assert.Contains(t, errors[i].Error(), "job already exists")
		}
	}

	assert.Equal(t, 1, successCount, "Only one job should succeed with duplicate ID")
}

func TestAnalyticsEngine_JobStatusEdgeCases(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)
	mockSet := NewMockComponentSet()

	queryEngine := &QueryEngine{
		config:       cfg,
		logger:       logger,
		catalog:      &Catalog{},
		tableManager: &TableManager{},
		running:      false,
	}

	tableManager := &TableManager{
		config:  cfg,
		logger:  logger,
		catalog: &Catalog{},
		storage: mockSet.Storage,
		running: false,
	}

	engine := &AnalyticsEngine{
		config:       cfg,
		logger:       logger,
		queryEngine:  queryEngine,
		tableManager: tableManager,
		jobs:         make(map[string]*AnalyticsJob),
		running:      true,
	}

	// Test GetJobStatus with non-existent job
	status, err := engine.GetJobStatus("non-existent-job")
	assert.Error(t, err)
	assert.Nil(t, status)
	assert.Contains(t, err.Error(), "job not found")

	// Test GetJobStatus with empty job ID
	status, err = engine.GetJobStatus("")
	assert.Error(t, err)
	assert.Nil(t, status)
	assert.Contains(t, err.Error(), "job ID is required")

	// Test ListJobs when no jobs exist
	jobs := engine.ListJobs()
	assert.NotNil(t, jobs)
	assert.Equal(t, 0, len(jobs))
}

// Phase 5.3: Performance Testing
func TestAnalyticsEngine_PerformanceTesting(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)
	ctx := context.Background()

	// Create real components with mock storage for performance testing
	mockStorage := NewMockStorageClient()

	// Create catalog
	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skipf("Cannot create catalog for performance test: %v", err)
		return
	}

	// Create table manager
	tableManager, err := NewTableManager(cfg, logger, catalog, mockStorage)
	if err != nil {
		t.Skipf("Cannot create table manager for performance test: %v", err)
		return
	}

	// Create query engine
	queryEngine, err := NewQueryEngine(cfg, logger, catalog, tableManager)
	if err != nil {
		t.Skipf("Cannot create query engine for performance test: %v", err)
		return
	}

	engine := &AnalyticsEngine{
		config:       cfg,
		logger:       logger,
		queryEngine:  queryEngine,
		tableManager: tableManager,
		jobs:         make(map[string]*AnalyticsJob),
		running:      true,
	}

	t.Run("concurrent_job_submissions", func(t *testing.T) {
		const numJobs = 50
		const numWorkers = 10

		jobChan := make(chan int, numJobs)
		resultChan := make(chan error, numJobs)

		// Fill job channel
		for i := 0; i < numJobs; i++ {
			jobChan <- i
		}
		close(jobChan)

		// Start workers
		for w := 0; w < numWorkers; w++ {
			go func() {
				for jobID := range jobChan {
					request := &AnalyticsRequest{
						JobID:   fmt.Sprintf("perf-job-%d", jobID),
						JobType: "data_quality",
						Tables:  []string{"test.table1"},
					}

					_, err := engine.RunAnalytics(ctx, request)
					resultChan <- err
				}
			}()
		}

		// Collect results
		successCount := 0
		for i := 0; i < numJobs; i++ {
			err := <-resultChan
			if err == nil {
				successCount++
			}
		}

		// Verify performance
		assert.Greater(t, successCount, numJobs/2, "At least half of jobs should succeed")

		// Verify job tracking
		jobs := engine.ListJobs()
		assert.LessOrEqual(t, len(jobs), numJobs, "Should not exceed submitted jobs")
	})

	t.Run("high_volume_status_checks", func(t *testing.T) {
		// Submit a few jobs first
		for i := 0; i < 5; i++ {
			request := &AnalyticsRequest{
				JobID:   fmt.Sprintf("status-job-%d", i),
				JobType: "data_quality",
				Tables:  []string{"test.table1"},
			}
			_, err := engine.RunAnalytics(ctx, request)
			assert.NoError(t, err)
		}

		// Perform high-volume status checks
		const numChecks = 200
		const numWorkers = 20

		checkChan := make(chan string, numChecks)
		resultChan := make(chan bool, numChecks)

		// Fill check channel with job IDs
		for i := 0; i < numChecks; i++ {
			jobID := fmt.Sprintf("status-job-%d", i%5) // Cycle through existing jobs
			checkChan <- jobID
		}
		close(checkChan)

		// Start workers
		for w := 0; w < numWorkers; w++ {
			go func() {
				for jobID := range checkChan {
					job, err := engine.GetJobStatus(jobID)
					success := (err == nil && job != nil) || (err != nil && strings.Contains(err.Error(), "not found"))
					resultChan <- success
				}
			}()
		}

		// Collect results
		successCount := 0
		for i := 0; i < numChecks; i++ {
			if <-resultChan {
				successCount++
			}
		}

		// All status checks should succeed (either find job or return not found)
		assert.Equal(t, numChecks, successCount, "All status checks should succeed")
	})

	t.Run("memory_usage_under_load", func(t *testing.T) {
		// Submit many jobs to test memory usage
		const numJobs = 100

		for i := 0; i < numJobs; i++ {
			request := &AnalyticsRequest{
				JobID:   fmt.Sprintf("memory-job-%d", i),
				JobType: "data_quality",
				Tables:  []string{"test.table1", "test.table2"},
			}

			_, err := engine.RunAnalytics(ctx, request)
			if err != nil && !strings.Contains(err.Error(), "already exists") {
				t.Logf("Job %d failed: %v", i, err)
			}
		}

		// Verify engine is still responsive
		jobs := engine.ListJobs()
		assert.LessOrEqual(t, len(jobs), numJobs, "Should not exceed submitted jobs")

		// Test that we can still submit new jobs
		request := &AnalyticsRequest{
			JobID:   "final-memory-test-job",
			JobType: "data_quality",
			Tables:  []string{"test.table1"},
		}

		_, err := engine.RunAnalytics(ctx, request)
		assert.NoError(t, err, "Engine should still be responsive after load test")
	})
}

// Phase 5.4: Error Path Coverage - Comprehensive error handling testing
func TestAnalyticsEngine_ErrorPathCoverage(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)
	ctx := context.Background()

	t.Run("engine_not_running_errors", func(t *testing.T) {
		// Create engine but don't start it
		mockStorage := NewMockStorageClient()
		catalog, _ := NewCatalog(cfg, logger, mockStorage)
		tableManager, _ := NewTableManager(cfg, logger, catalog, mockStorage)
		queryEngine, _ := NewQueryEngine(cfg, logger, catalog, tableManager)

		engine := &AnalyticsEngine{
			config:       cfg,
			logger:       logger,
			queryEngine:  queryEngine,
			tableManager: tableManager,
			jobs:         make(map[string]*AnalyticsJob),
			running:      false, // Not running
		}

		// Test operations on non-running engine
		request := &AnalyticsRequest{
			JobID:   "test-job",
			JobType: "data_quality",
			Tables:  []string{"test.table"},
		}

		_, err := engine.RunAnalytics(ctx, request)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not running")
	})

	t.Run("invalid_job_types", func(t *testing.T) {
		mockStorage := NewMockStorageClient()
		catalog, _ := NewCatalog(cfg, logger, mockStorage)
		tableManager, _ := NewTableManager(cfg, logger, catalog, mockStorage)
		queryEngine, _ := NewQueryEngine(cfg, logger, catalog, tableManager)

		engine := &AnalyticsEngine{
			config:       cfg,
			logger:       logger,
			queryEngine:  queryEngine,
			tableManager: tableManager,
			jobs:         make(map[string]*AnalyticsJob),
			running:      true,
		}

		// Test various invalid job types
		invalidJobTypes := []string{
			"invalid_type",
			"",
			"unknown_job",
			"malformed-type",
		}

		for _, jobType := range invalidJobTypes {
			request := &AnalyticsRequest{
				JobID:   fmt.Sprintf("test-job-%s", jobType),
				JobType: jobType,
				Tables:  []string{"test.table"},
			}

			if jobType == "" {
				// Empty job type should fail validation
				_, err := engine.RunAnalytics(ctx, request)
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "job type is required")
			} else {
				// Invalid job types should be accepted but may fail during execution
				result, err := engine.RunAnalytics(ctx, request)
				if err == nil {
					assert.NotNil(t, result)
					// Job should be created but may fail during execution
				}
			}
		}
	})

	t.Run("malformed_table_names", func(t *testing.T) {
		mockStorage := NewMockStorageClient()
		catalog, _ := NewCatalog(cfg, logger, mockStorage)
		tableManager, _ := NewTableManager(cfg, logger, catalog, mockStorage)
		queryEngine, _ := NewQueryEngine(cfg, logger, catalog, tableManager)

		engine := &AnalyticsEngine{
			config:       cfg,
			logger:       logger,
			queryEngine:  queryEngine,
			tableManager: tableManager,
			jobs:         make(map[string]*AnalyticsJob),
			running:      true,
		}

		// Test various malformed table names
		malformedTables := [][]string{
			{""},               // Empty table name
			{"invalid..table"}, // Double dots
			{"table with spaces"},
			{"table@with#special$chars"},
			{"very.long.namespace.with.many.parts.table"},
		}

		for i, tables := range malformedTables {
			request := &AnalyticsRequest{
				JobID:   fmt.Sprintf("malformed-table-job-%d", i),
				JobType: "data_quality",
				Tables:  tables,
			}

			// These should be accepted but may fail during execution
			result, err := engine.RunAnalytics(ctx, request)
			if err == nil {
				assert.NotNil(t, result)
			}
			// Some malformed names might be caught during validation
		}
	})

	t.Run("context_cancellation", func(t *testing.T) {
		mockStorage := NewMockStorageClient()
		catalog, _ := NewCatalog(cfg, logger, mockStorage)
		tableManager, _ := NewTableManager(cfg, logger, catalog, mockStorage)
		queryEngine, _ := NewQueryEngine(cfg, logger, catalog, tableManager)

		engine := &AnalyticsEngine{
			config:       cfg,
			logger:       logger,
			queryEngine:  queryEngine,
			tableManager: tableManager,
			jobs:         make(map[string]*AnalyticsJob),
			running:      true,
		}

		// Create a context that's already cancelled
		cancelledCtx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		request := &AnalyticsRequest{
			JobID:   "cancelled-context-job",
			JobType: "data_quality",
			Tables:  []string{"test.table"},
		}

		// Should handle cancelled context gracefully
		result, err := engine.RunAnalytics(cancelledCtx, request)
		// May succeed in creating job but fail during execution
		if err != nil {
			assert.Contains(t, err.Error(), "context")
		} else {
			assert.NotNil(t, result)
		}
	})

	t.Run("concurrent_duplicate_jobs", func(t *testing.T) {
		mockStorage := NewMockStorageClient()
		catalog, _ := NewCatalog(cfg, logger, mockStorage)
		tableManager, _ := NewTableManager(cfg, logger, catalog, mockStorage)
		queryEngine, _ := NewQueryEngine(cfg, logger, catalog, tableManager)

		engine := &AnalyticsEngine{
			config:       cfg,
			logger:       logger,
			queryEngine:  queryEngine,
			tableManager: tableManager,
			jobs:         make(map[string]*AnalyticsJob),
			running:      true,
		}

		// Test race condition with duplicate job IDs
		const numWorkers = 10
		jobID := "duplicate-job-test"

		var wg sync.WaitGroup
		results := make([]*AnalyticsResult, numWorkers)
		errors := make([]error, numWorkers)

		for i := 0; i < numWorkers; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()
				request := &AnalyticsRequest{
					JobID:   jobID,
					JobType: "data_quality",
					Tables:  []string{"test.table"},
				}
				results[index], errors[index] = engine.RunAnalytics(ctx, request)
			}(i)
		}

		wg.Wait()

		// Only one should succeed, others should fail with duplicate error
		successCount := 0
		duplicateErrorCount := 0

		for i := 0; i < numWorkers; i++ {
			if errors[i] == nil {
				successCount++
				assert.NotNil(t, results[i])
			} else if strings.Contains(errors[i].Error(), "already exists") {
				duplicateErrorCount++
			}
		}

		assert.Equal(t, 1, successCount, "Only one job should succeed")
		assert.Greater(t, duplicateErrorCount, 0, "Should have duplicate job errors")
	})
}

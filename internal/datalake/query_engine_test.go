package datalake

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"go.uber.org/zap/zaptest"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

func TestNewQueryEngine(t *testing.T) {
	tests := []struct {
		name         string
		config       *config.Config
		logger       *zap.Logger
		catalog      *Catalog
		tableManager *TableManager
		expectError  bool
	}{
		{
			name:         "nil_config",
			config:       nil,
			logger:       zaptest.NewLogger(t),
			catalog:      &Catalog{},
			tableManager: &TableManager{},
			expectError:  true,
		},
		{
			name:         "nil_logger",
			config:       CreateValidQueryEngineConfig(),
			logger:       nil,
			catalog:      &Catalog{},
			tableManager: &TableManager{},
			expectError:  true,
		},
		{
			name:         "nil_catalog",
			config:       CreateValidQueryEngineConfig(),
			logger:       zaptest.NewLogger(t),
			catalog:      nil,
			tableManager: &TableManager{},
			expectError:  true,
		},
		{
			name:         "nil_table_manager",
			config:       CreateValidQueryEngineConfig(),
			logger:       zaptest.NewLogger(t),
			catalog:      &Catalog{},
			tableManager: nil,
			expectError:  true,
		},
		{
			name:         "valid_configuration",
			config:       CreateValidQueryEngineConfig(),
			logger:       zaptest.NewLogger(t),
			catalog:      &Catalog{},
			tableManager: &TableManager{},
			expectError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			queryEngine, err := NewQueryEngine(tt.config, tt.logger, tt.catalog, tt.tableManager)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, queryEngine)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, queryEngine)
			assert.Equal(t, tt.config, queryEngine.config)
			assert.Equal(t, tt.logger, queryEngine.logger)
			assert.Equal(t, tt.catalog, queryEngine.catalog)
			assert.Equal(t, tt.tableManager, queryEngine.tableManager)
		})
	}
}

func TestQueryEngine_StartStop(t *testing.T) {
	// Create test query engine
	engine := createTestQueryEngine(t)
	if engine == nil {
		return // Skip if creation failed
	}

	// Test start
	err := engine.Start()
	assert.NoError(t, err)

	// Test double start (should return error)
	err = engine.Start()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already running")

	// Test stop
	err = engine.Stop()
	assert.NoError(t, err)

	// Test double stop (should be idempotent)
	err = engine.Stop()
	assert.NoError(t, err)
}

func TestQueryEngine_ParseQuery(t *testing.T) {
	// Create test query engine
	engine := createTestQueryEngine(t)
	if engine == nil {
		return // Skip if creation failed
	}

	err := engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	tests := []struct {
		name        string
		sql         string
		expectError bool
		queryType   string
	}{
		{
			name:        "select_query",
			sql:         "SELECT * FROM test.table",
			expectError: false,
			queryType:   "SELECT",
		},
		{
			name:        "show_tables",
			sql:         "SHOW TABLES",
			expectError: false,
			queryType:   "SHOW",
		},
		{
			name:        "describe_table",
			sql:         "DESCRIBE test.table",
			expectError: false,
			queryType:   "DESCRIBE",
		},
		{
			name:        "empty_query",
			sql:         "",
			expectError: true,
			queryType:   "",
		},
		{
			name:        "invalid_sql",
			sql:         "INVALID SQL STATEMENT",
			expectError: false, // Parser is lenient
			queryType:   "UNKNOWN",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			queryInfo, err := engine.parseQuery(tt.sql)

			if tt.expectError {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, queryInfo)
			assert.Equal(t, tt.sql, queryInfo.Raw)
			if tt.queryType != "" {
				assert.Equal(t, tt.queryType, queryInfo.Type)
			}
		})
	}
}

func TestQueryEngine_ExecuteShowQuery(t *testing.T) {
	// Create test query engine
	engine := createTestQueryEngine(t)
	if engine == nil {
		return // Skip if creation failed
	}

	err := engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	ctx := context.Background()

	tests := []struct {
		name        string
		sql         string
		expectError bool
	}{
		{
			name:        "show_tables",
			sql:         "SHOW TABLES",
			expectError: false,
		},
		{
			name:        "show_namespaces",
			sql:         "SHOW NAMESPACES",
			expectError: false,
		},
		{
			name:        "show_tables_in_namespace",
			sql:         "SHOW TABLES IN test",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			request := &QueryRequest{
				SQL: tt.sql,
			}

			result, err := engine.ExecuteQuery(ctx, request)

			if tt.expectError {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, result)
			assert.NotNil(t, result.Columns)
			assert.NotNil(t, result.Rows)
			assert.GreaterOrEqual(t, result.RowCount, int64(0))
		})
	}
}

func TestQueryEngine_ExecuteSelectQuery(t *testing.T) {
	// Create test query engine
	engine := createTestQueryEngine(t)
	if engine == nil {
		return // Skip if creation failed
	}

	err := engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	ctx := context.Background()

	tests := []struct {
		name        string
		sql         string
		expectError bool
	}{
		{
			name:        "simple_select",
			sql:         "SELECT * FROM test.table",
			expectError: false,
		},
		{
			name:        "select_with_where",
			sql:         "SELECT id, name FROM test.table WHERE id > 0",
			expectError: false,
		},
		{
			name:        "select_count",
			sql:         "SELECT COUNT(*) FROM test.table",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			request := &QueryRequest{
				SQL: tt.sql,
			}

			result, err := engine.ExecuteQuery(ctx, request)

			if tt.expectError {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, result)
			assert.NotNil(t, result.Columns)
			assert.NotNil(t, result.Rows)
			assert.GreaterOrEqual(t, result.RowCount, int64(0))
		})
	}
}

func TestQueryEngine_QueryCaching(t *testing.T) {
	// Create test query engine
	engine := createTestQueryEngine(t)
	if engine == nil {
		return // Skip if creation failed
	}

	err := engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	ctx := context.Background()

	// Execute the same query twice
	request := &QueryRequest{
		SQL: "SELECT * FROM test.table",
	}

	// First execution
	result1, err := engine.ExecuteQuery(ctx, request)
	assert.NoError(t, err)
	assert.NotNil(t, result1)

	// Second execution (should use cache)
	result2, err := engine.ExecuteQuery(ctx, request)
	assert.NoError(t, err)
	assert.NotNil(t, result2)

	// Results should be identical
	assert.Equal(t, len(result1.Columns), len(result2.Columns))
	assert.Equal(t, len(result1.Rows), len(result2.Rows))
	assert.Equal(t, result1.RowCount, result2.RowCount)
}

func TestQueryEngine_ConcurrentQueries(t *testing.T) {
	// Create test query engine
	engine := createTestQueryEngine(t)
	if engine == nil {
		return // Skip if creation failed
	}

	err := engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	ctx := context.Background()

	// Execute multiple queries concurrently
	numQueries := 5
	results := make(chan *QueryResult, numQueries)
	errors := make(chan error, numQueries)

	for i := 0; i < numQueries; i++ {
		go func(queryID int) {
			request := &QueryRequest{
				SQL: "SELECT * FROM test.table",
			}

			result, err := engine.ExecuteQuery(ctx, request)
			if err != nil {
				errors <- err
			} else {
				results <- result
			}
		}(i)
	}

	// Collect results
	successCount := 0
	errorCount := 0

	for i := 0; i < numQueries; i++ {
		select {
		case result := <-results:
			assert.NotNil(t, result)
			successCount++
		case err := <-errors:
			assert.NoError(t, err) // We expect no errors
			errorCount++
		}
	}

	assert.Equal(t, numQueries, successCount)
	assert.Equal(t, 0, errorCount)
}

func TestQueryEngine_ErrorHandling(t *testing.T) {
	// Create test query engine
	engine := createTestQueryEngine(t)
	if engine == nil {
		return // Skip if creation failed
	}

	err := engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	ctx := context.Background()

	// Test nil request
	_, err = engine.ExecuteQuery(ctx, nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "request is required")

	// Test empty SQL
	request := &QueryRequest{
		SQL: "",
	}
	_, err = engine.ExecuteQuery(ctx, request)
	assert.Error(t, err)

	// Test malformed SQL (should still work with lenient parser)
	request = &QueryRequest{
		SQL: "COMPLETELY INVALID SQL STATEMENT",
	}
	result, err := engine.ExecuteQuery(ctx, request)
	// The query engine should handle this gracefully
	assert.NoError(t, err)
	assert.NotNil(t, result)
}

// Test query engine business logic that doesn't require external dependencies
func TestQueryEngine_BusinessLogic(t *testing.T) {
	// Test query engine creation with valid configuration
	cfg := CreateValidQueryEngineConfig()
	logger := zaptest.NewLogger(t)
	catalog := &Catalog{}
	tableManager := &TableManager{}

	qe, err := NewQueryEngine(cfg, logger, catalog, tableManager)
	assert.NoError(t, err)
	assert.NotNil(t, qe)
	assert.Equal(t, cfg, qe.config)
	assert.Equal(t, logger, qe.logger)
	assert.Equal(t, catalog, qe.catalog)
	assert.Equal(t, tableManager, qe.tableManager)
	assert.False(t, qe.running)
	assert.NotNil(t, qe.queryCache)
	assert.NotNil(t, qe.ctx)
	assert.NotNil(t, qe.cancel)
}

func TestQueryEngine_ConfigValidation(t *testing.T) {
	tests := []struct {
		name         string
		config       *config.Config
		logger       *zap.Logger
		catalog      *Catalog
		tableManager *TableManager
		expectError  bool
		errorMsg     string
	}{
		{
			name:         "nil_config",
			config:       nil,
			logger:       zaptest.NewLogger(t),
			catalog:      &Catalog{},
			tableManager: &TableManager{},
			expectError:  true,
			errorMsg:     "config is required",
		},
		{
			name:         "nil_logger",
			config:       CreateValidQueryEngineConfig(),
			logger:       nil,
			catalog:      &Catalog{},
			tableManager: &TableManager{},
			expectError:  true,
			errorMsg:     "logger is required",
		},
		{
			name:         "nil_catalog",
			config:       CreateValidQueryEngineConfig(),
			logger:       zaptest.NewLogger(t),
			catalog:      nil,
			tableManager: &TableManager{},
			expectError:  true,
			errorMsg:     "catalog is required",
		},
		{
			name:         "nil_table_manager",
			config:       CreateValidQueryEngineConfig(),
			logger:       zaptest.NewLogger(t),
			catalog:      &Catalog{},
			tableManager: nil,
			expectError:  true,
			errorMsg:     "table manager is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			qe, err := NewQueryEngine(tt.config, tt.logger, tt.catalog, tt.tableManager)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, qe)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, qe)
			}
		})
	}
}

// Helper function to create a test query engine
func createTestQueryEngine(t *testing.T) *QueryEngine {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Enabled: true,
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
			},
		},
	}

	logger, _ := zap.NewDevelopment()
	mockStorage := NewMockStorageClient()

	// Create components
	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skipf("Cannot create catalog for test: %v", err)
		return nil
	}

	tableManager, err := NewTableManager(cfg, logger, catalog, mockStorage)
	if err != nil {
		t.Skipf("Cannot create table manager for test: %v", err)
		return nil
	}

	engine, err := NewQueryEngine(cfg, logger, catalog, tableManager)
	if err != nil {
		t.Skipf("Cannot create query engine for test: %v", err)
		return nil
	}

	return engine
}

// Phase 5.2: Edge Case Coverage - Advanced edge case testing for QueryEngine
func TestQueryEngine_EdgeCases_Advanced(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)
	mockSet := NewMockComponentSet()

	// Create query engine with mock components
	catalog := &Catalog{
		config:  cfg,
		logger:  logger,
		storage: mockSet.Storage,
		running: false,
	}

	tableManager := &TableManager{
		config:  cfg,
		logger:  logger,
		catalog: catalog,
		storage: mockSet.Storage,
		running: false,
	}

	engine := &QueryEngine{
		config:       cfg,
		logger:       logger,
		catalog:      catalog,
		tableManager: tableManager,
		queryCache:   make(map[string]*CachedQuery),
		running:      true,
	}

	ctx := context.Background()

	// Test ExecuteQuery with various edge cases
	tests := []struct {
		name        string
		request     *QueryRequest
		expectError bool
		errorMsg    string
	}{
		{
			name:        "nil_request",
			request:     nil,
			expectError: true,
			errorMsg:    "request is required",
		},
		{
			name: "empty_sql",
			request: &QueryRequest{
				SQL: "",
			},
			expectError: true,
			errorMsg:    "SQL query is required",
		},
		{
			name: "whitespace_only_sql",
			request: &QueryRequest{
				SQL: "   \t\n   ",
			},
			expectError: true,
			errorMsg:    "SQL query is required",
		},
		{
			name: "valid_select_query",
			request: &QueryRequest{
				SQL: "SHOW TABLES",
			},
			expectError: false,
		},
		{
			name: "valid_show_tables",
			request: &QueryRequest{
				SQL: "SHOW TABLES",
			},
			expectError: false,
		},
		{
			name: "sql_injection_attempt",
			request: &QueryRequest{
				SQL: "SHOW TABLES; DROP TABLE test.table; --",
			},
			expectError: false, // Should be handled gracefully
		},
		{
			name: "extremely_long_sql",
			request: &QueryRequest{
				SQL: "SHOW TABLES " + strings.Repeat("-- comment ", 100),
			},
			expectError: false, // Should handle large queries
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := engine.ExecuteQuery(ctx, tt.request)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

func TestQueryEngine_CacheEdgeCases(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)
	mockSet := NewMockComponentSet()

	catalog := &Catalog{
		config:  cfg,
		logger:  logger,
		storage: mockSet.Storage,
		running: false,
	}

	tableManager := &TableManager{
		config:  cfg,
		logger:  logger,
		catalog: catalog,
		storage: mockSet.Storage,
		running: false,
	}

	engine := &QueryEngine{
		config:       cfg,
		logger:       logger,
		catalog:      catalog,
		tableManager: tableManager,
		queryCache:   make(map[string]*CachedQuery),
		running:      true,
	}

	ctx := context.Background()

	// Test cache behavior with identical queries (using SHOW TABLES which works)
	request1 := &QueryRequest{SQL: "SHOW TABLES"}
	request2 := &QueryRequest{SQL: "SHOW TABLES"}
	request3 := &QueryRequest{SQL: "show tables"} // Different case

	// Execute first query
	result1, err := engine.ExecuteQuery(ctx, request1)
	assert.NoError(t, err)
	assert.NotNil(t, result1)

	// Execute identical query (should use cache)
	result2, err := engine.ExecuteQuery(ctx, request2)
	assert.NoError(t, err)
	assert.NotNil(t, result2)

	// Execute case-different query (should be treated as different)
	result3, err := engine.ExecuteQuery(ctx, request3)
	assert.NoError(t, err)
	assert.NotNil(t, result3)

	// Verify cache behavior
	assert.Equal(t, len(result1.Columns), len(result2.Columns))
	assert.Equal(t, result1.RowCount, result2.RowCount)
}

// Phase 5.3: Performance Testing for QueryEngine
func TestQueryEngine_PerformanceTesting(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)
	ctx := context.Background()

	// Create real components with mock storage for performance testing
	mockStorage := NewMockStorageClient()

	// Create catalog
	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skipf("Cannot create catalog for performance test: %v", err)
		return
	}

	// Create table manager
	tableManager, err := NewTableManager(cfg, logger, catalog, mockStorage)
	if err != nil {
		t.Skipf("Cannot create table manager for performance test: %v", err)
		return
	}

	// Create query engine
	engine, err := NewQueryEngine(cfg, logger, catalog, tableManager)
	if err != nil {
		t.Skipf("Cannot create query engine for performance test: %v", err)
		return
	}

	t.Run("concurrent_query_execution", func(t *testing.T) {
		const numQueries = 100
		const numWorkers = 20

		queryChan := make(chan int, numQueries)
		resultChan := make(chan error, numQueries)

		// Fill query channel
		for i := 0; i < numQueries; i++ {
			queryChan <- i
		}
		close(queryChan)

		// Start workers
		for w := 0; w < numWorkers; w++ {
			go func() {
				for queryID := range queryChan {
					request := &QueryRequest{
						SQL: fmt.Sprintf("SHOW TABLES -- query %d", queryID),
					}

					_, err := engine.ExecuteQuery(ctx, request)
					resultChan <- err
				}
			}()
		}

		// Collect results
		successCount := 0
		for i := 0; i < numQueries; i++ {
			err := <-resultChan
			if err == nil {
				successCount++
			}
		}

		// Verify performance - most queries should succeed
		assert.Greater(t, successCount, numQueries*3/4, "At least 75% of queries should succeed")
	})

	t.Run("high_volume_simple_queries", func(t *testing.T) {
		const numQueries = 200

		successCount := 0
		for i := 0; i < numQueries; i++ {
			request := &QueryRequest{
				SQL: "SHOW TABLES",
			}

			result, err := engine.ExecuteQuery(ctx, request)
			if err == nil && result != nil {
				successCount++
			}
		}

		// All simple queries should succeed
		assert.Equal(t, numQueries, successCount, "All simple queries should succeed")
	})

	t.Run("query_cache_performance", func(t *testing.T) {
		const numRepeats = 50

		// Execute the same query multiple times to test cache performance
		request := &QueryRequest{
			SQL: "SHOW TABLES",
		}

		var firstResult *QueryResult
		successCount := 0

		for i := 0; i < numRepeats; i++ {
			result, err := engine.ExecuteQuery(ctx, request)
			if err == nil && result != nil {
				successCount++
				if i == 0 {
					firstResult = result
				} else {
					// Subsequent queries should have similar results (cache behavior)
					assert.Equal(t, len(firstResult.Columns), len(result.Columns), "Cached queries should have same column count")
					assert.Equal(t, firstResult.RowCount, result.RowCount, "Cached queries should have same row count")
				}
			}
		}

		// All cached queries should succeed
		assert.Equal(t, numRepeats, successCount, "All cached queries should succeed")
	})
}

// Phase 5.4: Error Path Coverage for QueryEngine
func TestQueryEngine_ErrorPathCoverage(t *testing.T) {
	cfg := CreateValidTestConfig()
	logger := zaptest.NewLogger(t)
	ctx := context.Background()

	t.Run("engine_not_running_errors", func(t *testing.T) {
		// Create engine but don't start it
		mockStorage := NewMockStorageClient()
		catalog, _ := NewCatalog(cfg, logger, mockStorage)
		tableManager, _ := NewTableManager(cfg, logger, catalog, mockStorage)

		engine := &QueryEngine{
			config:       cfg,
			logger:       logger,
			catalog:      catalog,
			tableManager: tableManager,
			queryCache:   make(map[string]*CachedQuery),
			running:      false, // Not running
		}

		// Test operations on non-running engine
		request := &QueryRequest{
			SQL: "SELECT * FROM test.table",
		}

		_, err := engine.ExecuteQuery(ctx, request)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not running")
	})

	t.Run("malformed_sql_queries", func(t *testing.T) {
		// Create a minimal engine setup to test parsing errors
		engine := &QueryEngine{
			config:       cfg,
			logger:       logger,
			catalog:      nil, // Intentionally nil to avoid execution
			tableManager: nil, // Intentionally nil to avoid execution
			queryCache:   make(map[string]*CachedQuery),
			running:      true,
		}

		// Test various malformed SQL queries that should fail at parse level
		malformedQueries := []string{
			"INVALID SQL SYNTAX",
			"",    // Empty query (should be caught by validation)
			"   ", // Whitespace only (should be caught by validation)
		}

		for i, sql := range malformedQueries {
			t.Run(fmt.Sprintf("malformed_query_%d", i), func(t *testing.T) {
				request := &QueryRequest{
					SQL: sql,
				}

				result, err := engine.ExecuteQuery(ctx, request)
				// Should handle malformed queries gracefully - either parse error or execution error
				assert.Error(t, err, "Malformed query should fail")
				assert.Nil(t, result, "Result should be nil for failed query")
				t.Logf("Query failed as expected: %v", err)
			})
		}
	})

	t.Run("nil_request_validation", func(t *testing.T) {
		// Test nil request validation
		engine := &QueryEngine{
			config:       cfg,
			logger:       logger,
			catalog:      nil,
			tableManager: nil,
			queryCache:   make(map[string]*CachedQuery),
			running:      true,
		}

		// Test nil request
		result, err := engine.ExecuteQuery(ctx, nil)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "query request is required")
	})

	t.Run("empty_sql_validation", func(t *testing.T) {
		// Test empty SQL validation
		engine := &QueryEngine{
			config:       cfg,
			logger:       logger,
			catalog:      nil,
			tableManager: nil,
			queryCache:   make(map[string]*CachedQuery),
			running:      true,
		}

		// Test empty SQL
		request := &QueryRequest{
			SQL: "",
		}
		result, err := engine.ExecuteQuery(ctx, request)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "SQL query is required")

		// Test whitespace-only SQL
		request = &QueryRequest{
			SQL: "   \t\n  ",
		}
		result, err = engine.ExecuteQuery(ctx, request)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "SQL query is required")
	})
}

package datalake

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

func TestNewCatalog(t *testing.T) {
	tests := []struct {
		name        string
		config      *config.Config
		expectError bool
	}{
		{
			name:        "nil config",
			config:      nil,
			expectError: true,
		},
		{
			name: "missing catalog config",
			config: &config.Config{
				DataLake: config.DataLakeConfig{},
			},
			expectError: true,
		},
		{
			name: "unsupported catalog type",
			config: &config.Config{
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "unsupported",
					},
				},
			},
			expectError: true,
		},
		{
			name: "empty_catalog_type",
			config: &config.Config{
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "",
						URI:  "http://localhost:8181",
					},
				},
			},
			expectError: true,
		},
		{
			name: "empty_catalog_uri",
			config: &config.Config{
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "rest",
						URI:  "",
					},
				},
			},
			expectError: true,
		},
		{
			name: "valid_rest_catalog_config",
			config: &config.Config{
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "rest",
						URI:  "http://localhost:8181",
						Properties: map[string]string{
							"warehouse": "s3://test-bucket/warehouse",
						},
					},
				},
			},
			expectError: false, // May skip due to external dependencies
		},
		{
			name: "valid_glue_catalog_config",
			config: &config.Config{
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "glue",
						URI:  "glue://us-east-1",
						Properties: map[string]string{
							"warehouse": "s3://test-bucket/warehouse",
						},
					},
				},
			},
			expectError: false, // May skip due to external dependencies
		},
		{
			name: "valid_sql_catalog_config",
			config: &config.Config{
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "sql",
						URI:  "postgresql://localhost:5432/iceberg",
						Properties: map[string]string{
							"warehouse": "s3://test-bucket/warehouse",
						},
					},
				},
			},
			expectError: false, // May skip due to external dependencies
		},
		{
			name: "catalog_with_custom_properties",
			config: &config.Config{
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "rest",
						URI:  "http://localhost:8181",
						Properties: map[string]string{
							"warehouse":            "s3://test-bucket/warehouse",
							"io-impl":              "org.apache.iceberg.aws.s3.S3FileIO",
							"s3.endpoint":          "http://localhost:9000",
							"s3.path-style-access": "true",
						},
					},
				},
			},
			expectError: false, // May skip due to external dependencies
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			logger := zaptest.NewLogger(t)
			mockStorage := NewMockStorageClient()

			catalog, err := NewCatalog(tt.config, logger, mockStorage)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, catalog)
				return
			}

			// For valid configs, we expect either success or skip due to external dependencies
			if err != nil {
				t.Skipf("Skipping test due to external dependency: %v", err)
				return
			}

			assert.NotNil(t, catalog)
			assert.Equal(t, tt.config, catalog.config)
			assert.NotNil(t, catalog.storage)
			assert.NotNil(t, catalog.logger)
		})
	}
}

func TestCatalog_StartStop(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
	logger := zaptest.NewLogger(t)
	mockStorage := &MockStorageClient{}

	// This will fail because we don't have a real REST catalog
	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skip("Skipping test because catalog creation failed (expected without real catalog)")
	}

	// Test start
	err = catalog.Start()
	if err != nil {
		t.Skip("Skipping test because catalog start failed (expected without real catalog)")
	}
	assert.True(t, catalog.running)

	// Test start when already running
	err = catalog.Start()
	assert.Error(t, err)

	// Test stop
	err = catalog.Stop()
	assert.NoError(t, err)
	assert.False(t, catalog.running)

	// Test stop when not running
	err = catalog.Stop()
	assert.NoError(t, err)
}

func TestCatalog_CreateNamespace(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
	logger := zaptest.NewLogger(t)
	mockStorage := &MockStorageClient{}

	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skip("Skipping test because catalog creation failed (expected without real catalog)")
	}

	ctx := context.Background()
	properties := map[string]string{
		"description": "test namespace",
	}

	// This would fail because we don't have a real catalog
	err = catalog.CreateNamespace(ctx, "test_namespace", properties)
	assert.Error(t, err)
}

func TestCatalog_CreateTable(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
	logger := zaptest.NewLogger(t)
	mockStorage := &MockStorageClient{}

	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skip("Skipping test because catalog creation failed (expected without real catalog)")
	}

	ctx := context.Background()
	request := &CreateTableRequest{
		Namespace:  "test_namespace",
		TableName:  "test_table",
		Schema:     nil, // Would be a proper schema in real usage
		Properties: map[string]string{"test": "value"},
	}

	// This would fail because we don't have a real catalog and namespace
	_, err = catalog.CreateTable(ctx, request)
	assert.Error(t, err)
}

func TestCatalog_Health(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
	logger := zaptest.NewLogger(t)
	mockStorage := &MockStorageClient{}

	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skip("Skipping test because catalog creation failed (expected without real catalog)")
	}

	ctx := context.Background()

	// Test health when not running
	err = catalog.Health(ctx)
	assert.Error(t, err)

	// Start catalog (this would fail with real catalog)
	err = catalog.Start()
	if err != nil {
		t.Skip("Skipping health test because catalog start failed")
	}
	defer catalog.Stop()

	// Test health when running (would fail because no real catalog)
	err = catalog.Health(ctx)
	assert.Error(t, err)
}

func TestCatalog_ListNamespaces(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
	logger := zaptest.NewLogger(t)
	mockStorage := &MockStorageClient{}

	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skip("Skipping test because catalog creation failed (expected without real catalog)")
	}

	ctx := context.Background()

	namespaces, err := catalog.ListNamespaces(ctx)
	assert.NoError(t, err)
	assert.NotNil(t, namespaces)
	assert.Equal(t, 0, len(namespaces)) // Empty because no real catalog
}

func TestCatalog_DropNamespace(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
	logger := zaptest.NewLogger(t)
	mockStorage := &MockStorageClient{}

	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skip("Skipping test because catalog creation failed (expected without real catalog)")
	}

	ctx := context.Background()

	// This would fail because namespace doesn't exist
	err = catalog.DropNamespace(ctx, "nonexistent_namespace")
	assert.Error(t, err)
}

func TestCatalog_GetTable(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
	logger := zaptest.NewLogger(t)
	mockStorage := &MockStorageClient{}

	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skip("Skipping test because catalog creation failed (expected without real catalog)")
	}

	ctx := context.Background()

	// This would fail because table doesn't exist
	_, err = catalog.GetTable(ctx, "test_namespace", "test_table")
	assert.Error(t, err)
}

func TestCatalog_ListTables(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
	logger := zaptest.NewLogger(t)
	mockStorage := &MockStorageClient{}

	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skip("Skipping test because catalog creation failed (expected without real catalog)")
	}

	ctx := context.Background()

	tables, err := catalog.ListTables(ctx, "test_namespace")
	assert.NoError(t, err)
	assert.NotNil(t, tables)
	assert.Equal(t, 0, len(tables)) // Empty because no real catalog
}

func TestCatalog_DropTable(t *testing.T) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
	logger := zaptest.NewLogger(t)
	mockStorage := &MockStorageClient{}

	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skip("Skipping test because catalog creation failed (expected without real catalog)")
	}

	ctx := context.Background()

	// This would fail because table doesn't exist
	err = catalog.DropTable(ctx, "test_namespace", "test_table")
	assert.Error(t, err)
}

// Integration test helper functions

func setupTestCatalog(t *testing.T) (*Catalog, func()) {
	cfg := &config.Config{
		DataLake: config.DataLakeConfig{
			Catalog: config.CatalogConfig{
				Type: "rest",
				URI:  "http://localhost:8181",
				Properties: map[string]string{
					"warehouse": "s3://test-bucket/warehouse",
				},
			},
		},
	}
	logger := zaptest.NewLogger(t)
	mockStorage := &MockStorageClient{}

	catalog, err := NewCatalog(cfg, logger, mockStorage)
	if err != nil {
		t.Skip("Skipping test because catalog creation failed (expected without real catalog)")
	}

	cleanup := func() {
		if catalog != nil && catalog.running {
			catalog.Stop()
		}
	}

	return catalog, cleanup
}

func TestCatalog_Integration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	catalog, cleanup := setupTestCatalog(t)
	defer cleanup()

	ctx := context.Background()

	// Test the full workflow (would fail without real catalog)
	// 1. Create namespace
	err := catalog.CreateNamespace(ctx, "integration_test", map[string]string{
		"description": "Integration test namespace",
	})
	assert.Error(t, err) // Expected to fail without real catalog

	// 2. List namespaces
	namespaces, err := catalog.ListNamespaces(ctx)
	assert.NoError(t, err)
	assert.NotNil(t, namespaces)

	// 3. Create table (would fail)
	request := &CreateTableRequest{
		Namespace:  "integration_test",
		TableName:  "test_table",
		Schema:     nil,
		Properties: map[string]string{"test": "value"},
	}
	_, err = catalog.CreateTable(ctx, request)
	assert.Error(t, err) // Expected to fail

	// 4. List tables
	tables, err := catalog.ListTables(ctx, "integration_test")
	assert.NoError(t, err)
	assert.NotNil(t, tables)
}

// Test catalog business logic that doesn't require external dependencies
func TestCatalog_BusinessLogic(t *testing.T) {
	// Test catalog creation with valid configuration
	cfg := CreateValidCatalogConfig()
	logger := zaptest.NewLogger(t)
	storageClient := NewMockStorageClient()

	// This will fail due to external dependency, but we can test the validation logic
	catalog, err := NewCatalog(cfg, logger, storageClient)
	if err != nil {
		// Expected - external dependency not available
		assert.Contains(t, err.Error(), "failed to create iceberg catalog")
		return
	}

	// If we somehow get a catalog (shouldn't happen in unit tests), test basic properties
	assert.NotNil(t, catalog)
	assert.Equal(t, cfg, catalog.config)
	assert.Equal(t, logger, catalog.logger)
	assert.Equal(t, storageClient, catalog.storage)
}

func TestCatalog_ConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *config.Config
		expectError bool
		errorMsg    string
	}{
		{
			name:        "nil_config",
			config:      nil,
			expectError: true,
			errorMsg:    "config is required",
		},
		{
			name: "missing_datalake_config",
			config: &config.Config{
				ObjectStorage: config.ObjectStorageConfig{
					Provider: "s3",
					Bucket:   "test-bucket",
				},
			},
			expectError: true,
			errorMsg:    "unsupported catalog type:",
		},
		{
			name: "empty_catalog_type",
			config: &config.Config{
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "",
						URI:  "http://localhost:8181",
					},
				},
			},
			expectError: true,
			errorMsg:    "unsupported catalog type:",
		},
		{
			name: "empty_catalog_uri",
			config: &config.Config{
				DataLake: config.DataLakeConfig{
					Catalog: config.CatalogConfig{
						Type: "rest",
						URI:  "",
					},
				},
			},
			expectError: true,
			errorMsg:    "failed to create iceberg catalog:",
		},
	}

	logger := zaptest.NewLogger(t)
	storageClient := NewMockStorageClient()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			catalog, err := NewCatalog(tt.config, logger, storageClient)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, catalog)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				// Even valid configs will fail due to external dependencies
				// but we're testing the validation logic
				if err != nil {
					assert.Contains(t, err.Error(), "failed to create iceberg catalog")
				}
			}
		})
	}
}

// Test catalog state management methods that don't require external dependencies
func TestCatalog_StateManagement(t *testing.T) {
	cfg := CreateValidCatalogConfig()
	logger := zaptest.NewLogger(t)
	storageClient := NewMockStorageClient()

	// Create catalog manually to avoid external dependency
	catalog := &Catalog{
		config:     cfg,
		logger:     logger,
		storage:    storageClient,
		namespaces: make(map[string]*CatalogNamespace),
		tables:     make(map[string]*Table),
		running:    false,
	}

	// Test initial state
	assert.False(t, catalog.running)
	assert.NotNil(t, catalog.namespaces)
	assert.NotNil(t, catalog.tables)
	assert.Equal(t, 0, len(catalog.namespaces))
	assert.Equal(t, 0, len(catalog.tables))

	// Test adding namespaces to local cache
	namespace1 := &CatalogNamespace{
		Name: "test_namespace_1",
		Properties: map[string]string{
			"description": "Test namespace 1",
		},
	}
	namespace2 := &CatalogNamespace{
		Name: "test_namespace_2",
		Properties: map[string]string{
			"description": "Test namespace 2",
		},
	}

	catalog.namespaces["test_namespace_1"] = namespace1
	catalog.namespaces["test_namespace_2"] = namespace2

	assert.Equal(t, 2, len(catalog.namespaces))
	assert.Equal(t, namespace1, catalog.namespaces["test_namespace_1"])
	assert.Equal(t, namespace2, catalog.namespaces["test_namespace_2"])

	// Test adding tables to local cache
	table1 := &Table{
		ID:        "table-1",
		Namespace: "test_namespace_1",
		Name:      "events",
		Snapshots: []Snapshot{},
	}
	table2 := &Table{
		ID:        "table-2",
		Namespace: "test_namespace_2",
		Name:      "logs",
		Snapshots: []Snapshot{},
	}

	catalog.tables["test_namespace_1.events"] = table1
	catalog.tables["test_namespace_2.logs"] = table2

	assert.Equal(t, 2, len(catalog.tables))
	assert.Equal(t, table1, catalog.tables["test_namespace_1.events"])
	assert.Equal(t, table2, catalog.tables["test_namespace_2.logs"])
}

func TestCatalog_ListTablesLocal(t *testing.T) {
	cfg := CreateValidCatalogConfig()
	logger := zaptest.NewLogger(t)
	storageClient := NewMockStorageClient()
	ctx := context.Background()

	// Create catalog with local tables
	catalog := &Catalog{
		config:     cfg,
		logger:     logger,
		storage:    storageClient,
		namespaces: make(map[string]*CatalogNamespace),
		tables:     make(map[string]*Table),
		running:    true,
	}

	// Add test tables to different namespaces
	table1 := &Table{
		ID:        "table-1",
		Namespace: "analytics",
		Name:      "events",
		Snapshots: []Snapshot{},
	}
	table2 := &Table{
		ID:        "table-2",
		Namespace: "analytics",
		Name:      "metrics",
		Snapshots: []Snapshot{},
	}
	table3 := &Table{
		ID:        "table-3",
		Namespace: "logs",
		Name:      "application",
		Snapshots: []Snapshot{},
	}

	catalog.tables["analytics.events"] = table1
	catalog.tables["analytics.metrics"] = table2
	catalog.tables["logs.application"] = table3

	// Test ListTables for analytics namespace
	analyticsTables, err := catalog.ListTables(ctx, "analytics")
	assert.NoError(t, err)
	assert.Equal(t, 2, len(analyticsTables))

	// Verify the tables are from the correct namespace
	for _, table := range analyticsTables {
		assert.Equal(t, "analytics", table.Namespace)
	}

	// Test ListTables for logs namespace
	logsTables, err := catalog.ListTables(ctx, "logs")
	assert.NoError(t, err)
	assert.Equal(t, 1, len(logsTables))
	assert.Equal(t, "logs", logsTables[0].Namespace)
	assert.Equal(t, "application", logsTables[0].Name)

	// Test ListTables for non-existent namespace
	emptyTables, err := catalog.ListTables(ctx, "nonexistent")
	assert.NoError(t, err)
	assert.Equal(t, 0, len(emptyTables))
}

func TestCatalog_ErrorHandling(t *testing.T) {
	cfg := CreateValidCatalogConfig()
	logger := zaptest.NewLogger(t)
	storageClient := NewMockStorageClient()
	ctx := context.Background()

	// Create catalog with local state
	catalog := &Catalog{
		config:     cfg,
		logger:     logger,
		storage:    storageClient,
		namespaces: make(map[string]*CatalogNamespace),
		tables:     make(map[string]*Table),
		running:    false, // Not running
	}

	// Test operations when not running
	err := catalog.Health(ctx)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "catalog is not running")

	// Test with running state but no external catalog
	catalog.running = true

	// Health check should fail due to missing external catalog
	err = catalog.Health(ctx)
	assert.Error(t, err) // Will fail due to nil catalog.catalog
	assert.Contains(t, err.Error(), "catalog health check failed")
}

func TestCatalog_ConcurrentAccess(t *testing.T) {
	cfg := CreateValidCatalogConfig()
	logger := zaptest.NewLogger(t)
	storageClient := NewMockStorageClient()
	ctx := context.Background()

	catalog := &Catalog{
		config:     cfg,
		logger:     logger,
		storage:    storageClient,
		namespaces: make(map[string]*CatalogNamespace),
		tables:     make(map[string]*Table),
		running:    true,
	}

	// Test concurrent access to ListTables
	done := make(chan bool, 2)

	go func() {
		defer func() { done <- true }()
		for i := 0; i < 10; i++ {
			tables, err := catalog.ListTables(ctx, "test_namespace")
			assert.NoError(t, err)
			assert.NotNil(t, tables)
		}
	}()

	go func() {
		defer func() { done <- true }()
		for i := 0; i < 10; i++ {
			// Add tables concurrently using proper locking
			catalog.mu.Lock()
			table := &Table{
				ID:        fmt.Sprintf("table-%d", i),
				Namespace: "test_namespace",
				Name:      fmt.Sprintf("table_%d", i),
				Snapshots: []Snapshot{},
			}
			catalog.tables[fmt.Sprintf("test_namespace.table_%d", i)] = table
			catalog.mu.Unlock()
		}
	}()

	// Wait for both goroutines to complete
	<-done
	<-done

	// Verify final state
	tables, err := catalog.ListTables(ctx, "test_namespace")
	assert.NoError(t, err)
	assert.LessOrEqual(t, len(tables), 10) // May be less due to race conditions
}

func TestCatalog_EdgeCases(t *testing.T) {
	cfg := CreateValidCatalogConfig()
	logger := zaptest.NewLogger(t)
	storageClient := NewMockStorageClient()
	ctx := context.Background()

	catalog := &Catalog{
		config:     cfg,
		logger:     logger,
		storage:    storageClient,
		namespaces: make(map[string]*CatalogNamespace),
		tables:     make(map[string]*Table),
		running:    true,
	}

	// Test ListTables with nil namespace
	tables, err := catalog.ListTables(ctx, "")
	assert.NoError(t, err)
	assert.Equal(t, 0, len(tables))

	// Test with very long namespace name
	longNamespace := strings.Repeat("a", 1000)
	tables, err = catalog.ListTables(ctx, longNamespace)
	assert.NoError(t, err)
	assert.Equal(t, 0, len(tables))

	// Test with special characters in namespace
	specialNamespace := "test-namespace_with.special@chars"
	tables, err = catalog.ListTables(ctx, specialNamespace)
	assert.NoError(t, err)
	assert.Equal(t, 0, len(tables))
}

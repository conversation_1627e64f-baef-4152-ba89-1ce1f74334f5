package datalake

import (
	"testing"
	"time"

	"github.com/apache/iceberg-go"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestGenerateID(t *testing.T) {
	// Test that GenerateID returns a valid UUID
	id := GenerateID()
	assert.NotEmpty(t, id)

	// Verify it's a valid UUID
	_, err := uuid.Parse(id)
	assert.NoError(t, err)

	// Test that multiple calls return different IDs
	id2 := GenerateID()
	assert.NotEqual(t, id, id2)
}

func TestTable_Exists(t *testing.T) {
	tests := []struct {
		name     string
		table    *Table
		expected bool
	}{
		{
			name: "table with ID exists",
			table: &Table{
				ID:        "test-id",
				Namespace: "test",
				Name:      "table",
			},
			expected: true,
		},
		{
			name: "table without ID does not exist",
			table: &Table{
				ID:        "",
				Namespace: "test",
				Name:      "table",
			},
			expected: false,
		},
		{
			name:     "empty table does not exist",
			table:    &Table{},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.table.Exists()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestTable_IsEmpty(t *testing.T) {
	tests := []struct {
		name     string
		table    *Table
		expected bool
	}{
		{
			name: "table with no snapshots is empty",
			table: &Table{
				ID:        "test-id",
				Snapshots: []Snapshot{},
			},
			expected: true,
		},
		{
			name: "table with snapshots is not empty",
			table: &Table{
				ID: "test-id",
				Snapshots: []Snapshot{
					{ID: 1, TimestampMs: time.Now().UnixMilli()},
				},
			},
			expected: false,
		},
		{
			name: "table with nil snapshots is empty",
			table: &Table{
				ID:        "test-id",
				Snapshots: nil,
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.table.IsEmpty()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestTable_GetCurrentSnapshot(t *testing.T) {
	tests := []struct {
		name     string
		table    *Table
		expected *Snapshot
	}{
		{
			name: "table with no snapshots returns nil",
			table: &Table{
				ID:        "test-id",
				Snapshots: []Snapshot{},
			},
			expected: nil,
		},
		{
			name: "table with one snapshot returns that snapshot",
			table: &Table{
				ID: "test-id",
				Snapshots: []Snapshot{
					{ID: 1, TimestampMs: time.Now().UnixMilli()},
				},
			},
			expected: &Snapshot{ID: 1},
		},
		{
			name: "table with multiple snapshots returns the last one",
			table: &Table{
				ID: "test-id",
				Snapshots: []Snapshot{
					{ID: 1, TimestampMs: time.Now().Add(-time.Hour).UnixMilli()},
					{ID: 2, TimestampMs: time.Now().Add(-30 * time.Minute).UnixMilli()},
					{ID: 3, TimestampMs: time.Now().UnixMilli()},
				},
			},
			expected: &Snapshot{ID: 3},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.table.GetCurrentSnapshot()
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, tt.expected.ID, result.ID)
			}
		})
	}
}

func TestTable_GetSnapshotByID(t *testing.T) {
	table := &Table{
		ID: "test-id",
		Snapshots: []Snapshot{
			{ID: 1, TimestampMs: time.Now().Add(-time.Hour).UnixMilli()},
			{ID: 2, TimestampMs: time.Now().Add(-30 * time.Minute).UnixMilli()},
			{ID: 3, TimestampMs: time.Now().UnixMilli()},
		},
	}

	tests := []struct {
		name       string
		snapshotID int64
		expected   *Snapshot
	}{
		{
			name:       "existing snapshot ID returns snapshot",
			snapshotID: 2,
			expected:   &Snapshot{ID: 2},
		},
		{
			name:       "non-existing snapshot ID returns nil",
			snapshotID: 999,
			expected:   nil,
		},
		{
			name:       "first snapshot",
			snapshotID: 1,
			expected:   &Snapshot{ID: 1},
		},
		{
			name:       "last snapshot",
			snapshotID: 3,
			expected:   &Snapshot{ID: 3},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := table.GetSnapshotByID(tt.snapshotID)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, tt.expected.ID, result.ID)
			}
		})
	}
}

func TestWriteMode_String(t *testing.T) {
	tests := []struct {
		mode     WriteMode
		expected string
	}{
		{WriteModeAppend, "append"},
		{WriteModeOverwrite, "overwrite"},
		{WriteMode("unknown"), "unknown"},
	}

	for _, tt := range tests {
		t.Run(string(tt.mode), func(t *testing.T) {
			result := string(tt.mode)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCreateTableRequest_Validation(t *testing.T) {
	schema := iceberg.NewSchema(0,
		iceberg.NestedField{
			ID:       1,
			Name:     "id",
			Type:     iceberg.PrimitiveTypes.Int64,
			Required: true,
		},
	)

	tests := []struct {
		name    string
		request *CreateTableRequest
		valid   bool
	}{
		{
			name: "valid request",
			request: &CreateTableRequest{
				Namespace: "test_namespace",
				TableName: "test_table",
				Schema:    schema,
				Properties: map[string]string{
					"description": "Test table",
				},
			},
			valid: true,
		},
		{
			name: "missing namespace",
			request: &CreateTableRequest{
				Namespace: "",
				TableName: "test_table",
				Schema:    schema,
			},
			valid: false,
		},
		{
			name: "missing table name",
			request: &CreateTableRequest{
				Namespace: "test_namespace",
				TableName: "",
				Schema:    schema,
			},
			valid: false,
		},
		{
			name: "missing schema",
			request: &CreateTableRequest{
				Namespace: "test_namespace",
				TableName: "test_table",
				Schema:    nil,
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Basic validation checks
			hasNamespace := tt.request.Namespace != ""
			hasTableName := tt.request.TableName != ""
			hasSchema := tt.request.Schema != nil

			isValid := hasNamespace && hasTableName && hasSchema
			assert.Equal(t, tt.valid, isValid)
		})
	}
}

func TestWriteDataRequest_Validation(t *testing.T) {
	records := []map[string]interface{}{
		{"id": 1, "name": "test"},
	}

	tests := []struct {
		name    string
		request *WriteDataRequest
		valid   bool
	}{
		{
			name: "valid request",
			request: &WriteDataRequest{
				Namespace: "test_namespace",
				TableName: "test_table",
				Records:   records,
				WriteMode: WriteModeAppend,
			},
			valid: true,
		},
		{
			name: "missing namespace",
			request: &WriteDataRequest{
				Namespace: "",
				TableName: "test_table",
				Records:   records,
				WriteMode: WriteModeAppend,
			},
			valid: false,
		},
		{
			name: "missing table name",
			request: &WriteDataRequest{
				Namespace: "test_namespace",
				TableName: "",
				Records:   records,
				WriteMode: WriteModeAppend,
			},
			valid: false,
		},
		{
			name: "empty records",
			request: &WriteDataRequest{
				Namespace: "test_namespace",
				TableName: "test_table",
				Records:   []map[string]interface{}{},
				WriteMode: WriteModeAppend,
			},
			valid: false,
		},
		{
			name: "nil records",
			request: &WriteDataRequest{
				Namespace: "test_namespace",
				TableName: "test_table",
				Records:   nil,
				WriteMode: WriteModeAppend,
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Basic validation checks
			hasNamespace := tt.request.Namespace != ""
			hasTableName := tt.request.TableName != ""
			hasRecords := tt.request.Records != nil && len(tt.request.Records) > 0

			isValid := hasNamespace && hasTableName && hasRecords
			assert.Equal(t, tt.valid, isValid)
		})
	}
}

func TestReadDataRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		request *ReadDataRequest
		valid   bool
	}{
		{
			name: "valid request",
			request: func() *ReadDataRequest {
				limit := int64(100)
				return &ReadDataRequest{
					Namespace: "test_namespace",
					TableName: "test_table",
					Filter:    "id > 0",
					Limit:     &limit,
				}
			}(),
			valid: true,
		},
		{
			name: "valid request without filter",
			request: func() *ReadDataRequest {
				limit := int64(100)
				return &ReadDataRequest{
					Namespace: "test_namespace",
					TableName: "test_table",
					Limit:     &limit,
				}
			}(),
			valid: true,
		},
		{
			name: "missing namespace",
			request: &ReadDataRequest{
				Namespace: "",
				TableName: "test_table",
			},
			valid: false,
		},
		{
			name: "missing table name",
			request: &ReadDataRequest{
				Namespace: "test_namespace",
				TableName: "",
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Basic validation checks
			hasNamespace := tt.request.Namespace != ""
			hasTableName := tt.request.TableName != ""

			isValid := hasNamespace && hasTableName
			assert.Equal(t, tt.valid, isValid)
		})
	}
}

func TestQueryRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		request *QueryRequest
		valid   bool
	}{
		{
			name: "valid SELECT query",
			request: &QueryRequest{
				SQL: "SELECT * FROM test.table",
			},
			valid: true,
		},
		{
			name: "valid SHOW query",
			request: &QueryRequest{
				SQL: "SHOW NAMESPACES",
			},
			valid: true,
		},
		{
			name: "empty SQL",
			request: &QueryRequest{
				SQL: "",
			},
			valid: false,
		},
		{
			name: "whitespace only SQL",
			request: &QueryRequest{
				SQL: "   ",
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Basic validation checks
			hasSQL := len(tt.request.SQL) > 0 && len(tt.request.SQL) != len("   ")

			assert.Equal(t, tt.valid, hasSQL)
		})
	}
}

func TestAnalyticsRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		request *AnalyticsRequest
		valid   bool
	}{
		{
			name: "valid table stats request",
			request: &AnalyticsRequest{
				JobID:   "job-1",
				JobType: "table_stats",
				Tables:  []string{"test.table"},
			},
			valid: true,
		},
		{
			name: "valid data quality request",
			request: &AnalyticsRequest{
				JobID:   "job-2",
				JobType: "data_quality",
				Tables:  []string{"test.table1", "test.table2"},
			},
			valid: true,
		},
		{
			name: "missing job ID",
			request: &AnalyticsRequest{
				JobID:   "",
				JobType: "table_stats",
				Tables:  []string{"test.table"},
			},
			valid: false,
		},
		{
			name: "missing job type",
			request: &AnalyticsRequest{
				JobID:   "job-1",
				JobType: "",
				Tables:  []string{"test.table"},
			},
			valid: false,
		},
		{
			name: "empty tables",
			request: &AnalyticsRequest{
				JobID:   "job-1",
				JobType: "table_stats",
				Tables:  []string{},
			},
			valid: false,
		},
		{
			name: "nil tables",
			request: &AnalyticsRequest{
				JobID:   "job-1",
				JobType: "table_stats",
				Tables:  nil,
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Basic validation checks
			hasJobID := tt.request.JobID != ""
			hasJobType := tt.request.JobType != ""
			hasTables := tt.request.Tables != nil && len(tt.request.Tables) > 0

			isValid := hasJobID && hasJobType && hasTables
			assert.Equal(t, tt.valid, isValid)
		})
	}
}

package archival

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// ComplianceReporter handles compliance reporting and audit trails
type ComplianceReporter struct {
	config   *config.Config
	logger   *zap.Logger
	events   []ComplianceEvent
	reports  []ComplianceReport
	running  bool
	ctx      context.Context
	cancel   context.CancelFunc
	mu       sync.RWMutex
}

// ComplianceEvent represents a compliance-related event
type ComplianceEvent struct {
	ID           string                 `json:"id"`
	Type         string                 `json:"type"` // archive, delete, access, policy_change
	Source       string                 `json:"source"`
	DataType     string                 `json:"data_type"`
	Timestamp    time.Time              `json:"timestamp"`
	User         string                 `json:"user,omitempty"`
	Action       string                 `json:"action"`
	Result       string                 `json:"result"` // success, failure, partial
	Policy       *RetentionPolicy       `json:"policy,omitempty"`
	Compliance   *ComplianceRequirements `json:"compliance,omitempty"`
	Details      map[string]interface{} `json:"details,omitempty"`
	Error        string                 `json:"error,omitempty"`
}

// ComplianceReport represents a compliance report
type ComplianceReport struct {
	ID              string            `json:"id"`
	GeneratedAt     time.Time         `json:"generated_at"`
	Period          ReportPeriod      `json:"period"`
	Events          []ComplianceEvent `json:"events"`
	Summary         ReportSummary     `json:"summary"`
	Violations      []Violation       `json:"violations"`
	Recommendations []string          `json:"recommendations"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// ReportPeriod defines the time period for a compliance report
type ReportPeriod struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Duration  string    `json:"duration"`
}

// ReportSummary provides summary statistics for a compliance report
type ReportSummary struct {
	TotalEvents         int                        `json:"total_events"`
	EventsByType        map[string]int             `json:"events_by_type"`
	EventsBySource      map[string]int             `json:"events_by_source"`
	EventsByCompliance  map[string]int             `json:"events_by_compliance"`
	SuccessfulEvents    int                        `json:"successful_events"`
	FailedEvents        int                        `json:"failed_events"`
	TotalViolations     int                        `json:"total_violations"`
	ViolationsByType    map[string]int             `json:"violations_by_type"`
	DataClassifications map[string]int             `json:"data_classifications"`
	RegulationsCovered  []string                   `json:"regulations_covered"`
}

// Violation represents a compliance violation
type Violation struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"` // retention_exceeded, encryption_missing, audit_failed
	Severity    string                 `json:"severity"` // low, medium, high, critical
	Description string                 `json:"description"`
	Source      string                 `json:"source"`
	DataType    string                 `json:"data_type"`
	Timestamp   time.Time              `json:"timestamp"`
	Policy      *RetentionPolicy       `json:"policy,omitempty"`
	Compliance  *ComplianceRequirements `json:"compliance,omitempty"`
	Resolution  string                 `json:"resolution,omitempty"`
	Status      string                 `json:"status"` // open, investigating, resolved, closed
	Details     map[string]interface{} `json:"details,omitempty"`
}

// NewComplianceReporter creates a new compliance reporter
func NewComplianceReporter(cfg *config.Config, logger *zap.Logger) (*ComplianceReporter, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &ComplianceReporter{
		config:  cfg,
		logger:  logger,
		events:  make([]ComplianceEvent, 0),
		reports: make([]ComplianceReport, 0),
		ctx:     ctx,
		cancel:  cancel,
	}, nil
}

// Start starts the compliance reporter
func (cr *ComplianceReporter) Start() error {
	if cr.running {
		return fmt.Errorf("compliance reporter is already running")
	}

	cr.running = true
	cr.logger.Info("Starting compliance reporter")

	return nil
}

// Stop stops the compliance reporter
func (cr *ComplianceReporter) Stop() error {
	if !cr.running {
		return nil
	}

	cr.logger.Info("Stopping compliance reporter")
	cr.cancel()
	cr.running = false

	return nil
}

// RecordEvent records a compliance event
func (cr *ComplianceReporter) RecordEvent(request *ArchivalRequest, policy *RetentionPolicy) error {
	cr.mu.Lock()
	defer cr.mu.Unlock()

	event := ComplianceEvent{
		ID:         fmt.Sprintf("event-%d", time.Now().UnixNano()),
		Type:       "archive",
		Source:     request.Source,
		DataType:   request.DataType,
		Timestamp:  time.Now(),
		Action:     "data_archived",
		Result:     "success",
		Policy:     policy,
		Compliance: request.Compliance,
		Details: map[string]interface{}{
			"request_id": request.ID,
			"data_size":  len(request.Data),
		},
	}

	cr.events = append(cr.events, event)

	cr.logger.Debug("Recorded compliance event",
		zap.String("event_id", event.ID),
		zap.String("type", event.Type),
		zap.String("source", event.Source),
		zap.String("data_type", event.DataType),
	)

	return nil
}

// RecordViolation records a compliance violation
func (cr *ComplianceReporter) RecordViolation(violation Violation) error {
	cr.mu.Lock()
	defer cr.mu.Unlock()

	event := ComplianceEvent{
		ID:        fmt.Sprintf("violation-%d", time.Now().UnixNano()),
		Type:      "violation",
		Source:    violation.Source,
		DataType:  violation.DataType,
		Timestamp: time.Now(),
		Action:    "compliance_violation",
		Result:    "failure",
		Details: map[string]interface{}{
			"violation_id":   violation.ID,
			"violation_type": violation.Type,
			"severity":       violation.Severity,
			"description":    violation.Description,
		},
	}

	cr.events = append(cr.events, event)

	cr.logger.Warn("Recorded compliance violation",
		zap.String("violation_id", violation.ID),
		zap.String("type", violation.Type),
		zap.String("severity", violation.Severity),
		zap.String("source", violation.Source),
	)

	return nil
}

// GenerateReport generates a compliance report for the specified period
func (cr *ComplianceReporter) GenerateReport() (*ComplianceReport, error) {
	cr.mu.RLock()
	defer cr.mu.RUnlock()

	now := time.Now()
	startTime := now.Add(-24 * time.Hour) // Last 24 hours

	// Filter events for the period
	var periodEvents []ComplianceEvent
	var violations []Violation

	for _, event := range cr.events {
		if event.Timestamp.After(startTime) && event.Timestamp.Before(now) {
			periodEvents = append(periodEvents, event)

			// Check for violations
			if event.Type == "violation" {
				violation := Violation{
					ID:          event.ID,
					Type:        event.Details["violation_type"].(string),
					Severity:    event.Details["severity"].(string),
					Description: event.Details["description"].(string),
					Source:      event.Source,
					DataType:    event.DataType,
					Timestamp:   event.Timestamp,
					Status:      "open",
				}
				violations = append(violations, violation)
			}
		}
	}

	// Generate summary
	summary := cr.generateSummary(periodEvents, violations)

	// Generate recommendations
	recommendations := cr.generateRecommendations(summary, violations)

	report := &ComplianceReport{
		ID:          fmt.Sprintf("report-%d", now.UnixNano()),
		GeneratedAt: now,
		Period: ReportPeriod{
			StartTime: startTime,
			EndTime:   now,
			Duration:  "24h",
		},
		Events:          periodEvents,
		Summary:         summary,
		Violations:      violations,
		Recommendations: recommendations,
	}

	cr.reports = append(cr.reports, *report)

	cr.logger.Info("Generated compliance report",
		zap.String("report_id", report.ID),
		zap.Int("total_events", len(periodEvents)),
		zap.Int("violations", len(violations)),
	)

	return report, nil
}

// generateSummary generates summary statistics for events
func (cr *ComplianceReporter) generateSummary(events []ComplianceEvent, violations []Violation) ReportSummary {
	summary := ReportSummary{
		TotalEvents:         len(events),
		EventsByType:        make(map[string]int),
		EventsBySource:      make(map[string]int),
		EventsByCompliance:  make(map[string]int),
		TotalViolations:     len(violations),
		ViolationsByType:    make(map[string]int),
		DataClassifications: make(map[string]int),
		RegulationsCovered:  make([]string, 0),
	}

	regulationsSet := make(map[string]bool)

	for _, event := range events {
		// Count by type
		summary.EventsByType[event.Type]++

		// Count by source
		summary.EventsBySource[event.Source]++

		// Count by result
		if event.Result == "success" {
			summary.SuccessfulEvents++
		} else {
			summary.FailedEvents++
		}

		// Process compliance requirements
		if event.Compliance != nil {
			summary.DataClassifications[event.Compliance.Classification]++

			for _, regulation := range event.Compliance.Regulations {
				if !regulationsSet[regulation] {
					regulationsSet[regulation] = true
					summary.RegulationsCovered = append(summary.RegulationsCovered, regulation)
				}
			}
		}
	}

	for _, violation := range violations {
		summary.ViolationsByType[violation.Type]++
	}

	return summary
}

// generateRecommendations generates recommendations based on report data
func (cr *ComplianceReporter) generateRecommendations(summary ReportSummary, violations []Violation) []string {
	var recommendations []string

	// Check failure rate
	if summary.FailedEvents > 0 {
		failureRate := float64(summary.FailedEvents) / float64(summary.TotalEvents) * 100
		if failureRate > 5.0 {
			recommendations = append(recommendations,
				fmt.Sprintf("High failure rate detected (%.1f%%). Review archival processes and error handling.", failureRate))
		}
	}

	// Check for violations
	if summary.TotalViolations > 0 {
		recommendations = append(recommendations,
			fmt.Sprintf("Found %d compliance violations. Review and address immediately.", summary.TotalViolations))
	}

	// Check for missing encryption
	for _, violation := range violations {
		if violation.Type == "encryption_missing" {
			recommendations = append(recommendations,
				"Enable encryption for all confidential and restricted data classifications.")
			break
		}
	}

	// Check for retention policy violations
	for _, violation := range violations {
		if violation.Type == "retention_exceeded" {
			recommendations = append(recommendations,
				"Review and update retention policies to ensure compliance with regulations.")
			break
		}
	}

	// Default recommendations
	if len(recommendations) == 0 {
		recommendations = append(recommendations,
			"Continue monitoring compliance metrics and maintain current archival practices.")
	}

	return recommendations
}

// GetEvents returns all compliance events
func (cr *ComplianceReporter) GetEvents() []ComplianceEvent {
	cr.mu.RLock()
	defer cr.mu.RUnlock()

	events := make([]ComplianceEvent, len(cr.events))
	copy(events, cr.events)
	return events
}

// GetReports returns all compliance reports
func (cr *ComplianceReporter) GetReports() []ComplianceReport {
	cr.mu.RLock()
	defer cr.mu.RUnlock()

	reports := make([]ComplianceReport, len(cr.reports))
	copy(reports, cr.reports)
	return reports
}

package archival

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/minio"
	"go.uber.org/zap/zaptest"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

func TestArchivalServiceIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	ctx := context.Background()
	logger := zaptest.NewLogger(t)

	// Start MinIO container
	minioContainer, err := minio.RunContainer(ctx,
		testcontainers.WithImage("minio/minio:RELEASE.2023-12-07T04-16-00Z"),
		minio.WithUsername("minioadmin"),
		minio.WithPassword("minioadmin"),
	)
	require.NoError(t, err)
	defer func() {
		if err := minioContainer.Terminate(ctx); err != nil {
			t.Logf("Failed to terminate MinIO container: %v", err)
		}
	}()

	// Get MinIO connection details
	endpoint, err := minioContainer.ConnectionString(ctx)
	require.NoError(t, err)

	// Create test configuration
	cfg := &config.Config{
		Archive: config.ArchiveConfig{
			Enabled:       true,
			BufferSize:    1000,
			FlushInterval: 5 * time.Minute,
			Compression:   true,
			Retention:     720 * time.Hour,
			PolicyManager: config.PolicyManagerConfig{
				Enabled:             true,
				PolicyCheckInterval: 1 * time.Hour,
				DefaultRetention:    720 * time.Hour,
				PoliciesPath:        "/tmp/policies",
			},
			ComplianceReport: config.ComplianceReportConfig{
				Enabled:        true,
				ReportInterval: 24 * time.Hour,
				ReportsPath:    "/tmp/reports",
				Regulations:    []string{"GDPR", "SOX"},
				AlertThreshold: 5,
			},
			LifecycleManager: config.LifecycleManagerConfig{
				Enabled:            true,
				CleanupInterval:    1 * time.Hour,
				MetadataPath:       "/tmp/metadata",
				StorageTiers:       []string{"hot", "warm", "cold"},
				CompressionEnabled: true,
			},
		},
		ObjectStorage: config.ObjectStorageConfig{
			Provider:        "minio",
			Bucket:          "test-archival-bucket",
			Region:          "us-east-1",
			Endpoint:        endpoint,
			AccessKeyID:     "minioadmin",
			SecretAccessKey: "minioadmin",
			UseSSL:          false,
		},
	}

	// Create archival service
	service, err := NewService(cfg, logger)
	require.NoError(t, err)

	// Start the service
	err = service.Start()
	require.NoError(t, err)
	defer func() {
		if err := service.Stop(); err != nil {
			t.Logf("Failed to stop archival service: %v", err)
		}
	}()

	// Wait a moment for service to initialize
	time.Sleep(100 * time.Millisecond)

	t.Run("archive and retrieve data", func(t *testing.T) {
		testData := []byte(`{
			"timestamp": "2023-01-01T12:00:00Z",
			"level": "INFO",
			"message": "Test log message for archival",
			"source": "test-application",
			"user_id": "user123"
		}`)

		request := &ArchivalRequest{
			ID:        "integration-test-001",
			Source:    "test-application",
			DataType:  "application-logs",
			Data:      testData,
			Timestamp: time.Now(),
			Metadata: map[string]interface{}{
				"environment": "test",
				"version":     "1.0.0",
			},
			Compliance: &ComplianceRequirements{
				Regulations:    []string{"GDPR", "SOX"},
				Classification: "internal",
				Encryption:     true,
				Audit:          true,
			},
		}

		// Archive the data
		result, err := service.Archive(ctx, request)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "archived", result.Status)
		assert.NotEmpty(t, result.ArchivePath)
		assert.Equal(t, int64(len(testData)), result.Size)
		assert.NotNil(t, result.PolicyApplied)

		// Verify data was stored
		storedData, err := service.storage.Download(ctx, result.ArchivePath)
		require.NoError(t, err)
		assert.Equal(t, testData, storedData)

		// Check metrics
		metrics := service.GetMetrics()
		assert.Equal(t, int64(1), metrics.TotalArchived)
		assert.GreaterOrEqual(t, metrics.ActiveRetentionRules, 1)
	})

	t.Run("policy-based archival", func(t *testing.T) {
		// Test different data types get different policies
		testCases := []struct {
			source   string
			dataType string
			expected string
		}{
			{"ocsf", "guardium", "ocsf-warm-30d"},
			{"security", "auditlog", "security-auditlog"},
			{"crowdstrike", "raw-logs", "raw-logs-crowdstrike"},
			{"unknown", "unknown", "default"},
		}

		for _, tc := range testCases {
			t.Run(fmt.Sprintf("%s-%s", tc.source, tc.dataType), func(t *testing.T) {
				testData := []byte(fmt.Sprintf(`{"source": "%s", "type": "%s", "data": "test"}`, tc.source, tc.dataType))

				request := &ArchivalRequest{
					ID:        fmt.Sprintf("policy-test-%s-%s", tc.source, tc.dataType),
					Source:    tc.source,
					DataType:  tc.dataType,
					Data:      testData,
					Timestamp: time.Now(),
				}

				result, err := service.Archive(ctx, request)
				require.NoError(t, err)
				assert.NotNil(t, result.PolicyApplied)
				assert.Equal(t, tc.expected, result.PolicyApplied.ID)
			})
		}
	})

	t.Run("compliance reporting", func(t *testing.T) {
		// Archive data with compliance requirements
		testData := []byte(`{"sensitive": "data", "user_id": "user456"}`)

		request := &ArchivalRequest{
			ID:        "compliance-test-001",
			Source:    "user-service",
			DataType:  "user-data",
			Data:      testData,
			Timestamp: time.Now(),
			Compliance: &ComplianceRequirements{
				Regulations:    []string{"GDPR", "HIPAA"},
				Classification: "confidential",
				Encryption:     true,
				Audit:          true,
				Immutable:      true,
			},
		}

		result, err := service.Archive(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, "archived", result.Status)

		// Generate compliance report
		report, err := service.complianceReport.GenerateReport()
		require.NoError(t, err)
		assert.NotNil(t, report)
		assert.Greater(t, len(report.Events), 0)
		assert.Contains(t, report.Summary.RegulationsCovered, "GDPR")
		assert.Contains(t, report.Summary.RegulationsCovered, "HIPAA")
		assert.Greater(t, report.Summary.DataClassifications["confidential"], 0)
	})

	t.Run("lifecycle management", func(t *testing.T) {
		// Create archive with short retention for testing
		testData := []byte(`{"test": "lifecycle data"}`)

		// Add a policy with very short retention for testing
		shortRetentionPolicy := &RetentionPolicy{
			ID:              "test-short-retention",
			Name:            "Test Short Retention",
			Source:          "lifecycle-test",
			DataType:        "test-data",
			RetentionPeriod: 1 * time.Second, // Very short for testing
			CompressionType: "gzip",
			StorageTier:     "standard",
			Active:          true,
			Priority:        100,
		}

		err := service.policyManager.AddPolicy(shortRetentionPolicy)
		require.NoError(t, err)

		request := &ArchivalRequest{
			ID:        "lifecycle-test-001",
			Source:    "lifecycle-test",
			DataType:  "test-data",
			Data:      testData,
			Timestamp: time.Now(),
		}

		// Archive the data
		result, err := service.Archive(ctx, request)
		require.NoError(t, err)
		assert.Equal(t, "archived", result.Status)

		// Register with lifecycle manager
		archive := &ArchivedData{
			ID:        result.ID,
			Path:      result.ArchivePath,
			Source:    request.Source,
			DataType:  request.DataType,
			Size:      result.Size,
			ExpiresAt: result.ExpiresAt,
			Policy:    result.PolicyApplied,
		}

		err = service.lifecycleManager.RegisterArchive(archive)
		require.NoError(t, err)

		// Wait for expiration
		time.Sleep(2 * time.Second)

		// Check for expired archives
		expired, err := service.lifecycleManager.GetExpiredArchives()
		require.NoError(t, err)
		assert.Greater(t, len(expired), 0)

		// Execute lifecycle actions (cleanup)
		err = service.lifecycleManager.ExecuteLifecycleActions()
		require.NoError(t, err)

		// Verify archive was deleted
		_, err = service.storage.Download(ctx, result.ArchivePath)
		assert.Error(t, err) // Should fail because file was deleted
	})

	t.Run("archive statistics", func(t *testing.T) {
		// Get lifecycle statistics
		stats := service.lifecycleManager.GetArchiveStats()
		assert.NotNil(t, stats)
		assert.Contains(t, stats, "total_archives")
		assert.Contains(t, stats, "by_status")
		assert.Contains(t, stats, "by_source")
		assert.Contains(t, stats, "total_size")

		// Get service metrics
		metrics := service.GetMetrics()
		assert.Greater(t, metrics.TotalArchived, int64(0))
		assert.GreaterOrEqual(t, metrics.ActiveRetentionRules, 1)
	})
}

func TestArchivalServiceConcurrency(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping concurrency tests in short mode")
	}

	ctx := context.Background()
	logger := zaptest.NewLogger(t)

	// Start MinIO container
	minioContainer, err := minio.RunContainer(ctx,
		testcontainers.WithImage("minio/minio:RELEASE.2023-12-07T04-16-00Z"),
		minio.WithUsername("minioadmin"),
		minio.WithPassword("minioadmin"),
	)
	require.NoError(t, err)
	defer func() {
		if err := minioContainer.Terminate(ctx); err != nil {
			t.Logf("Failed to terminate MinIO container: %v", err)
		}
	}()

	endpoint, err := minioContainer.ConnectionString(ctx)
	require.NoError(t, err)

	cfg := &config.Config{
		Archive: config.ArchiveConfig{
			Enabled:       true,
			BufferSize:    1000,
			FlushInterval: 5 * time.Minute,
			Compression:   true,
			Retention:     720 * time.Hour,
			PolicyManager: config.PolicyManagerConfig{
				Enabled:             true,
				PolicyCheckInterval: 1 * time.Hour,
				DefaultRetention:    720 * time.Hour,
			},
			ComplianceReport: config.ComplianceReportConfig{
				Enabled:        true,
				ReportInterval: 24 * time.Hour,
			},
			LifecycleManager: config.LifecycleManagerConfig{
				Enabled:         true,
				CleanupInterval: 1 * time.Hour,
			},
		},
		ObjectStorage: config.ObjectStorageConfig{
			Provider:        "minio",
			Bucket:          "test-concurrency-bucket",
			Region:          "us-east-1",
			Endpoint:        endpoint,
			AccessKeyID:     "minioadmin",
			SecretAccessKey: "minioadmin",
			UseSSL:          false,
		},
	}

	service, err := NewService(cfg, logger)
	require.NoError(t, err)

	err = service.Start()
	require.NoError(t, err)
	defer func() {
		if err := service.Stop(); err != nil {
			t.Logf("Failed to stop archival service: %v", err)
		}
	}()

	// Test concurrent archival operations
	t.Run("concurrent archival", func(t *testing.T) {
		const numGoroutines = 10
		const numOperations = 5

		results := make(chan *ArchivalResult, numGoroutines*numOperations)
		errors := make(chan error, numGoroutines*numOperations)

		// Start concurrent archival operations
		for i := 0; i < numGoroutines; i++ {
			go func(workerID int) {
				for j := 0; j < numOperations; j++ {
					testData := []byte(fmt.Sprintf(`{"worker": %d, "operation": %d, "timestamp": "%s"}`,
						workerID, j, time.Now().Format(time.RFC3339)))

					request := &ArchivalRequest{
						ID:        fmt.Sprintf("concurrent-test-%d-%d", workerID, j),
						Source:    fmt.Sprintf("worker-%d", workerID),
						DataType:  "concurrent-logs",
						Data:      testData,
						Timestamp: time.Now(),
					}

					result, err := service.Archive(ctx, request)
					if err != nil {
						errors <- err
					} else {
						results <- result
					}
				}
			}(i)
		}

		// Collect results
		var successCount int
		var errorCount int

		for i := 0; i < numGoroutines*numOperations; i++ {
			select {
			case result := <-results:
				assert.Equal(t, "archived", result.Status)
				successCount++
			case err := <-errors:
				t.Logf("Archival error: %v", err)
				errorCount++
			case <-time.After(30 * time.Second):
				t.Fatal("Timeout waiting for archival operations")
			}
		}

		assert.Equal(t, numGoroutines*numOperations, successCount)
		assert.Equal(t, 0, errorCount)

		// Verify metrics
		metrics := service.GetMetrics()
		assert.Equal(t, int64(numGoroutines*numOperations), metrics.TotalArchived)
	})
}

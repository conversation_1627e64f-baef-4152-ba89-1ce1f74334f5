package archival

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/models"
	"github.com/gollm/core-gollmslake-go/pkg/storage"
)

// Service provides enhanced archival functionality with retention policies and compliance reporting
type Service struct {
	config           *config.Config
	logger           *zap.Logger
	storage          storage.Client
	policyManager    *PolicyManager
	complianceReport *ComplianceReporter
	lifecycleManager *LifecycleManager
	running          bool
	ctx              context.Context
	cancel           context.CancelFunc
	wg               sync.WaitGroup
	metrics          *ServiceMetrics
}

// ServiceMetrics tracks archival service metrics
type ServiceMetrics struct {
	TotalArchived        int64     `json:"total_archived"`
	TotalDeleted         int64     `json:"total_deleted"`
	PolicyViolations     int64     `json:"policy_violations"`
	ComplianceReports    int64     `json:"compliance_reports"`
	ActiveRetentionRules int       `json:"active_retention_rules"`
	LastPolicyCheck      time.Time `json:"last_policy_check"`
	LastComplianceReport time.Time `json:"last_compliance_report"`
	mu                   sync.RWMutex
}

// ArchivalRequest represents a request to archive data
type ArchivalRequest struct {
	ID          string                    `json:"id"`
	Source      string                    `json:"source"`
	DataType    string                    `json:"data_type"`
	Data        []byte                    `json:"data"`
	Metadata    map[string]interface{}    `json:"metadata"`
	Timestamp   time.Time                 `json:"timestamp"`
	Policy      *RetentionPolicy          `json:"policy,omitempty"`
	Compliance  *ComplianceRequirements   `json:"compliance,omitempty"`
	ProcessedBy *models.ProcessedLogEntry `json:"processed_by,omitempty"`
}

// ArchivalResult represents the result of an archival operation
type ArchivalResult struct {
	ID            string                 `json:"id"`
	Status        string                 `json:"status"`
	ArchivePath   string                 `json:"archive_path"`
	Size          int64                  `json:"size"`
	Timestamp     time.Time              `json:"timestamp"`
	ExpiresAt     time.Time              `json:"expires_at"`
	PolicyApplied *RetentionPolicy       `json:"policy_applied,omitempty"`
	Error         string                 `json:"error,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// NewService creates a new archival service
func NewService(cfg *config.Config, logger *zap.Logger) (*Service, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	// Create storage client
	storageClient, err := storage.NewClient(cfg.ObjectStorage, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create storage client: %w", err)
	}

	// Create context
	ctx, cancel := context.WithCancel(context.Background())

	// Create policy manager
	policyManager, err := NewPolicyManager(cfg, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create policy manager: %w", err)
	}

	// Create compliance reporter
	complianceReporter, err := NewComplianceReporter(cfg, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create compliance reporter: %w", err)
	}

	// Create lifecycle manager
	lifecycleManager, err := NewLifecycleManager(cfg, logger, storageClient)
	if err != nil {
		return nil, fmt.Errorf("failed to create lifecycle manager: %w", err)
	}

	return &Service{
		config:           cfg,
		logger:           logger,
		storage:          storageClient,
		policyManager:    policyManager,
		complianceReport: complianceReporter,
		lifecycleManager: lifecycleManager,
		ctx:              ctx,
		cancel:           cancel,
		metrics:          &ServiceMetrics{},
	}, nil
}

// Start starts the archival service
func (s *Service) Start() error {
	if s.running {
		return fmt.Errorf("service is already running")
	}

	s.running = true
	s.logger.Info("Starting archival service")

	// Start policy manager
	if err := s.policyManager.Start(); err != nil {
		return fmt.Errorf("failed to start policy manager: %w", err)
	}

	// Start compliance reporter
	if err := s.complianceReport.Start(); err != nil {
		return fmt.Errorf("failed to start compliance reporter: %w", err)
	}

	// Start lifecycle manager
	if err := s.lifecycleManager.Start(); err != nil {
		return fmt.Errorf("failed to start lifecycle manager: %w", err)
	}

	// Start background workers
	s.wg.Add(1)
	go s.policyEnforcer()

	s.wg.Add(1)
	go s.complianceReporter()

	s.wg.Add(1)
	go s.metricsCollector()

	return nil
}

// Stop stops the archival service
func (s *Service) Stop() error {
	if !s.running {
		return nil
	}

	s.logger.Info("Stopping archival service")
	s.cancel()
	s.running = false

	// Stop components
	if err := s.policyManager.Stop(); err != nil {
		s.logger.Error("Failed to stop policy manager", zap.Error(err))
	}

	if err := s.complianceReport.Stop(); err != nil {
		s.logger.Error("Failed to stop compliance reporter", zap.Error(err))
	}

	if err := s.lifecycleManager.Stop(); err != nil {
		s.logger.Error("Failed to stop lifecycle manager", zap.Error(err))
	}

	// Wait for workers to finish
	s.wg.Wait()

	return nil
}

// Archive archives data according to retention policies
func (s *Service) Archive(ctx context.Context, request *ArchivalRequest) (*ArchivalResult, error) {
	if request == nil {
		return nil, fmt.Errorf("archival request is required")
	}

	// Apply retention policy
	policy, err := s.policyManager.GetPolicy(request.Source, request.DataType)
	if err != nil {
		return nil, fmt.Errorf("failed to get retention policy: %w", err)
	}

	// Calculate expiration
	expiresAt := request.Timestamp.Add(policy.RetentionPeriod)

	// Create archive path
	archivePath := s.generateArchivePath(request, policy)

	// Archive data
	if err := s.storage.Upload(ctx, archivePath, request.Data); err != nil {
		return nil, fmt.Errorf("failed to upload to storage: %w", err)
	}

	// Update metrics
	s.updateMetrics(request, policy)

	// Record compliance event
	if request.Compliance != nil {
		if err := s.complianceReport.RecordEvent(request, policy); err != nil {
			s.logger.Error("Failed to record compliance event", zap.Error(err))
		}
	}

	result := &ArchivalResult{
		ID:            request.ID,
		Status:        "archived",
		ArchivePath:   archivePath,
		Size:          int64(len(request.Data)),
		Timestamp:     time.Now(),
		ExpiresAt:     expiresAt,
		PolicyApplied: policy,
		Metadata:      request.Metadata,
	}

	s.logger.Info("Data archived successfully",
		zap.String("id", request.ID),
		zap.String("source", request.Source),
		zap.String("path", archivePath),
		zap.Int64("size", result.Size),
		zap.Time("expires_at", expiresAt),
	)

	return result, nil
}

// GetMetrics returns current service metrics
func (s *Service) GetMetrics() *ServiceMetrics {
	s.metrics.mu.RLock()
	defer s.metrics.mu.RUnlock()

	// Create a copy to avoid race conditions
	metrics := *s.metrics
	return &metrics
}

// generateArchivePath generates a storage path for archived data
func (s *Service) generateArchivePath(request *ArchivalRequest, policy *RetentionPolicy) string {
	timestamp := request.Timestamp.Format("2006/01/02/15")
	return fmt.Sprintf("archives/%s/%s/%s/%s.json.gz",
		request.Source,
		request.DataType,
		timestamp,
		request.ID,
	)
}

// updateMetrics updates service metrics
func (s *Service) updateMetrics(request *ArchivalRequest, policy *RetentionPolicy) {
	s.metrics.mu.Lock()
	defer s.metrics.mu.Unlock()

	s.metrics.TotalArchived++
	s.metrics.ActiveRetentionRules = s.policyManager.GetActiveRuleCount()
}

// policyEnforcer runs policy enforcement in background
func (s *Service) policyEnforcer() {
	defer s.wg.Done()

	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			if err := s.enforcePolicies(); err != nil {
				s.logger.Error("Policy enforcement failed", zap.Error(err))
			}
		}
	}
}

// enforcePolicies enforces retention policies
func (s *Service) enforcePolicies() error {
	s.logger.Debug("Enforcing retention policies")

	// Get expired archives
	expiredArchives, err := s.lifecycleManager.GetExpiredArchives()
	if err != nil {
		return fmt.Errorf("failed to get expired archives: %w", err)
	}

	// Delete expired archives
	for _, archive := range expiredArchives {
		if err := s.lifecycleManager.DeleteArchive(archive); err != nil {
			s.logger.Error("Failed to delete expired archive",
				zap.String("archive", archive.Path),
				zap.Error(err),
			)
			s.metrics.mu.Lock()
			s.metrics.PolicyViolations++
			s.metrics.mu.Unlock()
		} else {
			s.metrics.mu.Lock()
			s.metrics.TotalDeleted++
			s.metrics.mu.Unlock()
		}
	}

	s.metrics.mu.Lock()
	s.metrics.LastPolicyCheck = time.Now()
	s.metrics.mu.Unlock()

	return nil
}

// complianceReporter runs compliance reporting in background
func (s *Service) complianceReporter() {
	defer s.wg.Done()

	ticker := time.NewTicker(24 * time.Hour) // Daily reports
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			if err := s.generateComplianceReport(); err != nil {
				s.logger.Error("Compliance reporting failed", zap.Error(err))
			}
		}
	}
}

// generateComplianceReport generates compliance reports
func (s *Service) generateComplianceReport() error {
	s.logger.Debug("Generating compliance report")

	report, err := s.complianceReport.GenerateReport()
	if err != nil {
		return fmt.Errorf("failed to generate compliance report: %w", err)
	}

	// Store report
	reportPath := fmt.Sprintf("compliance/reports/%s.json", time.Now().Format("2006-01-02"))
	reportData, err := json.Marshal(report)
	if err != nil {
		return fmt.Errorf("failed to marshal compliance report: %w", err)
	}

	if err := s.storage.Upload(s.ctx, reportPath, reportData); err != nil {
		return fmt.Errorf("failed to store compliance report: %w", err)
	}

	s.metrics.mu.Lock()
	s.metrics.ComplianceReports++
	s.metrics.LastComplianceReport = time.Now()
	s.metrics.mu.Unlock()

	s.logger.Info("Compliance report generated",
		zap.String("path", reportPath),
		zap.Int("total_events", len(report.Events)),
	)

	return nil
}

// metricsCollector collects and updates metrics
func (s *Service) metricsCollector() {
	defer s.wg.Done()

	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.collectMetrics()
		}
	}
}

// collectMetrics collects current metrics
func (s *Service) collectMetrics() {
	s.metrics.mu.Lock()
	defer s.metrics.mu.Unlock()

	s.metrics.ActiveRetentionRules = s.policyManager.GetActiveRuleCount()
}

// GetPolicyManager returns the policy manager
func (s *Service) GetPolicyManager() *PolicyManager {
	return s.policyManager
}

// GetComplianceReporter returns the compliance reporter
func (s *Service) GetComplianceReporter() *ComplianceReporter {
	return s.complianceReport
}

// GetLifecycleManager returns the lifecycle manager
func (s *Service) GetLifecycleManager() *LifecycleManager {
	return s.lifecycleManager
}

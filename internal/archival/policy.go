package archival

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// PolicyManager manages retention policies for different data types and sources
type PolicyManager struct {
	config   *config.Config
	logger   *zap.Logger
	policies map[string]*RetentionPolicy
	rules    map[string]*PolicyRule
	running  bool
	ctx      context.Context
	cancel   context.CancelFunc
	mu       sync.RWMutex
}

// RetentionPolicy defines retention rules for archived data
type RetentionPolicy struct {
	ID              string                 `json:"id"`
	Name            string                 `json:"name"`
	Description     string                 `json:"description"`
	Source          string                 `json:"source"`
	DataType        string                 `json:"data_type"`
	RetentionPeriod time.Duration          `json:"retention_period"`
	CompressionType string                 `json:"compression_type"`
	StorageTier     string                 `json:"storage_tier"`
	Compliance      *ComplianceRequirements `json:"compliance,omitempty"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
	Active          bool                   `json:"active"`
	Priority        int                    `json:"priority"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// PolicyRule defines conditional rules for policy application
type PolicyRule struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Conditions  []PolicyCondition      `json:"conditions"`
	Actions     []PolicyAction         `json:"actions"`
	Priority    int                    `json:"priority"`
	Active      bool                   `json:"active"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// PolicyCondition defines conditions for policy rule matching
type PolicyCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"` // eq, ne, gt, lt, gte, lte, contains, matches
	Value    interface{} `json:"value"`
	Type     string      `json:"type"` // string, number, boolean, time
}

// PolicyAction defines actions to take when policy rules match
type PolicyAction struct {
	Type       string                 `json:"type"` // set_retention, set_compression, set_tier, alert
	Parameters map[string]interface{} `json:"parameters"`
}

// ComplianceRequirements defines compliance requirements for data
type ComplianceRequirements struct {
	Regulations    []string               `json:"regulations"`    // GDPR, HIPAA, SOX, PCI-DSS, etc.
	Classification string                 `json:"classification"` // public, internal, confidential, restricted
	Encryption     bool                   `json:"encryption"`
	Audit          bool                   `json:"audit"`
	Immutable      bool                   `json:"immutable"`
	LegalHold      bool                   `json:"legal_hold"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// NewPolicyManager creates a new policy manager
func NewPolicyManager(cfg *config.Config, logger *zap.Logger) (*PolicyManager, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	ctx, cancel := context.WithCancel(context.Background())

	pm := &PolicyManager{
		config:   cfg,
		logger:   logger,
		policies: make(map[string]*RetentionPolicy),
		rules:    make(map[string]*PolicyRule),
		ctx:      ctx,
		cancel:   cancel,
	}

	// Load default policies
	if err := pm.loadDefaultPolicies(); err != nil {
		return nil, fmt.Errorf("failed to load default policies: %w", err)
	}

	return pm, nil
}

// Start starts the policy manager
func (pm *PolicyManager) Start() error {
	if pm.running {
		return fmt.Errorf("policy manager is already running")
	}

	pm.running = true
	pm.logger.Info("Starting policy manager")

	return nil
}

// Stop stops the policy manager
func (pm *PolicyManager) Stop() error {
	if !pm.running {
		return nil
	}

	pm.logger.Info("Stopping policy manager")
	pm.cancel()
	pm.running = false

	return nil
}

// GetPolicy returns the appropriate retention policy for given source and data type
func (pm *PolicyManager) GetPolicy(source, dataType string) (*RetentionPolicy, error) {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	// Try exact match first
	key := fmt.Sprintf("%s:%s", source, dataType)
	if policy, exists := pm.policies[key]; exists && policy.Active {
		return policy, nil
	}

	// Try source-only match
	sourceKey := fmt.Sprintf("%s:*", source)
	if policy, exists := pm.policies[sourceKey]; exists && policy.Active {
		return policy, nil
	}

	// Try data type-only match
	typeKey := fmt.Sprintf("*:%s", dataType)
	if policy, exists := pm.policies[typeKey]; exists && policy.Active {
		return policy, nil
	}

	// Return default policy
	if policy, exists := pm.policies["default"]; exists && policy.Active {
		return policy, nil
	}

	return nil, fmt.Errorf("no retention policy found for source: %s, dataType: %s", source, dataType)
}

// AddPolicy adds a new retention policy
func (pm *PolicyManager) AddPolicy(policy *RetentionPolicy) error {
	if policy == nil {
		return fmt.Errorf("policy is required")
	}

	pm.mu.Lock()
	defer pm.mu.Unlock()

	key := fmt.Sprintf("%s:%s", policy.Source, policy.DataType)
	policy.CreatedAt = time.Now()
	policy.UpdatedAt = time.Now()
	pm.policies[key] = policy

	pm.logger.Info("Added retention policy",
		zap.String("id", policy.ID),
		zap.String("source", policy.Source),
		zap.String("data_type", policy.DataType),
		zap.Duration("retention", policy.RetentionPeriod),
	)

	return nil
}

// UpdatePolicy updates an existing retention policy
func (pm *PolicyManager) UpdatePolicy(policy *RetentionPolicy) error {
	if policy == nil {
		return fmt.Errorf("policy is required")
	}

	pm.mu.Lock()
	defer pm.mu.Unlock()

	key := fmt.Sprintf("%s:%s", policy.Source, policy.DataType)
	if _, exists := pm.policies[key]; !exists {
		return fmt.Errorf("policy not found: %s", key)
	}

	policy.UpdatedAt = time.Now()
	pm.policies[key] = policy

	pm.logger.Info("Updated retention policy",
		zap.String("id", policy.ID),
		zap.String("source", policy.Source),
		zap.String("data_type", policy.DataType),
	)

	return nil
}

// DeletePolicy deletes a retention policy
func (pm *PolicyManager) DeletePolicy(source, dataType string) error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	key := fmt.Sprintf("%s:%s", source, dataType)
	if _, exists := pm.policies[key]; !exists {
		return fmt.Errorf("policy not found: %s", key)
	}

	delete(pm.policies, key)

	pm.logger.Info("Deleted retention policy",
		zap.String("source", source),
		zap.String("data_type", dataType),
	)

	return nil
}

// ListPolicies returns all retention policies
func (pm *PolicyManager) ListPolicies() []*RetentionPolicy {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	policies := make([]*RetentionPolicy, 0, len(pm.policies))
	for _, policy := range pm.policies {
		policies = append(policies, policy)
	}

	return policies
}

// GetActiveRuleCount returns the number of active retention rules
func (pm *PolicyManager) GetActiveRuleCount() int {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	count := 0
	for _, policy := range pm.policies {
		if policy.Active {
			count++
		}
	}

	return count
}

// loadDefaultPolicies loads default retention policies based on configuration
func (pm *PolicyManager) loadDefaultPolicies() error {
	// Default policy for all data
	defaultPolicy := &RetentionPolicy{
		ID:              "default",
		Name:            "Default Retention Policy",
		Description:     "Default retention policy for all archived data",
		Source:          "*",
		DataType:        "*",
		RetentionPeriod: 720 * time.Hour, // 30 days
		CompressionType: "gzip",
		StorageTier:     "standard",
		Active:          true,
		Priority:        0,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
	pm.policies["default"] = defaultPolicy

	// OCSF data policies based on OpenSearch ISM policies
	ocsfPolicies := []*RetentionPolicy{
		{
			ID:              "ocsf-warm-7d",
			Name:            "OCSF 7-Day Warm Policy",
			Description:     "OCSF data with 7-day warm retention",
			Source:          "ocsf",
			DataType:        "*",
			RetentionPeriod: 7 * 24 * time.Hour,
			CompressionType: "gzip",
			StorageTier:     "warm",
			Active:          true,
			Priority:        10,
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		},
		{
			ID:              "ocsf-warm-30d",
			Name:            "OCSF 30-Day Warm Policy",
			Description:     "OCSF data with 30-day warm retention",
			Source:          "ocsf",
			DataType:        "guardium",
			RetentionPeriod: 30 * 24 * time.Hour,
			CompressionType: "gzip",
			StorageTier:     "warm",
			Active:          true,
			Priority:        15,
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		},
		{
			ID:              "ocsf-warm-90d",
			Name:            "OCSF 90-Day Warm Policy",
			Description:     "OCSF data with 90-day warm retention",
			Source:          "ocsf",
			DataType:        "isv-general",
			RetentionPeriod: 90 * 24 * time.Hour,
			CompressionType: "gzip",
			StorageTier:     "warm",
			Active:          true,
			Priority:        20,
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		},
	}

	for _, policy := range ocsfPolicies {
		key := fmt.Sprintf("%s:%s", policy.Source, policy.DataType)
		pm.policies[key] = policy
	}

	// Security audit log policy
	securityAuditPolicy := &RetentionPolicy{
		ID:              "security-auditlog",
		Name:            "Security Audit Log Policy",
		Description:     "Security audit logs with 180-day retention",
		Source:          "security",
		DataType:        "auditlog",
		RetentionPeriod: 180 * 24 * time.Hour,
		CompressionType: "gzip",
		StorageTier:     "cold",
		Active:          true,
		Priority:        25,
		Compliance: &ComplianceRequirements{
			Regulations:    []string{"SOX", "PCI-DSS"},
			Classification: "confidential",
			Encryption:     true,
			Audit:          true,
			Immutable:      true,
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	pm.policies["security:auditlog"] = securityAuditPolicy

	// Raw log policies for different sources
	rawLogPolicies := []*RetentionPolicy{
		{
			ID:              "raw-logs-akamai",
			Name:            "Akamai Raw Logs Policy",
			Description:     "Akamai raw logs with 30-day retention",
			Source:          "akamai",
			DataType:        "raw-logs",
			RetentionPeriod: 30 * 24 * time.Hour,
			CompressionType: "gzip",
			StorageTier:     "standard",
			Active:          true,
			Priority:        5,
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		},
		{
			ID:              "raw-logs-crowdstrike",
			Name:            "CrowdStrike Raw Logs Policy",
			Description:     "CrowdStrike raw logs with 90-day retention",
			Source:          "crowdstrike",
			DataType:        "raw-logs",
			RetentionPeriod: 90 * 24 * time.Hour,
			CompressionType: "gzip",
			StorageTier:     "standard",
			Active:          true,
			Priority:        5,
			Compliance: &ComplianceRequirements{
				Regulations:    []string{"SOX"},
				Classification: "internal",
				Encryption:     true,
				Audit:          true,
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:              "raw-logs-office365",
			Name:            "Office 365 Raw Logs Policy",
			Description:     "Office 365 raw logs with 365-day retention",
			Source:          "office365",
			DataType:        "raw-logs",
			RetentionPeriod: 365 * 24 * time.Hour,
			CompressionType: "gzip",
			StorageTier:     "cold",
			Active:          true,
			Priority:        5,
			Compliance: &ComplianceRequirements{
				Regulations:    []string{"GDPR", "SOX"},
				Classification: "confidential",
				Encryption:     true,
				Audit:          true,
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	for _, policy := range rawLogPolicies {
		key := fmt.Sprintf("%s:%s", policy.Source, policy.DataType)
		pm.policies[key] = policy
	}

	pm.logger.Info("Loaded default retention policies",
		zap.Int("total_policies", len(pm.policies)),
	)

	return nil
}

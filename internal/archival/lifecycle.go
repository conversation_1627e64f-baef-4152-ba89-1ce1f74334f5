package archival

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/storage"
)

// LifecycleManager manages the lifecycle of archived data
type LifecycleManager struct {
	config   *config.Config
	logger   *zap.Logger
	storage  storage.Client
	archives map[string]*ArchivedData
	running  bool
	ctx      context.Context
	cancel   context.CancelFunc
	mu       sync.RWMutex
}

// ArchivedData represents archived data with lifecycle information
type ArchivedData struct {
	ID           string                  `json:"id"`
	Path         string                  `json:"path"`
	Source       string                  `json:"source"`
	DataType     string                  `json:"data_type"`
	Size         int64                   `json:"size"`
	CreatedAt    time.Time               `json:"created_at"`
	ExpiresAt    time.Time               `json:"expires_at"`
	LastAccessed time.Time               `json:"last_accessed"`
	AccessCount  int64                   `json:"access_count"`
	StorageTier  string                  `json:"storage_tier"`
	Compressed   bool                    `json:"compressed"`
	Encrypted    bool                    `json:"encrypted"`
	Policy       *RetentionPolicy        `json:"policy,omitempty"`
	Compliance   *ComplianceRequirements `json:"compliance,omitempty"`
	Status       string                  `json:"status"` // active, expired, deleted, error
	Metadata     map[string]interface{}  `json:"metadata,omitempty"`
}

// LifecycleAction represents an action to be taken on archived data
type LifecycleAction struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"` // delete, move_tier, compress, encrypt
	Target    *ArchivedData          `json:"target"`
	Timestamp time.Time              `json:"timestamp"`
	Status    string                 `json:"status"` // pending, in_progress, completed, failed
	Error     string                 `json:"error,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// NewLifecycleManager creates a new lifecycle manager
func NewLifecycleManager(cfg *config.Config, logger *zap.Logger, storageClient storage.Client) (*LifecycleManager, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}
	if storageClient == nil {
		return nil, fmt.Errorf("storage client is required")
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &LifecycleManager{
		config:   cfg,
		logger:   logger,
		storage:  storageClient,
		archives: make(map[string]*ArchivedData),
		ctx:      ctx,
		cancel:   cancel,
	}, nil
}

// Start starts the lifecycle manager
func (lm *LifecycleManager) Start() error {
	if lm.running {
		return fmt.Errorf("lifecycle manager is already running")
	}

	lm.running = true
	lm.logger.Info("Starting lifecycle manager")

	// Load existing archives
	if err := lm.loadExistingArchives(); err != nil {
		lm.logger.Error("Failed to load existing archives", zap.Error(err))
	}

	return nil
}

// Stop stops the lifecycle manager
func (lm *LifecycleManager) Stop() error {
	if !lm.running {
		return nil
	}

	lm.logger.Info("Stopping lifecycle manager")
	lm.cancel()
	lm.running = false

	return nil
}

// RegisterArchive registers a new archived data entry
func (lm *LifecycleManager) RegisterArchive(archive *ArchivedData) error {
	if archive == nil {
		return fmt.Errorf("archive data is required")
	}

	lm.mu.Lock()
	defer lm.mu.Unlock()

	archive.Status = "active"
	archive.CreatedAt = time.Now()
	archive.LastAccessed = time.Now()
	lm.archives[archive.ID] = archive

	lm.logger.Debug("Registered archive",
		zap.String("id", archive.ID),
		zap.String("path", archive.Path),
		zap.String("source", archive.Source),
		zap.Time("expires_at", archive.ExpiresAt),
	)

	return nil
}

// GetExpiredArchives returns archives that have exceeded their retention period
func (lm *LifecycleManager) GetExpiredArchives() ([]*ArchivedData, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	var expired []*ArchivedData
	now := time.Now()

	for _, archive := range lm.archives {
		if archive.Status == "active" && archive.ExpiresAt.Before(now) {
			expired = append(expired, archive)
		}
	}

	lm.logger.Debug("Found expired archives",
		zap.Int("count", len(expired)),
	)

	return expired, nil
}

// DeleteArchive deletes an archived data entry
func (lm *LifecycleManager) DeleteArchive(archive *ArchivedData) error {
	if archive == nil {
		return fmt.Errorf("archive data is required")
	}

	// Delete from storage
	if err := lm.storage.Delete(lm.ctx, archive.Path); err != nil {
		return fmt.Errorf("failed to delete from storage: %w", err)
	}

	lm.mu.Lock()
	defer lm.mu.Unlock()

	// Update status
	if existingArchive, exists := lm.archives[archive.ID]; exists {
		existingArchive.Status = "deleted"
	}

	lm.logger.Info("Deleted archive",
		zap.String("id", archive.ID),
		zap.String("path", archive.Path),
		zap.String("source", archive.Source),
	)

	return nil
}

// GetArchive retrieves archived data by ID
func (lm *LifecycleManager) GetArchive(id string) (*ArchivedData, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	archive, exists := lm.archives[id]
	if !exists {
		return nil, fmt.Errorf("archive not found: %s", id)
	}

	// Update access tracking
	archive.LastAccessed = time.Now()
	archive.AccessCount++

	return archive, nil
}

// ListArchives returns all archives matching the given criteria
func (lm *LifecycleManager) ListArchives(source, dataType, status string) ([]*ArchivedData, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	var filtered []*ArchivedData

	for _, archive := range lm.archives {
		// Apply filters
		if source != "" && archive.Source != source {
			continue
		}
		if dataType != "" && archive.DataType != dataType {
			continue
		}
		if status != "" && archive.Status != status {
			continue
		}

		filtered = append(filtered, archive)
	}

	return filtered, nil
}

// GetArchiveStats returns statistics about archived data
func (lm *LifecycleManager) GetArchiveStats() map[string]interface{} {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	stats := map[string]interface{}{
		"total_archives":  len(lm.archives),
		"by_status":       make(map[string]int),
		"by_source":       make(map[string]int),
		"by_data_type":    make(map[string]int),
		"by_storage_tier": make(map[string]int),
		"total_size":      int64(0),
		"expired_count":   0,
		"active_count":    0,
	}

	now := time.Now()

	for _, archive := range lm.archives {
		// Count by status
		stats["by_status"].(map[string]int)[archive.Status]++

		// Count by source
		stats["by_source"].(map[string]int)[archive.Source]++

		// Count by data type
		stats["by_data_type"].(map[string]int)[archive.DataType]++

		// Count by storage tier
		stats["by_storage_tier"].(map[string]int)[archive.StorageTier]++

		// Total size
		stats["total_size"] = stats["total_size"].(int64) + archive.Size

		// Count expired vs active
		if archive.Status == "active" {
			if archive.ExpiresAt.Before(now) {
				stats["expired_count"] = stats["expired_count"].(int) + 1
			} else {
				stats["active_count"] = stats["active_count"].(int) + 1
			}
		}
	}

	return stats
}

// ExecuteLifecycleActions executes pending lifecycle actions
func (lm *LifecycleManager) ExecuteLifecycleActions() error {
	// Get expired archives
	expired, err := lm.GetExpiredArchives()
	if err != nil {
		return fmt.Errorf("failed to get expired archives: %w", err)
	}

	// Create delete actions for expired archives
	for _, archive := range expired {
		action := &LifecycleAction{
			ID:        fmt.Sprintf("delete-%s-%d", archive.ID, time.Now().UnixNano()),
			Type:      "delete",
			Target:    archive,
			Timestamp: time.Now(),
			Status:    "pending",
		}

		if err := lm.executeAction(action); err != nil {
			lm.logger.Error("Failed to execute lifecycle action",
				zap.String("action_id", action.ID),
				zap.String("type", action.Type),
				zap.String("archive_id", archive.ID),
				zap.Error(err),
			)
		}
	}

	return nil
}

// executeAction executes a single lifecycle action
func (lm *LifecycleManager) executeAction(action *LifecycleAction) error {
	action.Status = "in_progress"

	switch action.Type {
	case "delete":
		if err := lm.DeleteArchive(action.Target); err != nil {
			action.Status = "failed"
			action.Error = err.Error()
			return err
		}
		action.Status = "completed"

	case "move_tier":
		// Implementation for moving between storage tiers
		if err := lm.moveStorageTier(action.Target, action.Metadata["target_tier"].(string)); err != nil {
			action.Status = "failed"
			action.Error = err.Error()
			return err
		}
		action.Status = "completed"

	case "compress":
		// Implementation for compressing archived data
		if err := lm.compressArchive(action.Target); err != nil {
			action.Status = "failed"
			action.Error = err.Error()
			return err
		}
		action.Status = "completed"

	default:
		return fmt.Errorf("unknown action type: %s", action.Type)
	}

	lm.logger.Info("Executed lifecycle action",
		zap.String("action_id", action.ID),
		zap.String("type", action.Type),
		zap.String("status", action.Status),
		zap.String("archive_id", action.Target.ID),
	)

	return nil
}

// loadExistingArchives loads existing archives from storage metadata
func (lm *LifecycleManager) loadExistingArchives() error {
	// List all objects in the archives prefix
	objects, err := lm.storage.List(lm.ctx, "archives/")
	if err != nil {
		return fmt.Errorf("failed to list archives: %w", err)
	}

	for _, key := range objects {
		// Parse archive information from object path
		// Note: We only have the key, not size/lastModified from the storage interface
		archive := lm.parseArchiveFromPath(key, 0, time.Time{})
		if archive != nil {
			lm.archives[archive.ID] = archive
		}
	}

	lm.logger.Info("Loaded existing archives",
		zap.Int("count", len(lm.archives)),
	)

	return nil
}

// parseArchiveFromPath parses archive information from storage path
func (lm *LifecycleManager) parseArchiveFromPath(path string, size int64, lastModified time.Time) *ArchivedData {
	// Expected path format: archives/{source}/{data_type}/{timestamp}/{id}.json.gz
	parts := strings.Split(path, "/")
	if len(parts) < 5 {
		return nil
	}

	source := parts[1]
	dataType := parts[2]
	filename := parts[len(parts)-1]

	// Extract ID from filename (remove .json.gz extension)
	id := strings.TrimSuffix(filename, ".json.gz")

	return &ArchivedData{
		ID:           id,
		Path:         path,
		Source:       source,
		DataType:     dataType,
		Size:         size,
		CreatedAt:    lastModified,
		LastAccessed: lastModified,
		StorageTier:  "standard",
		Compressed:   strings.HasSuffix(filename, ".gz"),
		Status:       "active",
	}
}

// moveStorageTier moves archive to a different storage tier
func (lm *LifecycleManager) moveStorageTier(archive *ArchivedData, targetTier string) error {
	// Implementation would depend on storage backend capabilities
	// For now, just update the metadata
	lm.mu.Lock()
	defer lm.mu.Unlock()

	archive.StorageTier = targetTier

	lm.logger.Info("Moved archive to different storage tier",
		zap.String("archive_id", archive.ID),
		zap.String("target_tier", targetTier),
	)

	return nil
}

// compressArchive compresses an archived data entry
func (lm *LifecycleManager) compressArchive(archive *ArchivedData) error {
	if archive.Compressed {
		return nil // Already compressed
	}

	// Implementation would download, compress, and re-upload
	// For now, just update the metadata
	lm.mu.Lock()
	defer lm.mu.Unlock()

	archive.Compressed = true

	lm.logger.Info("Compressed archive",
		zap.String("archive_id", archive.ID),
	)

	return nil
}

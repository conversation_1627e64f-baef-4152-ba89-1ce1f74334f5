package archival

import (
	"context"
	"io"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// MockStorageClient is a mock implementation of storage.Client
type MockStorageClient struct {
	mock.Mock
}

func (m *MockStorageClient) Upload(ctx context.Context, key string, reader io.Reader) error {
	args := m.Called(ctx, key, reader)
	return args.Error(0)
}

func (m *MockStorageClient) Download(ctx context.Context, key string) (io.ReadCloser, error) {
	args := m.Called(ctx, key)
	return args.Get(0).(io.ReadCloser), args.Error(1)
}

func (m *MockStorageClient) Delete(ctx context.Context, key string) error {
	args := m.Called(ctx, key)
	return args.Error(0)
}

func (m *MockStorageClient) List(ctx context.Context, prefix string) ([]string, error) {
	args := m.Called(ctx, prefix)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockStorageClient) Health(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func TestNewService(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Archive: config.ArchiveConfig{
			Enabled:       true,
			BufferSize:    1000,
			FlushInterval: 5 * time.Minute,
			Compression:   true,
			Retention:     720 * time.Hour,
			PolicyManager: config.PolicyManagerConfig{
				Enabled:             true,
				PolicyCheckInterval: 1 * time.Hour,
				DefaultRetention:    720 * time.Hour,
				PoliciesPath:        "/tmp/policies",
			},
			ComplianceReport: config.ComplianceReportConfig{
				Enabled:        true,
				ReportInterval: 24 * time.Hour,
				ReportsPath:    "/tmp/reports",
				Regulations:    []string{"GDPR", "SOX"},
				AlertThreshold: 5,
			},
			LifecycleManager: config.LifecycleManagerConfig{
				Enabled:            true,
				CleanupInterval:    1 * time.Hour,
				MetadataPath:       "/tmp/metadata",
				StorageTiers:       []string{"hot", "warm", "cold"},
				CompressionEnabled: true,
			},
		},
		ObjectStorage: config.ObjectStorageConfig{
			Provider: "minio",
			Bucket:   "test-bucket",
			Region:   "us-east-1",
		},
	}

	t.Run("successful creation", func(t *testing.T) {
		service, err := NewService(cfg, logger)
		require.NoError(t, err)
		assert.NotNil(t, service)
		assert.Equal(t, cfg, service.config)
		assert.Equal(t, logger, service.logger)
		assert.NotNil(t, service.policyManager)
		assert.NotNil(t, service.complianceReport)
		assert.NotNil(t, service.lifecycleManager)
		assert.False(t, service.running)
	})

	t.Run("nil config", func(t *testing.T) {
		service, err := NewService(nil, logger)
		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.Error(), "config is required")
	})

	t.Run("nil logger", func(t *testing.T) {
		service, err := NewService(cfg, nil)
		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.Error(), "logger is required")
	})
}

func TestService_StartStop(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Archive: config.ArchiveConfig{
			Enabled:       true,
			BufferSize:    1000,
			FlushInterval: 5 * time.Minute,
			Compression:   true,
			Retention:     720 * time.Hour,
			PolicyManager: config.PolicyManagerConfig{
				Enabled:             true,
				PolicyCheckInterval: 1 * time.Hour,
				DefaultRetention:    720 * time.Hour,
			},
			ComplianceReport: config.ComplianceReportConfig{
				Enabled:        true,
				ReportInterval: 24 * time.Hour,
			},
			LifecycleManager: config.LifecycleManagerConfig{
				Enabled:         true,
				CleanupInterval: 1 * time.Hour,
			},
		},
		ObjectStorage: config.ObjectStorageConfig{
			Provider: "minio",
			Bucket:   "test-bucket",
			Region:   "us-east-1",
		},
	}

	service, err := NewService(cfg, logger)
	require.NoError(t, err)

	t.Run("start service", func(t *testing.T) {
		err := service.Start()
		assert.NoError(t, err)
		assert.True(t, service.running)
	})

	t.Run("start already running service", func(t *testing.T) {
		err := service.Start()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "service is already running")
	})

	t.Run("stop service", func(t *testing.T) {
		err := service.Stop()
		assert.NoError(t, err)
		assert.False(t, service.running)
	})

	t.Run("stop already stopped service", func(t *testing.T) {
		err := service.Stop()
		assert.NoError(t, err)
		assert.False(t, service.running)
	})
}

func TestService_Archive(t *testing.T) {
	logger := zaptest.NewLogger(t)
	mockStorage := &MockStorageClient{}

	cfg := &config.Config{
		Archive: config.ArchiveConfig{
			Enabled:       true,
			BufferSize:    1000,
			FlushInterval: 5 * time.Minute,
			Compression:   true,
			Retention:     720 * time.Hour,
			PolicyManager: config.PolicyManagerConfig{
				Enabled:             true,
				PolicyCheckInterval: 1 * time.Hour,
				DefaultRetention:    720 * time.Hour,
			},
			ComplianceReport: config.ComplianceReportConfig{
				Enabled:        true,
				ReportInterval: 24 * time.Hour,
			},
			LifecycleManager: config.LifecycleManagerConfig{
				Enabled:         true,
				CleanupInterval: 1 * time.Hour,
			},
		},
		ObjectStorage: config.ObjectStorageConfig{
			Provider: "minio",
			Bucket:   "test-bucket",
			Region:   "us-east-1",
		},
	}

	service, err := NewService(cfg, logger)
	require.NoError(t, err)

	// Replace storage client with mock
	service.storage = mockStorage

	ctx := context.Background()
	testData := []byte(`{"message": "test log entry", "timestamp": "2023-01-01T00:00:00Z"}`)

	request := &ArchivalRequest{
		ID:        "test-123",
		Source:    "test-source",
		DataType:  "logs",
		Data:      testData,
		Timestamp: time.Now(),
		Metadata: map[string]interface{}{
			"test": "metadata",
		},
		Compliance: &ComplianceRequirements{
			Regulations:    []string{"GDPR"},
			Classification: "internal",
			Encryption:     true,
			Audit:          true,
		},
	}

	t.Run("successful archive", func(t *testing.T) {
		mockStorage.On("Upload", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("*bytes.Reader")).Return(nil)

		result, err := service.Archive(ctx, request)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, request.ID, result.ID)
		assert.Equal(t, "archived", result.Status)
		assert.NotEmpty(t, result.ArchivePath)
		assert.Equal(t, int64(len(testData)), result.Size)
		assert.NotNil(t, result.PolicyApplied)
		assert.False(t, result.ExpiresAt.IsZero())

		mockStorage.AssertExpectations(t)
	})

	t.Run("nil request", func(t *testing.T) {
		result, err := service.Archive(ctx, nil)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "archival request is required")
	})

	t.Run("storage upload failure", func(t *testing.T) {
		mockStorage.On("Upload", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("*bytes.Reader")).Return(assert.AnError)

		result, err := service.Archive(ctx, request)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to upload to storage")

		mockStorage.AssertExpectations(t)
	})
}

func TestService_GetMetrics(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Archive: config.ArchiveConfig{
			Enabled:       true,
			BufferSize:    1000,
			FlushInterval: 5 * time.Minute,
			Compression:   true,
			Retention:     720 * time.Hour,
			PolicyManager: config.PolicyManagerConfig{
				Enabled:             true,
				PolicyCheckInterval: 1 * time.Hour,
				DefaultRetention:    720 * time.Hour,
			},
			ComplianceReport: config.ComplianceReportConfig{
				Enabled:        true,
				ReportInterval: 24 * time.Hour,
			},
			LifecycleManager: config.LifecycleManagerConfig{
				Enabled:         true,
				CleanupInterval: 1 * time.Hour,
			},
		},
		ObjectStorage: config.ObjectStorageConfig{
			Provider: "minio",
			Bucket:   "test-bucket",
			Region:   "us-east-1",
		},
	}

	service, err := NewService(cfg, logger)
	require.NoError(t, err)

	metrics := service.GetMetrics()
	assert.NotNil(t, metrics)
	assert.Equal(t, int64(0), metrics.TotalArchived)
	assert.Equal(t, int64(0), metrics.TotalDeleted)
	assert.Equal(t, int64(0), metrics.PolicyViolations)
	assert.Equal(t, int64(0), metrics.ComplianceReports)
	assert.GreaterOrEqual(t, metrics.ActiveRetentionRules, 0)
}

func TestService_generateArchivePath(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Archive: config.ArchiveConfig{
			Enabled:       true,
			BufferSize:    1000,
			FlushInterval: 5 * time.Minute,
			Compression:   true,
			Retention:     720 * time.Hour,
		},
		ObjectStorage: config.ObjectStorageConfig{
			Provider: "minio",
			Bucket:   "test-bucket",
			Region:   "us-east-1",
		},
	}

	service, err := NewService(cfg, logger)
	require.NoError(t, err)

	timestamp := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
	request := &ArchivalRequest{
		ID:        "test-123",
		Source:    "test-source",
		DataType:  "logs",
		Timestamp: timestamp,
	}

	policy := &RetentionPolicy{
		ID:              "test-policy",
		RetentionPeriod: 30 * 24 * time.Hour,
	}

	path := service.generateArchivePath(request, policy)
	expected := "archives/test-source/logs/2023/01/01/12/test-123.json.gz"
	assert.Equal(t, expected, path)
}

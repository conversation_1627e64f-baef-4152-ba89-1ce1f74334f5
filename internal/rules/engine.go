package rules

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/models"
	yaml "gopkg.in/yaml.v3"
)

// Engine handles rule evaluation and alert generation
type Engine struct {
	config   *config.Config
	logger   *zap.Logger
	rules    []Rule
	ruleSets map[string]*RuleSet
	patterns map[string]*regexp.Regexp
}

// Rule represents a single rule
type Rule struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Enabled     bool                   `json:"enabled"`
	Condition   string                 `json:"condition"`
	Actions     []RuleAction           `json:"actions"`
	Alert       AlertConfig            `json:"alert"`
	RateLimit   RateLimitConfig        `json:"rate_limit"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// AlertConfig represents alert configuration for a rule
type AlertConfig struct {
	Enabled  bool     `json:"enabled"`
	Severity string   `json:"severity"`
	Message  string   `json:"message"`
	Tags     []string `json:"tags"`
}

// RateLimitConfig represents rate limiting configuration for a rule
type RateLimitConfig struct {
	Enabled bool          `json:"enabled"`
	Count   int           `json:"count"`
	Window  time.Duration `json:"window"`
}

// RuleSet represents a collection of related rules
type RuleSet struct {
	Name    string `json:"name"`
	Enabled bool   `json:"enabled"`
	Rules   []Rule `json:"rules"`
}

// NewEngine creates a new rules engine
func NewEngine(cfg *config.Config, logger *zap.Logger) (*Engine, error) {
	engine := &Engine{
		config:   cfg,
		logger:   logger,
		ruleSets: make(map[string]*RuleSet),
		patterns: make(map[string]*regexp.Regexp),
	}

	// Load rule sets from configuration
	if err := engine.loadRuleSets(); err != nil {
		return nil, fmt.Errorf("failed to load rule sets: %w", err)
	}

	// Compile regex patterns
	if err := engine.compilePatterns(); err != nil {
		return nil, fmt.Errorf("failed to compile patterns: %w", err)
	}

	return engine, nil
}

// loadRuleSets loads rule sets from configuration
func (e *Engine) loadRuleSets() error {
	e.ruleSets = make(map[string]*RuleSet)

	if e.config == nil {
		return errors.New("engine config is nil")
	}

	rulesDir := strings.TrimSpace(e.config.Rules.RulesDir)
	if rulesDir == "" {
		return nil
	}

	entries, err := os.ReadDir(rulesDir)
	if err != nil {
		if errors.Is(err, os.ErrNotExist) {
			e.logger.Warn("Rules directory does not exist", zap.String("path", rulesDir))
			return nil
		}
		return fmt.Errorf("failed to read rules directory %s: %w", rulesDir, err)
	}

	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		ext := strings.ToLower(filepath.Ext(entry.Name()))
		if ext != ".yaml" && ext != ".yml" && ext != ".json" {
			continue
		}

		fullPath := filepath.Join(rulesDir, entry.Name())
		payload, err := os.ReadFile(fullPath)
		if err != nil {
			return fmt.Errorf("failed to read rule file %s: %w", fullPath, err)
		}

		ruleSets, err := decodeRuleSets(payload)
		if err != nil {
			return fmt.Errorf("failed to decode rule file %s: %w", fullPath, err)
		}

		for _, rs := range ruleSets {
			if strings.TrimSpace(rs.Name) == "" {
				rs.Name = strings.TrimSuffix(entry.Name(), filepath.Ext(entry.Name()))
			}

			if existing, exists := e.ruleSets[rs.Name]; exists {
				// Merge rules when multiple files contribute to the same ruleset.
				existing.Rules = append(existing.Rules, rs.Rules...)
				if rs.Enabled {
					existing.Enabled = true
				}
				continue
			}

			rsCopy := rs
			e.ruleSets[rsCopy.Name] = &rsCopy
		}
	}

	return nil
}

func decodeRuleSets(data []byte) ([]RuleSet, error) {
	if len(data) == 0 {
		return nil, errors.New("rule file is empty")
	}

	var wrapped struct {
		RuleSets []RuleSet `yaml:"rule_sets" json:"rule_sets"`
	}
	if err := yaml.Unmarshal(data, &wrapped); err == nil && len(wrapped.RuleSets) > 0 {
		return wrapped.RuleSets, nil
	}

	var list []RuleSet
	if err := yaml.Unmarshal(data, &list); err == nil && len(list) > 0 {
		return list, nil
	}

	var single RuleSet
	if err := yaml.Unmarshal(data, &single); err == nil && (strings.TrimSpace(single.Name) != "" || len(single.Rules) > 0) {
		return []RuleSet{single}, nil
	}

	return nil, errors.New("no rule sets found in file")
}

// compilePatterns compiles commonly used regex patterns
func (e *Engine) compilePatterns() error {
	patterns := map[string]string{
		"failed_login":        `(?i)(login\s+failed|authentication\s+failed|invalid\s+credentials)`,
		"suspicious_activity": `(?i)(suspicious|malicious|threat|attack|intrusion)`,
		"error_pattern":       `(?i)(error|exception|failure|fatal)`,
		"ip_address":          `\b(?:\d{1,3}\.){3}\d{1,3}\b`,
		"email":               `[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}`,
	}

	for name, pattern := range patterns {
		compiled, err := regexp.Compile(pattern)
		if err != nil {
			return fmt.Errorf("failed to compile pattern %s: %w", name, err)
		}
		e.patterns[name] = compiled
	}

	return nil
}

// Evaluate evaluates all rules against a log entry and returns alerts
func (e *Engine) Evaluate(ctx context.Context, entry *models.ProcessedLogEntry) ([]*models.Alert, error) {
	// Check if we have any rule sets configured
	if len(e.ruleSets) == 0 {
		return nil, nil
	}

	var alerts []*models.Alert

	for _, ruleSet := range e.ruleSets {
		if !ruleSet.Enabled {
			continue
		}

		for _, rule := range ruleSet.Rules {
			if !rule.Enabled {
				continue
			}

			if e.evaluateRule(entry, &rule) {
				ruleAlerts := e.generateAlerts(entry, &rule)
				alerts = append(alerts, ruleAlerts...)
			}
		}
	}

	return alerts, nil
}

// evaluateRule evaluates a single rule against a log entry
func (e *Engine) evaluateRule(entry *models.ProcessedLogEntry, rule *Rule) bool {
	return e.evaluateCondition(entry, rule.Condition)
}

// evaluateCondition evaluates a condition string against a log entry
func (e *Engine) evaluateCondition(entry *models.ProcessedLogEntry, condition string) bool {
	// Simple condition parser - in production, you'd want a more sophisticated parser
	condition = strings.TrimSpace(condition)

	// Handle AND conditions
	if strings.Contains(condition, " && ") {
		parts := strings.Split(condition, " && ")
		for _, part := range parts {
			if !e.evaluateCondition(entry, strings.TrimSpace(part)) {
				return false
			}
		}
		return true
	}

	// Handle OR conditions
	if strings.Contains(condition, " || ") {
		parts := strings.Split(condition, " || ")
		for _, part := range parts {
			if e.evaluateCondition(entry, strings.TrimSpace(part)) {
				return true
			}
		}
		return false
	}

	// Handle simple conditions
	return e.evaluateSimpleCondition(entry, condition)
}

// evaluateSimpleCondition evaluates a simple condition
func (e *Engine) evaluateSimpleCondition(entry *models.ProcessedLogEntry, condition string) bool {
	// Parse condition: field operator value
	parts := strings.Fields(condition)
	if len(parts) < 3 {
		return false
	}

	field := parts[0]
	operator := parts[1]
	value := strings.Join(parts[2:], " ")

	// Remove quotes from value
	value = strings.Trim(value, "'\"")

	// Get field value
	var fieldValue interface{}
	switch field {
	case "level":
		fieldValue = entry.Level
	case "message":
		fieldValue = entry.Message
	case "source":
		fieldValue = entry.Source
	default:
		fieldValue = entry.Fields[field]
	}

	// Evaluate based on operator
	switch operator {
	case "==", "equals":
		return e.compareEqual(fieldValue, value)
	case "!=", "not_equals":
		return !e.compareEqual(fieldValue, value)
	case "contains":
		return e.compareContains(fieldValue, value)
	case "not_contains":
		return !e.compareContains(fieldValue, value)
	case "matches", "regex":
		return e.compareRegex(fieldValue, value)
	case ">", "gt":
		return e.compareGreater(fieldValue, value)
	case "<", "lt":
		return e.compareLess(fieldValue, value)
	case ">=", "gte":
		return e.compareGreaterEqual(fieldValue, value)
	case "<=", "lte":
		return e.compareLessEqual(fieldValue, value)
	default:
		e.logger.Warn("Unknown operator", zap.String("operator", operator))
		return false
	}
}

// Comparison functions
func (e *Engine) compareEqual(fieldValue interface{}, value string) bool {
	if str, ok := fieldValue.(string); ok {
		return str == value
	}
	return fmt.Sprintf("%v", fieldValue) == value
}

func (e *Engine) compareContains(fieldValue interface{}, value string) bool {
	if str, ok := fieldValue.(string); ok {
		return strings.Contains(strings.ToLower(str), strings.ToLower(value))
	}
	return false
}

func (e *Engine) compareRegex(fieldValue interface{}, pattern string) bool {
	if str, ok := fieldValue.(string); ok {
		matched, err := regexp.MatchString(pattern, str)
		if err != nil {
			e.logger.Warn("Invalid regex pattern", zap.String("pattern", pattern), zap.Error(err))
			return false
		}
		return matched
	}
	return false
}

func (e *Engine) compareGreater(fieldValue interface{}, value string) bool {
	return e.compareNumeric(fieldValue, value, func(a, b float64) bool { return a > b })
}

func (e *Engine) compareLess(fieldValue interface{}, value string) bool {
	return e.compareNumeric(fieldValue, value, func(a, b float64) bool { return a < b })
}

func (e *Engine) compareGreaterEqual(fieldValue interface{}, value string) bool {
	return e.compareNumeric(fieldValue, value, func(a, b float64) bool { return a >= b })
}

func (e *Engine) compareLessEqual(fieldValue interface{}, value string) bool {
	return e.compareNumeric(fieldValue, value, func(a, b float64) bool { return a <= b })
}

func (e *Engine) compareNumeric(fieldValue interface{}, value string, compareFn func(float64, float64) bool) bool {
	var fieldNum float64
	var err error

	switch v := fieldValue.(type) {
	case float64:
		fieldNum = v
	case int:
		fieldNum = float64(v)
	case string:
		fieldNum, err = strconv.ParseFloat(v, 64)
		if err != nil {
			return false
		}
	default:
		return false
	}

	valueNum, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return false
	}

	return compareFn(fieldNum, valueNum)
}

// generateAlerts generates alerts based on rule actions
func (e *Engine) generateAlerts(entry *models.ProcessedLogEntry, rule *Rule) []*models.Alert {
	var alerts []*models.Alert

	for _, action := range rule.Actions {
		if action.Type == "alert" {
			// Get severity from action parameters
			severity := "medium"
			if sev, ok := action.Parameters["severity"].(string); ok {
				severity = sev
			}

			alert := &models.Alert{
				ID:        fmt.Sprintf("%s-%s-%d", rule.Name, entry.ID, time.Now().Unix()),
				RuleName:  rule.Name,
				Severity:  severity,
				Message:   fmt.Sprintf("Rule '%s' triggered", rule.Name),
				Timestamp: time.Now(),
				Source:    entry.Source,
				EntryID:   entry.ID,
				Fields:    make(map[string]interface{}),
				Tags:      []string{"rule_alert"},
			}

			// Add relevant fields from the log entry
			alert.Fields["original_message"] = entry.Message
			alert.Fields["original_level"] = entry.Level
			alert.Fields["rule_condition"] = rule.Condition

			// Add any additional fields from the action parameters
			for k, v := range action.Parameters {
				alert.Fields[k] = v
			}

			alerts = append(alerts, alert)
		}
	}

	return alerts
}

// AddRuleSet adds a new rule set to the engine
func (e *Engine) AddRuleSet(ruleSet *RuleSet) {
	e.ruleSets[ruleSet.Name] = ruleSet
}

// RemoveRuleSet removes a rule set from the engine
func (e *Engine) RemoveRuleSet(name string) {
	delete(e.ruleSets, name)
}

// GetRuleSet returns a rule set by name
func (e *Engine) GetRuleSet(name string) *RuleSet {
	return e.ruleSets[name]
}

// ListRuleSets returns all rule sets
func (e *Engine) ListRuleSets() map[string]*RuleSet {
	return e.ruleSets
}

// GetStats returns engine statistics
func (e *Engine) GetStats() EngineStats {
	totalRules := 0
	enabledRules := 0

	for _, ruleSet := range e.ruleSets {
		if !ruleSet.Enabled {
			continue
		}
		for _, rule := range ruleSet.Rules {
			totalRules++
			if rule.Enabled {
				enabledRules++
			}
		}
	}

	return EngineStats{
		EventsProcessed: 0, // This would be tracked in a real implementation
		RulesEvaluated:  0,
		RulesMatched:    0,
		ActionsExecuted: 0,
		Errors:          0,
		AverageLatency:  0,
		RuleStats:       make(map[string]*RuleStats),
		LastReset:       time.Now(),
	}
}

// RuleEvaluationContext provides context for rule evaluation
type RuleEvaluationContext struct {
	Entry     *models.ProcessedLogEntry
	Timestamp time.Time
	Metadata  map[string]interface{}
}

// AdvancedRuleEngine provides more sophisticated rule evaluation
type AdvancedRuleEngine struct {
	*Engine
	evaluationHistory map[string][]time.Time
	rateLimits        map[string]RateLimit
}

// RateLimit defines rate limiting for rules
type RateLimit struct {
	MaxOccurrences int           `json:"max_occurrences"`
	TimeWindow     time.Duration `json:"time_window"`
}

// NewAdvancedRuleEngine creates a new advanced rules engine
func NewAdvancedRuleEngine(cfg *config.Config, logger *zap.Logger) (*AdvancedRuleEngine, error) {
	baseEngine, err := NewEngine(cfg, logger)
	if err != nil {
		return nil, err
	}

	return &AdvancedRuleEngine{
		Engine:            baseEngine,
		evaluationHistory: make(map[string][]time.Time),
		rateLimits:        make(map[string]RateLimit),
	}, nil
}

// EvaluateWithRateLimit evaluates rules with rate limiting
func (are *AdvancedRuleEngine) EvaluateWithRateLimit(ctx context.Context, entry *models.ProcessedLogEntry) ([]*models.Alert, error) {
	alerts, err := are.Evaluate(ctx, entry)
	if err != nil {
		return nil, err
	}

	// Apply rate limiting
	var filteredAlerts []*models.Alert
	for _, alert := range alerts {
		if are.checkRateLimit(alert.RuleName) {
			filteredAlerts = append(filteredAlerts, alert)
		}
	}

	return filteredAlerts, nil
}

// checkRateLimit checks if a rule has exceeded its rate limit
func (are *AdvancedRuleEngine) checkRateLimit(ruleName string) bool {
	rateLimit, exists := are.rateLimits[ruleName]
	if !exists {
		// No rate limit configured, allow
		return true
	}

	now := time.Now()
	history := are.evaluationHistory[ruleName]

	// Remove old entries outside the time window
	var recentHistory []time.Time
	for _, timestamp := range history {
		if now.Sub(timestamp) <= rateLimit.TimeWindow {
			recentHistory = append(recentHistory, timestamp)
		}
	}

	// Check if we're under the limit
	if len(recentHistory) < rateLimit.MaxOccurrences {
		recentHistory = append(recentHistory, now)
		are.evaluationHistory[ruleName] = recentHistory
		return true
	}

	return false
}

// compareValues compares two values using the given operator
func (e *Engine) compareValues(field interface{}, operator string, value interface{}) bool {
	switch operator {
	case "==":
		return fmt.Sprintf("%v", field) == fmt.Sprintf("%v", value)
	case "!=":
		return fmt.Sprintf("%v", field) != fmt.Sprintf("%v", value)
	case ">":
		return compareNumeric(field, value, func(a, b float64) bool { return a > b })
	case ">=":
		return compareNumeric(field, value, func(a, b float64) bool { return a >= b })
	case "<":
		return compareNumeric(field, value, func(a, b float64) bool { return a < b })
	case "<=":
		return compareNumeric(field, value, func(a, b float64) bool { return a <= b })
	case "contains":
		return strings.Contains(fmt.Sprintf("%v", field), fmt.Sprintf("%v", value))
	case "matches":
		pattern, err := regexp.Compile(fmt.Sprintf("%v", value))
		if err != nil {
			return false
		}
		return pattern.MatchString(fmt.Sprintf("%v", field))
	default:
		return false
	}
}

// compareNumeric compares two values numerically
func compareNumeric(a, b interface{}, compare func(float64, float64) bool) bool {
	aFloat, aErr := parseFloat(a)
	bFloat, bErr := parseFloat(b)
	if aErr != nil || bErr != nil {
		return false
	}
	return compare(aFloat, bFloat)
}

// parseFloat converts an interface{} to float64
func parseFloat(v interface{}) (float64, error) {
	switch val := v.(type) {
	case float64:
		return val, nil
	case float32:
		return float64(val), nil
	case int:
		return float64(val), nil
	case int64:
		return float64(val), nil
	case string:
		return strconv.ParseFloat(val, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to float64", v)
	}
}

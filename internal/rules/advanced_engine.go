package rules

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// AdvancedEngine provides advanced rule processing capabilities
type AdvancedEngine struct {
	config      config.RulesConfig
	logger      *zap.Logger
	rules       map[string]*AdvancedRule
	ruleGroups  map[string]*RuleGroup
	mutex       sync.RWMutex
	stats       *EngineStats
	eventBuffer chan *ProcessingEvent
	stopCh      chan struct{}
	workers     []*RuleWorker
}

// AdvancedRule represents a complex rule with multiple conditions and actions
type AdvancedRule struct {
	ID          string                 `json:"id" yaml:"id"`
	Name        string                 `json:"name" yaml:"name"`
	Description string                 `json:"description" yaml:"description"`
	Enabled     bool                   `json:"enabled" yaml:"enabled"`
	Priority    int                    `json:"priority" yaml:"priority"`
	Category    string                 `json:"category" yaml:"category"`
	Tags        []string               `json:"tags" yaml:"tags"`
	Conditions  []RuleCondition        `json:"conditions" yaml:"conditions"`
	Actions     []RuleAction           `json:"actions" yaml:"actions"`
	Metadata    map[string]interface{} `json:"metadata" yaml:"metadata"`

	// Advanced features
	Aggregation *AggregationConfig `json:"aggregation,omitempty" yaml:"aggregation,omitempty"`
	Correlation *CorrelationConfig `json:"correlation,omitempty" yaml:"correlation,omitempty"`
	Suppression *SuppressionConfig `json:"suppression,omitempty" yaml:"suppression,omitempty"`
	Enrichment  *EnrichmentConfig  `json:"enrichment,omitempty" yaml:"enrichment,omitempty"`
	Schedule    *ScheduleConfig    `json:"schedule,omitempty" yaml:"schedule,omitempty"`

	// Runtime state
	LastTriggered time.Time `json:"last_triggered"`
	TriggerCount  int64     `json:"trigger_count"`
	compiledRegex map[string]*regexp.Regexp
	mutex         sync.RWMutex
}

// RuleCondition represents a single condition in a rule
type RuleCondition struct {
	Field    string      `json:"field" yaml:"field"`
	Operator string      `json:"operator" yaml:"operator"` // eq, ne, gt, lt, gte, lte, in, not_in, regex, contains, starts_with, ends_with
	Value    interface{} `json:"value" yaml:"value"`
	LogicOp  string      `json:"logic_op,omitempty" yaml:"logic_op,omitempty"` // and, or, not
	Negate   bool        `json:"negate,omitempty" yaml:"negate,omitempty"`
}

// RuleAction represents an action to take when a rule matches
type RuleAction struct {
	Type       string                 `json:"type" yaml:"type"` // alert, log, enrich, transform, forward, block
	Parameters map[string]interface{} `json:"parameters" yaml:"parameters"`
	Enabled    bool                   `json:"enabled" yaml:"enabled"`
}

// AggregationConfig configures event aggregation
type AggregationConfig struct {
	Enabled   bool          `json:"enabled" yaml:"enabled"`
	Window    time.Duration `json:"window" yaml:"window"`
	GroupBy   []string      `json:"group_by" yaml:"group_by"`
	Function  string        `json:"function" yaml:"function"` // count, sum, avg, min, max
	Threshold float64       `json:"threshold" yaml:"threshold"`
	Field     string        `json:"field,omitempty" yaml:"field,omitempty"`
}

// CorrelationConfig configures event correlation
type CorrelationConfig struct {
	Enabled     bool          `json:"enabled" yaml:"enabled"`
	Window      time.Duration `json:"window" yaml:"window"`
	Rules       []string      `json:"rules" yaml:"rules"`
	RequireAll  bool          `json:"require_all" yaml:"require_all"`
	CorrelateBy []string      `json:"correlate_by" yaml:"correlate_by"`
}

// SuppressionConfig configures alert suppression
type SuppressionConfig struct {
	Enabled   bool          `json:"enabled" yaml:"enabled"`
	Window    time.Duration `json:"window" yaml:"window"`
	MaxAlerts int           `json:"max_alerts" yaml:"max_alerts"`
	GroupBy   []string      `json:"group_by" yaml:"group_by"`
}

// EnrichmentConfig configures event enrichment
type EnrichmentConfig struct {
	Enabled bool                   `json:"enabled" yaml:"enabled"`
	Fields  map[string]interface{} `json:"fields" yaml:"fields"`
	Sources []string               `json:"sources" yaml:"sources"`
}

// ScheduleConfig configures rule scheduling
type ScheduleConfig struct {
	Enabled    bool   `json:"enabled" yaml:"enabled"`
	Cron       string `json:"cron" yaml:"cron"`
	TimeZone   string `json:"timezone" yaml:"timezone"`
	DaysOfWeek []int  `json:"days_of_week" yaml:"days_of_week"`
	StartTime  string `json:"start_time" yaml:"start_time"`
	EndTime    string `json:"end_time" yaml:"end_time"`
}

// RuleGroup represents a group of related rules
type RuleGroup struct {
	ID          string                 `json:"id" yaml:"id"`
	Name        string                 `json:"name" yaml:"name"`
	Description string                 `json:"description" yaml:"description"`
	Enabled     bool                   `json:"enabled" yaml:"enabled"`
	Rules       []string               `json:"rules" yaml:"rules"`
	Priority    int                    `json:"priority" yaml:"priority"`
	Metadata    map[string]interface{} `json:"metadata" yaml:"metadata"`
}

// ProcessingEvent represents an event being processed
type ProcessingEvent struct {
	ID        string                 `json:"id"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// RuleResult represents the result of rule evaluation
type RuleResult struct {
	RuleID    string                 `json:"rule_id"`
	RuleName  string                 `json:"rule_name"`
	Matched   bool                   `json:"matched"`
	Actions   []ActionResult         `json:"actions"`
	Duration  time.Duration          `json:"duration"`
	Error     error                  `json:"error,omitempty"`
	Metadata  map[string]interface{} `json:"metadata"`
	Timestamp time.Time              `json:"timestamp"`
}

// ActionResult represents the result of an action execution
type ActionResult struct {
	Type     string                 `json:"type"`
	Success  bool                   `json:"success"`
	Error    error                  `json:"error,omitempty"`
	Output   map[string]interface{} `json:"output,omitempty"`
	Duration time.Duration          `json:"duration"`
}

// EngineStats tracks engine statistics
type EngineStats struct {
	EventsProcessed int64                 `json:"events_processed"`
	RulesEvaluated  int64                 `json:"rules_evaluated"`
	RulesMatched    int64                 `json:"rules_matched"`
	ActionsExecuted int64                 `json:"actions_executed"`
	Errors          int64                 `json:"errors"`
	AverageLatency  time.Duration         `json:"average_latency"`
	RuleStats       map[string]*RuleStats `json:"rule_stats"`
	LastReset       time.Time             `json:"last_reset"`
	mutex           sync.RWMutex
}

// RuleStats tracks statistics for individual rules
type RuleStats struct {
	Evaluations   int64         `json:"evaluations"`
	Matches       int64         `json:"matches"`
	Errors        int64         `json:"errors"`
	LastTriggered time.Time     `json:"last_triggered"`
	AvgLatency    time.Duration `json:"avg_latency"`
}

// RuleWorker processes events through rules
type RuleWorker struct {
	id       int
	engine   *AdvancedEngine
	eventCh  chan *ProcessingEvent
	resultCh chan *RuleResult
	stopCh   chan struct{}
	logger   *zap.Logger
}

// NewAdvancedEngine creates a new advanced rules engine
func NewAdvancedEngine(cfg config.RulesConfig, logger *zap.Logger) (*AdvancedEngine, error) {
	engine := &AdvancedEngine{
		config:     cfg,
		logger:     logger,
		rules:      make(map[string]*AdvancedRule),
		ruleGroups: make(map[string]*RuleGroup),
		stats: &EngineStats{
			RuleStats: make(map[string]*RuleStats),
			LastReset: time.Now(),
		},
		eventBuffer: make(chan *ProcessingEvent, cfg.BufferSize),
		stopCh:      make(chan struct{}),
	}

	// Load rules if enabled
	if cfg.Enabled && cfg.RulesDir != "" {
		if err := engine.LoadRules(); err != nil {
			return nil, fmt.Errorf("failed to load rules: %w", err)
		}
	}

	return engine, nil
}

// Start starts the advanced rules engine
func (ae *AdvancedEngine) Start(ctx context.Context) error {
	if !ae.config.Enabled {
		ae.logger.Info("Advanced rules engine is disabled")
		return nil
	}

	ae.logger.Info("Starting advanced rules engine",
		zap.Int("workers", ae.config.Workers),
		zap.Int("buffer_size", ae.config.BufferSize))

	// Start worker goroutines
	for i := 0; i < ae.config.Workers; i++ {
		worker := &RuleWorker{
			id:       i,
			engine:   ae,
			eventCh:  make(chan *ProcessingEvent, 100),
			resultCh: make(chan *RuleResult, 100),
			stopCh:   make(chan struct{}),
			logger:   ae.logger.With(zap.Int("worker_id", i)),
		}
		ae.workers = append(ae.workers, worker)
		go worker.run(ctx)
	}

	// Start event distributor
	go ae.distributeEvents(ctx)

	return nil
}

// Stop stops the advanced rules engine
func (ae *AdvancedEngine) Stop() {
	ae.logger.Info("Stopping advanced rules engine")
	close(ae.stopCh)

	// Stop all workers
	for _, worker := range ae.workers {
		close(worker.stopCh)
	}
}

// ProcessEvent processes a single event through the rules engine
func (ae *AdvancedEngine) ProcessEvent(ctx context.Context, event map[string]interface{}) ([]*RuleResult, error) {
	if !ae.config.Enabled {
		return nil, nil
	}

	processingEvent := &ProcessingEvent{
		ID:        generateEventID(),
		Data:      event,
		Timestamp: time.Now(),
		Source:    "streaming",
		Metadata:  make(map[string]interface{}),
	}

	select {
	case ae.eventBuffer <- processingEvent:
		// Event queued successfully
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
		// Buffer full, process synchronously
		return ae.processEventSync(ctx, processingEvent)
	}

	return nil, nil
}

// processEventSync processes an event synchronously
func (ae *AdvancedEngine) processEventSync(ctx context.Context, event *ProcessingEvent) ([]*RuleResult, error) {
	ae.mutex.RLock()
	rules := make([]*AdvancedRule, 0, len(ae.rules))
	for _, rule := range ae.rules {
		if rule.Enabled {
			rules = append(rules, rule)
		}
	}
	ae.mutex.RUnlock()

	var results []*RuleResult
	for _, rule := range rules {
		result := ae.evaluateRule(ctx, rule, event)
		if result != nil {
			results = append(results, result)
		}
	}

	ae.updateStats(len(rules), len(results))
	return results, nil
}

// evaluateRule evaluates a single rule against an event
func (ae *AdvancedEngine) evaluateRule(ctx context.Context, rule *AdvancedRule, event *ProcessingEvent) *RuleResult {
	start := time.Now()
	result := &RuleResult{
		RuleID:    rule.ID,
		RuleName:  rule.Name,
		Timestamp: start,
		Metadata:  make(map[string]interface{}),
	}

	// Check if rule should run based on schedule
	if rule.Schedule != nil && rule.Schedule.Enabled {
		if !ae.shouldRunRule(rule) {
			return nil
		}
	}

	// Evaluate conditions
	matched, err := ae.evaluateConditions(rule.Conditions, event.Data)
	if err != nil {
		result.Error = err
		ae.updateRuleStats(rule.ID, false, true)
		return result
	}

	result.Matched = matched
	result.Duration = time.Since(start)

	if matched {
		// Execute actions
		result.Actions = ae.executeActions(ctx, rule.Actions, event)

		// Update rule state
		rule.mutex.Lock()
		rule.LastTriggered = time.Now()
		rule.TriggerCount++
		rule.mutex.Unlock()

		ae.updateRuleStats(rule.ID, true, false)
	} else {
		ae.updateRuleStats(rule.ID, false, false)
	}

	return result
}

// evaluateConditions evaluates all conditions for a rule
func (ae *AdvancedEngine) evaluateConditions(conditions []RuleCondition, data map[string]interface{}) (bool, error) {
	if len(conditions) == 0 {
		return true, nil
	}

	result := true
	currentLogicOp := "and"

	for i, condition := range conditions {
		conditionResult, err := ae.evaluateCondition(condition, data)
		if err != nil {
			return false, err
		}

		if condition.Negate {
			conditionResult = !conditionResult
		}

		if i == 0 {
			result = conditionResult
		} else {
			switch currentLogicOp {
			case "and":
				result = result && conditionResult
			case "or":
				result = result || conditionResult
			case "not":
				result = result && !conditionResult
			}
		}

		// Set logic operator for next iteration
		if condition.LogicOp != "" {
			currentLogicOp = condition.LogicOp
		}
	}

	return result, nil
}

// evaluateCondition evaluates a single condition
func (ae *AdvancedEngine) evaluateCondition(condition RuleCondition, data map[string]interface{}) (bool, error) {
	fieldValue := getNestedValue(data, condition.Field)

	switch condition.Operator {
	case "eq":
		return compareValues(fieldValue, condition.Value, "eq"), nil
	case "ne":
		return compareValues(fieldValue, condition.Value, "ne"), nil
	case "gt":
		return compareValues(fieldValue, condition.Value, "gt"), nil
	case "lt":
		return compareValues(fieldValue, condition.Value, "lt"), nil
	case "gte":
		return compareValues(fieldValue, condition.Value, "gte"), nil
	case "lte":
		return compareValues(fieldValue, condition.Value, "lte"), nil
	case "in":
		return valueInSlice(fieldValue, condition.Value), nil
	case "not_in":
		return !valueInSlice(fieldValue, condition.Value), nil
	case "regex":
		return ae.matchRegex(condition.Value, fieldValue)
	case "contains":
		return strings.Contains(fmt.Sprintf("%v", fieldValue), fmt.Sprintf("%v", condition.Value)), nil
	case "starts_with":
		return strings.HasPrefix(fmt.Sprintf("%v", fieldValue), fmt.Sprintf("%v", condition.Value)), nil
	case "ends_with":
		return strings.HasSuffix(fmt.Sprintf("%v", fieldValue), fmt.Sprintf("%v", condition.Value)), nil
	default:
		return false, fmt.Errorf("unsupported operator: %s", condition.Operator)
	}
}

// executeActions executes all actions for a matched rule
func (ae *AdvancedEngine) executeActions(ctx context.Context, actions []RuleAction, event *ProcessingEvent) []ActionResult {
	var results []ActionResult

	for _, action := range actions {
		if !action.Enabled {
			continue
		}

		start := time.Now()
		result := ActionResult{
			Type: action.Type,
		}

		switch action.Type {
		case "alert":
			result = ae.executeAlertAction(action, event)
		case "log":
			result = ae.executeLogAction(action, event)
		case "enrich":
			result = ae.executeEnrichAction(action, event)
		case "transform":
			result = ae.executeTransformAction(action, event)
		case "forward":
			result = ae.executeForwardAction(action, event)
		case "block":
			result = ae.executeBlockAction(action, event)
		default:
			result.Error = fmt.Errorf("unsupported action type: %s", action.Type)
		}

		result.Duration = time.Since(start)
		results = append(results, result)
	}

	return results
}

// Helper functions

func generateEventID() string {
	return fmt.Sprintf("evt_%d", time.Now().UnixNano())
}

func getNestedValue(data map[string]interface{}, field string) interface{} {
	parts := strings.Split(field, ".")
	current := data

	for i, part := range parts {
		if i == len(parts)-1 {
			return current[part]
		}

		if next, ok := current[part].(map[string]interface{}); ok {
			current = next
		} else {
			return nil
		}
	}

	return nil
}

func compareValues(a, b interface{}, op string) bool {
	// Convert to strings for comparison
	aStr := fmt.Sprintf("%v", a)
	bStr := fmt.Sprintf("%v", b)

	// Try numeric comparison first
	if aNum, aErr := strconv.ParseFloat(aStr, 64); aErr == nil {
		if bNum, bErr := strconv.ParseFloat(bStr, 64); bErr == nil {
			switch op {
			case "eq":
				return aNum == bNum
			case "ne":
				return aNum != bNum
			case "gt":
				return aNum > bNum
			case "lt":
				return aNum < bNum
			case "gte":
				return aNum >= bNum
			case "lte":
				return aNum <= bNum
			}
		}
	}

	// Fall back to string comparison
	switch op {
	case "eq":
		return aStr == bStr
	case "ne":
		return aStr != bStr
	case "gt":
		return aStr > bStr
	case "lt":
		return aStr < bStr
	case "gte":
		return aStr >= bStr
	case "lte":
		return aStr <= bStr
	}

	return false
}

func valueInSlice(value interface{}, slice interface{}) bool {
	valueStr := fmt.Sprintf("%v", value)

	switch s := slice.(type) {
	case []interface{}:
		for _, item := range s {
			if fmt.Sprintf("%v", item) == valueStr {
				return true
			}
		}
	case []string:
		for _, item := range s {
			if item == valueStr {
				return true
			}
		}
	}

	return false
}

// LoadRules loads rules from the configured directory
func (ae *AdvancedEngine) LoadRules() error {
	ae.logger.Info("Loading advanced rules", zap.String("rules_dir", ae.config.RulesDir))

	// Implementation would load rules from YAML/JSON files
	// For now, create a sample rule
	sampleRule := &AdvancedRule{
		ID:          "sample_rule_001",
		Name:        "High Severity Security Event",
		Description: "Detects high severity security events",
		Enabled:     true,
		Priority:    1,
		Category:    "security",
		Tags:        []string{"security", "high-priority"},
		Conditions: []RuleCondition{
			{
				Field:    "severity_id",
				Operator: "gte",
				Value:    4,
			},
			{
				Field:    "category_name",
				Operator: "eq",
				Value:    "Security",
				LogicOp:  "and",
			},
		},
		Actions: []RuleAction{
			{
				Type:    "alert",
				Enabled: true,
				Parameters: map[string]interface{}{
					"severity": "high",
					"message":  "High severity security event detected",
				},
			},
			{
				Type:    "log",
				Enabled: true,
				Parameters: map[string]interface{}{
					"level": "warn",
				},
			},
		},
		compiledRegex: make(map[string]*regexp.Regexp),
	}

	ae.mutex.Lock()
	ae.rules[sampleRule.ID] = sampleRule
	ae.stats.RuleStats[sampleRule.ID] = &RuleStats{}
	ae.mutex.Unlock()

	ae.logger.Info("Advanced rules loaded", zap.Int("count", len(ae.rules)))
	return nil
}

// matchRegex matches a value against a regex pattern
func (ae *AdvancedEngine) matchRegex(pattern interface{}, value interface{}) (bool, error) {
	patternStr := fmt.Sprintf("%v", pattern)
	valueStr := fmt.Sprintf("%v", value)

	regex, err := regexp.Compile(patternStr)
	if err != nil {
		return false, fmt.Errorf("invalid regex pattern: %w", err)
	}

	return regex.MatchString(valueStr), nil
}

// shouldRunRule checks if a rule should run based on its schedule
func (ae *AdvancedEngine) shouldRunRule(rule *AdvancedRule) bool {
	if rule.Schedule == nil || !rule.Schedule.Enabled {
		return true
	}

	now := time.Now()

	// Check days of week
	if len(rule.Schedule.DaysOfWeek) > 0 {
		weekday := int(now.Weekday())
		found := false
		for _, day := range rule.Schedule.DaysOfWeek {
			if day == weekday {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Check time range
	if rule.Schedule.StartTime != "" && rule.Schedule.EndTime != "" {
		currentTime := now.Format("15:04")
		if currentTime < rule.Schedule.StartTime || currentTime > rule.Schedule.EndTime {
			return false
		}
	}

	return true
}

// distributeEvents distributes events to workers
func (ae *AdvancedEngine) distributeEvents(ctx context.Context) {
	workerIndex := 0

	for {
		select {
		case event := <-ae.eventBuffer:
			if len(ae.workers) > 0 {
				worker := ae.workers[workerIndex]
				select {
				case worker.eventCh <- event:
					workerIndex = (workerIndex + 1) % len(ae.workers)
				default:
					// Worker buffer full, try next worker
					workerIndex = (workerIndex + 1) % len(ae.workers)
					ae.workers[workerIndex].eventCh <- event
				}
			}
		case <-ae.stopCh:
			return
		case <-ctx.Done():
			return
		}
	}
}

// updateStats updates engine statistics
func (ae *AdvancedEngine) updateStats(rulesEvaluated, rulesMatched int) {
	ae.stats.mutex.Lock()
	defer ae.stats.mutex.Unlock()

	ae.stats.EventsProcessed++
	ae.stats.RulesEvaluated += int64(rulesEvaluated)
	ae.stats.RulesMatched += int64(rulesMatched)
}

// updateRuleStats updates statistics for a specific rule
func (ae *AdvancedEngine) updateRuleStats(ruleID string, matched, hasError bool) {
	ae.stats.mutex.Lock()
	defer ae.stats.mutex.Unlock()

	if stats, exists := ae.stats.RuleStats[ruleID]; exists {
		stats.Evaluations++
		if matched {
			stats.Matches++
			stats.LastTriggered = time.Now()
		}
		if hasError {
			stats.Errors++
		}
	}
}

// Action execution methods
func (ae *AdvancedEngine) executeAlertAction(action RuleAction, event *ProcessingEvent) ActionResult {
	result := ActionResult{Type: "alert", Success: true}

	// Create alert
	alert := map[string]interface{}{
		"timestamp": time.Now(),
		"event_id":  event.ID,
		"severity":  action.Parameters["severity"],
		"message":   action.Parameters["message"],
		"event":     event.Data,
	}

	result.Output = alert
	ae.logger.Warn("Rule alert triggered", zap.Any("alert", alert))

	return result
}

func (ae *AdvancedEngine) executeLogAction(action RuleAction, event *ProcessingEvent) ActionResult {
	result := ActionResult{Type: "log", Success: true}

	level := "info"
	if l, ok := action.Parameters["level"].(string); ok {
		level = l
	}

	message := fmt.Sprintf("Rule action: %s", event.ID)
	if m, ok := action.Parameters["message"].(string); ok {
		message = m
	}

	switch level {
	case "debug":
		ae.logger.Debug(message, zap.Any("event", event.Data))
	case "info":
		ae.logger.Info(message, zap.Any("event", event.Data))
	case "warn":
		ae.logger.Warn(message, zap.Any("event", event.Data))
	case "error":
		ae.logger.Error(message, zap.Any("event", event.Data))
	}

	return result
}

func (ae *AdvancedEngine) executeEnrichAction(action RuleAction, event *ProcessingEvent) ActionResult {
	result := ActionResult{Type: "enrich", Success: true}

	// Add enrichment fields to event
	if fields, ok := action.Parameters["fields"].(map[string]interface{}); ok {
		for k, v := range fields {
			event.Data[k] = v
		}
		result.Output = map[string]interface{}{
			"enriched_fields": len(fields),
		}
	}

	return result
}

func (ae *AdvancedEngine) executeTransformAction(action RuleAction, event *ProcessingEvent) ActionResult {
	result := ActionResult{Type: "transform", Success: true}

	// Apply transformations to event data
	if transforms, ok := action.Parameters["transforms"].(map[string]interface{}); ok {
		for field, transform := range transforms {
			if transformStr, ok := transform.(string); ok {
				// Simple string replacement transformation
				if currentValue, exists := event.Data[field]; exists {
					event.Data[field] = strings.ReplaceAll(fmt.Sprintf("%v", currentValue), "{{transform}}", transformStr)
				}
			}
		}
	}

	return result
}

func (ae *AdvancedEngine) executeForwardAction(action RuleAction, event *ProcessingEvent) ActionResult {
	result := ActionResult{Type: "forward", Success: true}

	// Forward event to specified destination
	destination := "default"
	if dest, ok := action.Parameters["destination"].(string); ok {
		destination = dest
	}

	ae.logger.Info("Forwarding event",
		zap.String("destination", destination),
		zap.String("event_id", event.ID))

	result.Output = map[string]interface{}{
		"destination": destination,
		"forwarded":   true,
	}

	return result
}

func (ae *AdvancedEngine) executeBlockAction(action RuleAction, event *ProcessingEvent) ActionResult {
	result := ActionResult{Type: "block", Success: true}

	// Block/drop the event
	reason := "Rule matched"
	if r, ok := action.Parameters["reason"].(string); ok {
		reason = r
	}

	ae.logger.Info("Blocking event",
		zap.String("reason", reason),
		zap.String("event_id", event.ID))

	result.Output = map[string]interface{}{
		"blocked": true,
		"reason":  reason,
	}

	return result
}

// RuleWorker methods

// run starts the worker processing loop
func (rw *RuleWorker) run(ctx context.Context) {
	rw.logger.Info("Starting rule worker")

	for {
		select {
		case event := <-rw.eventCh:
			results := rw.processEvent(ctx, event)
			for _, result := range results {
				select {
				case rw.resultCh <- result:
				default:
					rw.logger.Warn("Result channel full, dropping result")
				}
			}
		case <-rw.stopCh:
			rw.logger.Info("Stopping rule worker")
			return
		case <-ctx.Done():
			rw.logger.Info("Worker context cancelled")
			return
		}
	}
}

// processEvent processes an event through all enabled rules
func (rw *RuleWorker) processEvent(ctx context.Context, event *ProcessingEvent) []*RuleResult {
	rw.engine.mutex.RLock()
	rules := make([]*AdvancedRule, 0, len(rw.engine.rules))
	for _, rule := range rw.engine.rules {
		if rule.Enabled {
			rules = append(rules, rule)
		}
	}
	rw.engine.mutex.RUnlock()

	var results []*RuleResult
	for _, rule := range rules {
		result := rw.engine.evaluateRule(ctx, rule, event)
		if result != nil {
			results = append(results, result)
		}
	}

	rw.engine.updateStats(len(rules), len(results))
	return results
}

// Advanced Engine Management Methods

// AddRule adds a new rule to the engine
func (ae *AdvancedEngine) AddRule(rule *AdvancedRule) error {
	if rule.ID == "" {
		return fmt.Errorf("rule ID cannot be empty")
	}

	ae.mutex.Lock()
	defer ae.mutex.Unlock()

	if _, exists := ae.rules[rule.ID]; exists {
		return fmt.Errorf("rule with ID %s already exists", rule.ID)
	}

	// Initialize compiled regex map
	if rule.compiledRegex == nil {
		rule.compiledRegex = make(map[string]*regexp.Regexp)
	}

	ae.rules[rule.ID] = rule
	ae.stats.RuleStats[rule.ID] = &RuleStats{}

	ae.logger.Info("Rule added", zap.String("rule_id", rule.ID), zap.String("rule_name", rule.Name))
	return nil
}

// UpdateRule updates an existing rule
func (ae *AdvancedEngine) UpdateRule(rule *AdvancedRule) error {
	if rule.ID == "" {
		return fmt.Errorf("rule ID cannot be empty")
	}

	ae.mutex.Lock()
	defer ae.mutex.Unlock()

	if _, exists := ae.rules[rule.ID]; !exists {
		return fmt.Errorf("rule with ID %s does not exist", rule.ID)
	}

	// Initialize compiled regex map
	if rule.compiledRegex == nil {
		rule.compiledRegex = make(map[string]*regexp.Regexp)
	}

	ae.rules[rule.ID] = rule

	ae.logger.Info("Rule updated", zap.String("rule_id", rule.ID), zap.String("rule_name", rule.Name))
	return nil
}

// RemoveRule removes a rule from the engine
func (ae *AdvancedEngine) RemoveRule(ruleID string) error {
	ae.mutex.Lock()
	defer ae.mutex.Unlock()

	if _, exists := ae.rules[ruleID]; !exists {
		return fmt.Errorf("rule with ID %s does not exist", ruleID)
	}

	delete(ae.rules, ruleID)
	delete(ae.stats.RuleStats, ruleID)

	ae.logger.Info("Rule removed", zap.String("rule_id", ruleID))
	return nil
}

// GetRule retrieves a rule by ID
func (ae *AdvancedEngine) GetRule(ruleID string) (*AdvancedRule, error) {
	ae.mutex.RLock()
	defer ae.mutex.RUnlock()

	rule, exists := ae.rules[ruleID]
	if !exists {
		return nil, fmt.Errorf("rule with ID %s does not exist", ruleID)
	}

	return rule, nil
}

// ListRules returns all rules
func (ae *AdvancedEngine) ListRules() []*AdvancedRule {
	ae.mutex.RLock()
	defer ae.mutex.RUnlock()

	rules := make([]*AdvancedRule, 0, len(ae.rules))
	for _, rule := range ae.rules {
		rules = append(rules, rule)
	}

	return rules
}

// EnableRule enables a rule
func (ae *AdvancedEngine) EnableRule(ruleID string) error {
	ae.mutex.Lock()
	defer ae.mutex.Unlock()

	rule, exists := ae.rules[ruleID]
	if !exists {
		return fmt.Errorf("rule with ID %s does not exist", ruleID)
	}

	rule.Enabled = true
	ae.logger.Info("Rule enabled", zap.String("rule_id", ruleID))
	return nil
}

// DisableRule disables a rule
func (ae *AdvancedEngine) DisableRule(ruleID string) error {
	ae.mutex.Lock()
	defer ae.mutex.Unlock()

	rule, exists := ae.rules[ruleID]
	if !exists {
		return fmt.Errorf("rule with ID %s does not exist", ruleID)
	}

	rule.Enabled = false
	ae.logger.Info("Rule disabled", zap.String("rule_id", ruleID))
	return nil
}

// GetStats returns engine statistics
func (ae *AdvancedEngine) GetStats() *EngineStats {
	ae.stats.mutex.RLock()
	defer ae.stats.mutex.RUnlock()

	// Create a copy to avoid race conditions
	stats := &EngineStats{
		EventsProcessed: ae.stats.EventsProcessed,
		RulesEvaluated:  ae.stats.RulesEvaluated,
		RulesMatched:    ae.stats.RulesMatched,
		ActionsExecuted: ae.stats.ActionsExecuted,
		Errors:          ae.stats.Errors,
		AverageLatency:  ae.stats.AverageLatency,
		LastReset:       ae.stats.LastReset,
		RuleStats:       make(map[string]*RuleStats),
	}

	for ruleID, ruleStats := range ae.stats.RuleStats {
		stats.RuleStats[ruleID] = &RuleStats{
			Evaluations:   ruleStats.Evaluations,
			Matches:       ruleStats.Matches,
			Errors:        ruleStats.Errors,
			LastTriggered: ruleStats.LastTriggered,
			AvgLatency:    ruleStats.AvgLatency,
		}
	}

	return stats
}

// ResetStats resets engine statistics
func (ae *AdvancedEngine) ResetStats() {
	ae.stats.mutex.Lock()
	defer ae.stats.mutex.Unlock()

	ae.stats.EventsProcessed = 0
	ae.stats.RulesEvaluated = 0
	ae.stats.RulesMatched = 0
	ae.stats.ActionsExecuted = 0
	ae.stats.Errors = 0
	ae.stats.AverageLatency = 0
	ae.stats.LastReset = time.Now()

	for ruleID := range ae.stats.RuleStats {
		ae.stats.RuleStats[ruleID] = &RuleStats{}
	}

	ae.logger.Info("Engine statistics reset")
}

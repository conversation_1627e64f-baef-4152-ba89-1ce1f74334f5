package rules

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/models"
)

// Simple test helpers
func assertEqual(t *testing.T, expected, actual interface{}) {
	t.Helper()
	if expected != actual {
		t.<PERSON>rf("Expected %v, got %v", expected, actual)
	}
}

func assertNotNil(t *testing.T, value interface{}) {
	t.<PERSON>er()
	if value == nil {
		t.Error("Expected non-nil value")
	}
}

func assertNoError(t *testing.T, err error) {
	t.<PERSON>er()
	if err != nil {
		t.<PERSON><PERSON>("Expected no error, got %v", err)
	}
}

func assertLen(t *testing.T, slice interface{}, expectedLen int) {
	t.<PERSON><PERSON>()
	switch s := slice.(type) {
	case []*models.Alert:
		if len(s) != expectedLen {
			t.<PERSON>rrorf("Expected length %d, got %d", expectedLen, len(s))
		}
	case []Rule:
		if len(s) != expectedLen {
			t.<PERSON>("Expected length %d, got %d", expectedLen, len(s))
		}
	case []RuleSet:
		if len(s) != expectedLen {
			t.Errorf("Expected length %d, got %d", expectedLen, len(s))
		}
	case []*RuleSet:
		if len(s) != expectedLen {
			t.Errorf("Expected length %d, got %d", expectedLen, len(s))
		}
	default:
		t.Error("Unsupported slice type for assertLen")
	}
}

func TestNewEngine(t *testing.T) {
	cfg := &config.Config{
		Rules: config.RulesConfig{
			RulesDir:   "/tmp/rules",
			BufferSize: 1000,
		},
	}
	logger := zap.NewNop()

	engine, err := NewEngine(cfg, logger)
	assertNoError(t, err)
	assertNotNil(t, engine)
	assertNotNil(t, engine.ruleSets)
	// Engine created successfully
}

func TestNewEngine_Errors(t *testing.T) {
	logger := zap.NewNop()

	if _, err := NewEngine(nil, logger); err == nil {
		t.Fatal("expected error when config is nil")
	}

	tempDir := t.TempDir()
	if err := os.WriteFile(filepath.Join(tempDir, "invalid.yaml"), []byte("invalid:"), 0o600); err != nil {
		t.Fatalf("failed to write invalid rule file: %v", err)
	}

	cfg := &config.Config{
		Rules: config.RulesConfig{
			RulesDir: tempDir,
		},
	}

	if _, err := NewEngine(cfg, logger); err == nil {
		t.Fatal("expected error when rule loading fails")
	}
}

func TestEngine_LoadRules(t *testing.T) {
	cfg := &config.Config{
		Rules: config.RulesConfig{
			RulesDir:   "/tmp/rules",
			BufferSize: 1000,
		},
	}
	logger := zap.NewNop()

	engine, err := NewEngine(cfg, logger)
	assertNoError(t, err)

	// Test creating a rule set manually (since directory doesn't exist)
	ruleSet := &RuleSet{
		Name:    "test-ruleset",
		Enabled: true,
		Rules: []Rule{
			{
				Name:      "Test Rule 1",
				Enabled:   true,
				Condition: "level == 'error'",
				Actions: []RuleAction{
					{
						Type:    "alert",
						Enabled: true,
						Parameters: map[string]interface{}{
							"severity": "high",
							"message":  "Error detected",
							"tags":     []string{"error", "test"},
						},
					},
				},
			},
		},
	}

	engine.ruleSets["test-ruleset"] = ruleSet
	if len(engine.ruleSets) != 1 {
		t.Fatalf("expected 1 rule set, got %d", len(engine.ruleSets))
	}
	assertEqual(t, "test-ruleset", engine.ruleSets["test-ruleset"].Name)
}

func TestEngine_LoadRuleSetsFromFiles(t *testing.T) {
	tempDir := t.TempDir()

	writeFile := func(name, content string) {
		if err := os.WriteFile(filepath.Join(tempDir, name), []byte(content), 0o600); err != nil {
			t.Fatalf("failed to write file %s: %v", name, err)
		}
	}

	writeFile("security.yaml", `
rule_sets:
  - name: "security"
    enabled: false
    rules:
      - name: "failed_login"
        enabled: true
        condition: "level == 'error'"
        actions:
          - type: "alert"
            severity: "medium"
`)

	writeFile("security-extra.yml", `
- name: "security"
  enabled: true
  rules:
    - name: "suspicious_activity"
      enabled: true
      condition: "message contains 'suspicious'"
      actions:
        - type: "alert"
          severity: "high"
`)

	writeFile("compliance.json", `
{
  "name": "compliance",
  "enabled": true,
  "rules": [
    {
      "name": "data_access",
      "enabled": true,
      "condition": "message contains 'access'",
      "actions": [
        {
          "type": "alert",
          "severity": "low"
        }
      ]
    }
  ]
}
`)

	// File with unsupported extension should be ignored
	writeFile("ignored.txt", `this should not be loaded`)

	engine := &Engine{
		config: &config.Config{
			Rules: config.RulesConfig{
				RulesDir: tempDir,
			},
		},
		logger: zap.NewNop(),
	}

	if err := engine.loadRuleSets(); err != nil {
		t.Fatalf("expected no error loading rule sets, got %v", err)
	}

	if len(engine.ruleSets) != 2 {
		t.Fatalf("expected 2 rule sets, got %d", len(engine.ruleSets))
	}

	security := engine.ruleSets["security"]
	if security == nil {
		t.Fatalf("security rule set not loaded")
	}
	if !security.Enabled {
		t.Fatalf("expected security rule set to be enabled after merge")
	}
	if len(security.Rules) != 2 {
		t.Fatalf("expected merged security rule set to have 2 rules, got %d", len(security.Rules))
	}

	compliance := engine.ruleSets["compliance"]
	if compliance == nil {
		t.Fatalf("compliance rule set not loaded")
	}
	if len(compliance.Rules) != 1 || compliance.Rules[0].Name != "data_access" {
		t.Fatalf("compliance rule set not parsed correctly: %+v", compliance.Rules)
	}
}

func TestEngine_LoadRuleSetsDecodeError(t *testing.T) {
	tempDir := t.TempDir()
	if err := os.WriteFile(filepath.Join(tempDir, "invalid.yaml"), []byte("::::"), 0o600); err != nil {
		t.Fatalf("failed to write invalid file: %v", err)
	}

	engine := &Engine{
		config: &config.Config{
			Rules: config.RulesConfig{
				RulesDir: tempDir,
			},
		},
		logger: zap.NewNop(),
	}

	if err := engine.loadRuleSets(); err == nil {
		t.Fatal("expected error for invalid rule definition, got nil")
	}
}

func TestEngine_LoadRuleSetsNilConfig(t *testing.T) {
	engine := &Engine{
		logger: zap.NewNop(),
	}

	if err := engine.loadRuleSets(); err == nil {
		t.Fatal("expected error when config is nil")
	}
}

func TestEngine_LoadRuleSetsMissingDirectory(t *testing.T) {
	engine := &Engine{
		config: &config.Config{
			Rules: config.RulesConfig{
				RulesDir: filepath.Join(t.TempDir(), "nonexistent"),
			},
		},
		logger: zap.NewNop(),
	}

	if err := engine.loadRuleSets(); err != nil {
		t.Fatalf("expected no error when directory missing, got %v", err)
	}
	if len(engine.ruleSets) != 0 {
		t.Fatalf("expected no rule sets to be loaded, got %d", len(engine.ruleSets))
	}
}

func TestEngine_RuleSetManagementAndStats(t *testing.T) {
	tempDir := t.TempDir()

	cfg := &config.Config{
		Rules: config.RulesConfig{
			RulesDir: tempDir,
		},
	}

	engine, err := NewEngine(cfg, zap.NewNop())
	assertNoError(t, err)

	ruleSet := &RuleSet{
		Name:    "managed",
		Enabled: true,
		Rules: []Rule{
			{
				Name:      "rule-enabled",
				Enabled:   true,
				Condition: "level == 'info'",
				Actions: []RuleAction{
					{
						Type:    "alert",
						Enabled: true,
						Parameters: map[string]interface{}{
							"severity": "low",
						},
					},
				},
			},
			{
				Name:      "rule-disabled",
				Enabled:   false,
				Condition: "level == 'error'",
				Actions: []RuleAction{
					{
						Type:    "alert",
						Enabled: true,
						Parameters: map[string]interface{}{
							"severity": "high",
						},
					},
				},
			},
		},
	}

	engine.AddRuleSet(ruleSet)
	if engine.GetRuleSet("managed") == nil {
		t.Fatal("expected rule set to be retrievable")
	}

	listed := engine.ListRuleSets()
	if len(listed) != 1 {
		t.Fatalf("expected 1 rule set, got %d", len(listed))
	}

	stats := engine.GetStats()
	// Basic stats should be initialized
	if stats.EventsProcessed < 0 {
		t.Fatalf("expected non-negative events processed, got %d", stats.EventsProcessed)
	}
	if stats.RuleStats == nil {
		t.Fatal("expected rule stats to be initialized")
	}

	engine.RemoveRuleSet("managed")
	if engine.GetRuleSet("managed") != nil {
		t.Fatal("expected rule set to be removed")
	}
	if len(engine.ListRuleSets()) != 0 {
		t.Fatal("expected no rule sets after removal")
	}
}

func TestDecodeRuleSetsWrapped(t *testing.T) {
	data := []byte(`
rule_sets:
  - name: "security"
    enabled: true
    rules:
      - name: "failed_login"
        enabled: true
`)

	ruleSets, err := decodeRuleSets(data)
	assertNoError(t, err)
	assertLen(t, ruleSets, 1)
	assertEqual(t, "security", ruleSets[0].Name)
	if !ruleSets[0].Enabled {
		t.Fatal("expected ruleset to be enabled")
	}
}

func TestDecodeRuleSetsList(t *testing.T) {
	data := []byte(`
- name: "compliance"
  enabled: true
  rules:
    - name: "data_access"
      enabled: false
`)

	ruleSets, err := decodeRuleSets(data)
	assertNoError(t, err)
	assertLen(t, ruleSets, 1)
	assertEqual(t, "compliance", ruleSets[0].Name)
	if ruleSets[0].Rules[0].Enabled {
		t.Fatal("expected rule to be disabled")
	}
}

func TestDecodeRuleSetsSingle(t *testing.T) {
	data := []byte(`
name: "operations"
enabled: true
rules:
  - name: "latency"
    enabled: true
`)

	ruleSets, err := decodeRuleSets(data)
	assertNoError(t, err)
	assertLen(t, ruleSets, 1)
	assertEqual(t, "operations", ruleSets[0].Name)
}

func TestDecodeRuleSetsInvalid(t *testing.T) {
	_, err := decodeRuleSets([]byte(`foo: bar`))
	if err == nil {
		t.Fatal("expected error for invalid rule definition")
	}

	_, err = decodeRuleSets([]byte{})
	if err == nil {
		t.Fatal("expected error for empty data")
	}
}

func TestEngine_Evaluate(t *testing.T) {
	cfg := &config.Config{
		Rules: config.RulesConfig{
			Enabled:    true,
			RulesDir:   "/tmp/rules",
			BufferSize: 1000,
		},
	}
	logger := zap.NewNop()

	engine, err := NewEngine(cfg, logger)
	assertNoError(t, err)

	engine.ruleSets["default"] = &RuleSet{
		Name:    "default",
		Enabled: true,
		Rules: []Rule{
			{
				ID:          "error-rule",
				Name:        "Error Detection",
				Description: "Detect error level logs",
				Condition:   "level == 'error'",
				Alert: AlertConfig{
					Severity: "high",
					Message:  "Error log detected",
					Tags:     []string{"error"},
				},
				Actions: []RuleAction{
					{
						Type:    "alert",
						Enabled: true,
						Parameters: map[string]interface{}{
							"severity": "high",
						},
					},
				},
				Enabled: true,
			},
			{
				ID:          "disabled-rule",
				Name:        "Disabled Rule",
				Description: "This rule is disabled",
				Condition:   "level == 'debug'",
				Alert: AlertConfig{
					Severity: "low",
					Message:  "Debug log",
					Tags:     []string{"debug"},
				},
				Actions: []RuleAction{
					{
						Type:    "alert",
						Enabled: true,
						Parameters: map[string]interface{}{
							"severity": "low",
						},
					},
				},
				Enabled: false,
			},
		},
	}

	// Test with matching entry
	entry := &models.ProcessedLogEntry{
		ID:      "test-123",
		Level:   "error",
		Message: "Test error message",
		Fields:  make(map[string]interface{}),
	}

	ctx := context.Background()
	alerts, err := engine.Evaluate(ctx, entry)

	assertNoError(t, err)
	assertLen(t, alerts, 1)
	assertEqual(t, "Rule 'Error Detection' triggered", alerts[0].Message)
	assertEqual(t, "high", alerts[0].Severity)

	// Test with non-matching entry
	entry.Level = "info"
	alerts, err = engine.Evaluate(ctx, entry)

	assertNoError(t, err)
	assertLen(t, alerts, 0)
}

func TestEngine_EvaluateCondition(t *testing.T) {
	cfg := &config.Config{
		Rules: config.RulesConfig{
			Enabled:    true,
			RulesDir:   "/tmp/rules",
			BufferSize: 1000,
		},
	}
	logger := zap.NewNop()

	engine, err := NewEngine(cfg, logger)
	assertNoError(t, err)

	entry := &models.ProcessedLogEntry{
		ID:      "test-123",
		Level:   "error",
		Message: "Test error message",
		Source:  "test-app",
		Fields: map[string]interface{}{
			"user":   "testuser",
			"action": "login",
			"count":  5,
		},
	}

	tests := []struct {
		name      string
		condition string
		expected  bool
	}{
		{"simple equality", "level == 'error'", true},
		{"simple inequality", "level == 'info'", false},
		{"field equality", "user == 'testuser'", true},
		{"field inequality", "user == 'otheruser'", false},
		{"numeric comparison", "count > 3", true},
		{"numeric comparison false", "count > 10", false},
		{"contains check", "message contains 'error'", true},
		{"contains check false", "message contains 'success'", false},
		{"regex match", "source matches '^test-.*'", true},
		{"regex no match", "source matches '^prod-.*'", false},
		{"AND condition", "level == 'error' && user == 'testuser'", true},
		{"AND condition false", "level == 'error' && user == 'otheruser'", false},
		{"OR condition", "level == 'error' || level == 'warn'", true},
		{"OR condition true", "level == 'info' || user == 'testuser'", true},
		{"OR condition false", "level == 'info' || user == 'otheruser'", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := engine.evaluateCondition(entry, tt.condition)
			assertEqual(t, tt.expected, result)
		})
	}
}

func TestEngine_EvaluateSimpleConditionOperators(t *testing.T) {
	cfg := &config.Config{
		Rules: config.RulesConfig{
			Enabled:    true,
			RulesDir:   "/tmp/rules",
			BufferSize: 1000,
		},
	}
	engine, err := NewEngine(cfg, zap.NewNop())
	assertNoError(t, err)

	entry := &models.ProcessedLogEntry{
		ID:      "test-operators",
		Level:   "warn",
		Message: "Login failed from 10.0.0.1",
		Source:  "auth-service",
		Fields: map[string]interface{}{
			"status":     "active",
			"count":      10,
			"count_str":  "12",
			"invalidNum": "abc",
		},
	}

	tests := []struct {
		name      string
		condition string
		expected  bool
	}{
		{"not equals", "status != 'inactive'", true},
		{"not contains", "message not_contains 'success'", true},
		{"contains true", "message contains 'Login'", true},
		{"contains false", "message contains 'Success'", false},
		{"regex invalid", "message matches '('", false},
		{"less than true", "count < 20", true},
		{"less than false", "count < 5", false},
		{"less equal true", "count <= 10", true},
		{"less equal false", "count <= 9", false},
		{"greater true", "count > 5", true},
		{"greater false", "count > 20", false},
		{"greater equal true", "count >= 10", true},
		{"greater equal false", "count >= 11", false},
		{"string numeric greater", "count_str > 5", true},
		{"string numeric less equal false", "count_str <= 10", false},
		{"invalid numeric comparison", "invalidNum > 2", false},
		{"unknown operator", "status ?? 'active'", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := engine.evaluateSimpleCondition(entry, tt.condition)
			if got != tt.expected {
				t.Fatalf("expected %v for condition %q, got %v", tt.expected, tt.condition, got)
			}
		})
	}
}

func TestEngine_CompareValuesExtended(t *testing.T) {
	engine := &Engine{logger: zap.NewNop()}

	if !engine.compareValues("alpha", "==", "alpha") {
		t.Fatal("expected equality comparison to succeed")
	}
	if !engine.compareValues("alpha", "!=", "beta") {
		t.Fatal("expected inequality comparison to succeed")
	}
	if !engine.compareValues(5, ">", 3) {
		t.Fatal("expected numeric greater comparison to succeed")
	}
	if engine.compareValues(3, ">=", 5) {
		t.Fatal("expected numeric greater equal comparison to fail")
	}
	if !engine.compareValues(3, "<", 5) {
		t.Fatal("expected numeric less comparison to succeed")
	}
	if engine.compareValues(7, "<=", 2) {
		t.Fatal("expected numeric less equal comparison to fail")
	}
	if !engine.compareValues("hello world", "contains", "world") {
		t.Fatal("expected contains comparison to succeed")
	}
	if !engine.compareValues("abc123", "matches", "^abc\\d+") {
		t.Fatal("expected regex match to succeed")
	}
	if engine.compareValues("abc123", "matches", "[") {
		t.Fatal("expected invalid regex to return false")
	}
	if engine.compareValues("value", "unknown", "value") {
		t.Fatal("expected unknown operator to return false")
	}
	if engine.compareValues("value", ">", "not-a-number") {
		t.Fatal("expected invalid numeric comparison to return false")
	}
}

func TestParseFloat(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected float64
		wantErr  bool
	}{
		{"float64", float64(1.5), 1.5, false},
		{"float32", float32(2.5), 2.5, false},
		{"int", 3, 3.0, false},
		{"int64", int64(4), 4.0, false},
		{"string", "5.5", 5.5, false},
		{"string invalid", "abc", 0, true},
		{"unsupported type", true, 0, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			value, err := parseFloat(tt.input)
			if tt.wantErr {
				if err == nil {
					t.Fatalf("expected error for input %v", tt.input)
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if value != tt.expected {
				t.Fatalf("expected %v, got %v", tt.expected, value)
			}
		})
	}
}

func TestEngine_CompareValues(t *testing.T) {
	cfg := &config.Config{
		Rules: config.RulesConfig{
			Enabled:    true,
			RulesDir:   "/tmp/rules",
			BufferSize: 1000,
		},
	}
	logger := zap.NewNop()

	engine, err := NewEngine(cfg, logger)
	assertNoError(t, err)

	tests := []struct {
		name     string
		field    interface{}
		operator string
		value    string
		expected bool
	}{
		{"string equals", "test", "==", "test", true},
		{"string not equals", "test", "==", "other", false},
		{"string contains", "test message", "contains", "test", true},
		{"string not contains", "test message", "contains", "other", false},
		{"int greater", 10, ">", "5", true},
		{"int not greater", 10, ">", "15", false},
		{"int less", 5, "<", "10", true},
		{"int not less", 15, "<", "10", false},
		{"float greater", 10.5, ">", "10.0", true},
		{"float not greater", 10.5, ">", "11.0", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := engine.compareValues(tt.field, tt.operator, tt.value)
			assertEqual(t, tt.expected, result)
		})
	}
}

func TestEngine_CreateAlert(t *testing.T) {
	cfg := &config.Config{
		Rules: config.RulesConfig{
			Enabled:    true,
			RulesDir:   "/tmp/rules",
			BufferSize: 1000,
		},
	}
	logger := zap.NewNop()

	engine, err := NewEngine(cfg, logger)
	assertNoError(t, err)

	// Note: createAlert method doesn't exist - this test would need to be redesigned
	// to test the actual engine API

	// Note: createAlert method doesn't exist - this test would need to be redesigned
	// to test the actual engine API

	// Test that engine was created successfully
	assertNotNil(t, engine)
}

func TestEngine_RateLimiting(t *testing.T) {
	cfg := &config.Config{
		Rules: config.RulesConfig{
			Enabled:    true,
			RulesDir:   "/tmp/rules",
			BufferSize: 1000,
		},
	}
	logger := zap.NewNop()

	advEngine, err := NewAdvancedRuleEngine(cfg, logger)
	assertNoError(t, err)

	advEngine.AddRuleSet(&RuleSet{
		Name:    "rate-limit-set",
		Enabled: true,
		Rules: []Rule{
			{
				ID:          "rate-limited-rule",
				Name:        "Rate Limited Rule",
				Description: "Rule with rate limiting",
				Condition:   "level == 'error'",
				Actions: []RuleAction{
					{
						Type:    "alert",
						Enabled: true,
						Parameters: map[string]interface{}{
							"severity": "medium",
						},
					},
				},
				Enabled: true,
			},
		},
	})

	advEngine.rateLimits["Rate Limited Rule"] = RateLimit{
		MaxOccurrences: 2,
		TimeWindow:     time.Minute,
	}

	entry := &models.ProcessedLogEntry{
		ID:      "test-123",
		Level:   "error",
		Message: "Test error message",
		Fields:  make(map[string]interface{}),
	}

	ctx := context.Background()

	// First two evaluations should generate alerts
	alerts1, err := advEngine.EvaluateWithRateLimit(ctx, entry)
	assertNoError(t, err)
	assertLen(t, alerts1, 1)

	alerts2, err := advEngine.EvaluateWithRateLimit(ctx, entry)
	assertNoError(t, err)
	assertLen(t, alerts2, 1)

	// Third evaluation should be rate limited
	alerts3, err := advEngine.EvaluateWithRateLimit(ctx, entry)
	assertNoError(t, err)
	assertLen(t, alerts3, 0)
}

func BenchmarkEngine_Evaluate(b *testing.B) {
	cfg := &config.Config{
		Rules: config.RulesConfig{
			Enabled:    true,
			RulesDir:   "/tmp/rules",
			BufferSize: 1000,
		},
	}
	logger := zap.NewNop()

	engine, err := NewEngine(cfg, logger)
	if err != nil {
		b.Fatal(err)
	}

	engine.ruleSets["benchmark"] = &RuleSet{
		Name:    "benchmark",
		Enabled: true,
		Rules: []Rule{
			{
				ID:        "bench-rule-1",
				Name:      "bench-rule-1",
				Condition: "level == 'error'",
				Actions: []RuleAction{
					{
						Type:    "alert",
						Enabled: true,
						Parameters: map[string]interface{}{
							"severity": "high",
						},
					},
				},
				Enabled: true,
			},
			{
				ID:        "bench-rule-2",
				Name:      "bench-rule-2",
				Condition: "user == 'testuser'",
				Actions: []RuleAction{
					{
						Type:    "alert",
						Enabled: true,
						Parameters: map[string]interface{}{
							"severity": "medium",
						},
					},
				},
				Enabled: true,
			},
		},
	}

	entry := &models.ProcessedLogEntry{
		ID:      "bench-test",
		Level:   "error",
		Message: "benchmark message",
		Fields:  map[string]interface{}{"user": "testuser"},
	}

	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, err := engine.Evaluate(ctx, entry)
			if err != nil {
				b.Error(err)
			}
		}
	})
}

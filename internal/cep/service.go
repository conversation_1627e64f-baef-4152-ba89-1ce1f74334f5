package cep

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// Service provides a high-level interface to the CEP engine
type Service struct {
	engine *Engine
	config config.CEPConfig
	logger *zap.Logger
	mutex  sync.RWMutex
}

// CEPServiceStats represents CEP service statistics
type CEPServiceStats struct {
	EngineStats       *CEPStats                      `json:"engine_stats"`
	CorrelationStats  map[string]interface{}         `json:"correlation_stats"`
	DetectionStats    map[string]interface{}         `json:"detection_stats"`
	ServiceUptime     time.Duration                  `json:"service_uptime"`
	LastProcessedTime time.Time                      `json:"last_processed_time"`
	ProcessingRate    float64                        `json:"processing_rate"`
}

// NewService creates a new CEP service
func NewService(cfg config.CEPConfig, logger *zap.Logger) (*Service, error) {
	engine, err := NewEngine(cfg, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create CEP engine: %w", err)
	}

	return &Service{
		engine: engine,
		config: cfg,
		logger: logger,
	}, nil
}

// Start starts the CEP service
func (s *Service) Start(ctx context.Context) error {
	s.logger.Info("Starting CEP service")

	if err := s.engine.Start(ctx); err != nil {
		return fmt.Errorf("failed to start CEP engine: %w", err)
	}

	s.logger.Info("CEP service started successfully")
	return nil
}

// Stop stops the CEP service
func (s *Service) Stop() {
	s.logger.Info("Stopping CEP service")
	s.engine.Stop()
	s.logger.Info("CEP service stopped")
}

// ProcessEvent processes a single event through the CEP pipeline
func (s *Service) ProcessEvent(ctx context.Context, eventData map[string]interface{}) (*ProcessingResult, error) {
	if !s.config.Enabled {
		return &ProcessingResult{
			Processed: false,
			Reason:    "CEP service is disabled",
		}, nil
	}

	start := time.Now()

	matches, err := s.engine.ProcessEvent(ctx, eventData)
	if err != nil {
		return nil, fmt.Errorf("failed to process event: %w", err)
	}

	result := &ProcessingResult{
		Processed:       true,
		ProcessingTime:  time.Since(start),
		PatternMatches:  matches,
		MatchCount:      len(matches),
		Timestamp:       time.Now(),
	}

	s.logger.Debug("Event processed",
		zap.Duration("processing_time", result.ProcessingTime),
		zap.Int("pattern_matches", result.MatchCount))

	return result, nil
}

// ProcessBatch processes a batch of events
func (s *Service) ProcessBatch(ctx context.Context, events []map[string]interface{}) (*BatchProcessingResult, error) {
	if !s.config.Enabled {
		return &BatchProcessingResult{
			Processed: false,
			Reason:    "CEP service is disabled",
		}, nil
	}

	start := time.Now()
	var allMatches []*PatternMatch
	var errors []error

	for i, eventData := range events {
		matches, err := s.engine.ProcessEvent(ctx, eventData)
		if err != nil {
			errors = append(errors, fmt.Errorf("event %d: %w", i, err))
			continue
		}
		allMatches = append(allMatches, matches...)
	}

	result := &BatchProcessingResult{
		Processed:       true,
		ProcessingTime:  time.Since(start),
		EventCount:      len(events),
		TotalMatches:    len(allMatches),
		PatternMatches:  allMatches,
		Errors:          errors,
		Timestamp:       time.Now(),
	}

	s.logger.Info("Batch processed",
		zap.Int("event_count", result.EventCount),
		zap.Int("total_matches", result.TotalMatches),
		zap.Int("errors", len(errors)),
		zap.Duration("processing_time", result.ProcessingTime))

	return result, nil
}

// GetStats returns comprehensive CEP service statistics
func (s *Service) GetStats() *CEPServiceStats {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	stats := &CEPServiceStats{
		EngineStats:       s.engine.GetStats(),
		LastProcessedTime: time.Now(),
	}

	// Get correlation stats if correlator is available
	if s.engine.correlator != nil {
		stats.CorrelationStats = s.engine.correlator.GetCorrelationStats()
	}

	// Get detection stats if detector is available
	if s.engine.detector != nil {
		stats.DetectionStats = s.engine.detector.GetDetectionStats()
	}

	// Calculate processing rate (events per second)
	if stats.EngineStats != nil && stats.EngineStats.EventsProcessed > 0 {
		uptime := time.Since(stats.EngineStats.LastReset)
		if uptime.Seconds() > 0 {
			stats.ProcessingRate = float64(stats.EngineStats.EventsProcessed) / uptime.Seconds()
			stats.ServiceUptime = uptime
		}
	}

	return stats
}

// ResetStats resets CEP service statistics
func (s *Service) ResetStats() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.engine.ResetStats()
	s.logger.Info("CEP service statistics reset")
}

// GetPatterns returns all loaded patterns
func (s *Service) GetPatterns() map[string]*Pattern {
	s.engine.mutex.RLock()
	defer s.engine.mutex.RUnlock()

	// Create a copy to avoid race conditions
	patterns := make(map[string]*Pattern)
	for id, pattern := range s.engine.patterns {
		patterns[id] = &Pattern{
			ID:            pattern.ID,
			Name:          pattern.Name,
			Description:   pattern.Description,
			Enabled:       pattern.Enabled,
			Priority:      pattern.Priority,
			Conditions:    append([]PatternCondition(nil), pattern.Conditions...),
			Window:        pattern.Window,
			Actions:       append([]PatternAction(nil), pattern.Actions...),
			Metadata:      make(map[string]interface{}),
			LastTriggered: pattern.LastTriggered,
			TriggerCount:  pattern.TriggerCount,
		}

		// Copy metadata
		for k, v := range pattern.Metadata {
			patterns[id].Metadata[k] = v
		}
	}

	return patterns
}

// GetCorrelations returns current correlation groups
func (s *Service) GetCorrelations() map[string]*CorrelationGroup {
	if s.engine.correlator == nil {
		return make(map[string]*CorrelationGroup)
	}
	return s.engine.correlator.GetCorrelations()
}

// GetThreatDetectors returns current threat detection patterns
func (s *Service) GetThreatDetectors() map[string]*ThreatPattern {
	if s.engine.detector == nil {
		return make(map[string]*ThreatPattern)
	}
	return s.engine.detector.GetDetectors()
}

// ValidateConfiguration validates the CEP service configuration
func (s *Service) ValidateConfiguration() error {
	if s.config.BufferSize <= 0 {
		return fmt.Errorf("buffer_size must be greater than 0")
	}

	if s.config.CorrelationEnabled {
		if s.config.CorrelationWindow <= 0 {
			return fmt.Errorf("correlation_window must be greater than 0 when correlation is enabled")
		}
		if s.config.MaxCorrelationGroups <= 0 {
			return fmt.Errorf("max_correlation_groups must be greater than 0 when correlation is enabled")
		}
		if s.config.CorrelationThreshold < 0 || s.config.CorrelationThreshold > 1 {
			return fmt.Errorf("correlation_threshold must be between 0 and 1")
		}
	}

	if s.config.ThreatDetectionEnabled && s.config.ThreatUpdateInterval <= 0 {
		return fmt.Errorf("threat_update_interval must be greater than 0 when threat detection is enabled")
	}

	return nil
}

// ProcessingResult represents the result of processing a single event
type ProcessingResult struct {
	Processed       bool             `json:"processed"`
	ProcessingTime  time.Duration    `json:"processing_time"`
	PatternMatches  []*PatternMatch  `json:"pattern_matches"`
	MatchCount      int              `json:"match_count"`
	Timestamp       time.Time        `json:"timestamp"`
	Reason          string           `json:"reason,omitempty"`
}

// BatchProcessingResult represents the result of processing a batch of events
type BatchProcessingResult struct {
	Processed       bool             `json:"processed"`
	ProcessingTime  time.Duration    `json:"processing_time"`
	EventCount      int              `json:"event_count"`
	TotalMatches    int              `json:"total_matches"`
	PatternMatches  []*PatternMatch  `json:"pattern_matches"`
	Errors          []error          `json:"errors,omitempty"`
	Timestamp       time.Time        `json:"timestamp"`
	Reason          string           `json:"reason,omitempty"`
}

// HealthCheck performs a health check on the CEP service
func (s *Service) HealthCheck() map[string]interface{} {
	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now(),
		"enabled":   s.config.Enabled,
	}

	if !s.config.Enabled {
		health["status"] = "disabled"
		return health
	}

	// Check engine health
	stats := s.engine.GetStats()
	health["events_processed"] = stats.EventsProcessed
	health["patterns_matched"] = stats.PatternsMatched
	health["windows_active"] = stats.WindowsActive

	// Check correlator health
	if s.engine.correlator != nil && s.config.CorrelationEnabled {
		correlations := s.engine.correlator.GetCorrelations()
		health["correlation_groups"] = len(correlations)
	}

	// Check detector health
	if s.engine.detector != nil && s.config.ThreatDetectionEnabled {
		detectors := s.engine.detector.GetDetectors()
		enabledDetectors := 0
		for _, detector := range detectors {
			if detector.Enabled {
				enabledDetectors++
			}
		}
		health["threat_detectors"] = enabledDetectors
	}

	return health
}

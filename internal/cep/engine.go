package cep

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// Engine provides Complex Event Processing capabilities
type Engine struct {
	config      config.CEPConfig
	logger      *zap.Logger
	patterns    map[string]*Pattern
	correlator  *EventCorrelator
	detector    *ThreatDetector
	windows     map[string]*EventWindow
	stats       *CEPStats
	eventBuffer chan *Event
	stopCh      chan struct{}
	mutex       sync.RWMutex
}

// Event represents a complex event for processing
type Event struct {
	ID            string                 `json:"id"`
	Type          string                 `json:"type"`
	Source        string                 `json:"source"`
	Timestamp     time.Time              `json:"timestamp"`
	Data          map[string]interface{} `json:"data"`
	Metadata      map[string]interface{} `json:"metadata"`
	Attributes    map[string]interface{} `json:"attributes"`
	CorrelationID string                 `json:"correlation_id,omitempty"`
}

// Pattern represents a complex event pattern
type Pattern struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Enabled     bool                   `json:"enabled"`
	Priority    int                    `json:"priority"`
	Conditions  []PatternCondition     `json:"conditions"`
	Window      WindowConfig           `json:"window"`
	Actions     []PatternAction        `json:"actions"`
	Metadata    map[string]interface{} `json:"metadata"`

	// Runtime state
	LastTriggered time.Time `json:"last_triggered"`
	TriggerCount  int64     `json:"trigger_count"`
	mutex         sync.RWMutex
}

// PatternCondition represents a condition in a pattern
type PatternCondition struct {
	EventType  string                 `json:"event_type"`
	Filters    []ConditionFilter      `json:"filters"`
	Sequence   int                    `json:"sequence"`
	Optional   bool                   `json:"optional"`
	Quantifier string                 `json:"quantifier"` // one, one_or_more, zero_or_more, exactly_n
	Count      int                    `json:"count,omitempty"`
	Within     time.Duration          `json:"within,omitempty"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// ConditionFilter represents a filter within a condition
type ConditionFilter struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
	Negate   bool        `json:"negate,omitempty"`
}

// WindowConfig configures event windows
type WindowConfig struct {
	Type    string        `json:"type"` // time, count, session
	Size    time.Duration `json:"size,omitempty"`
	Count   int           `json:"count,omitempty"`
	Slide   time.Duration `json:"slide,omitempty"`
	Timeout time.Duration `json:"timeout,omitempty"`
}

// PatternAction represents an action to take when a pattern matches
type PatternAction struct {
	Type       string                 `json:"type"`
	Parameters map[string]interface{} `json:"parameters"`
	Enabled    bool                   `json:"enabled"`
}

// EventWindow manages a window of events
type EventWindow struct {
	ID        string        `json:"id"`
	Type      string        `json:"type"`
	Size      time.Duration `json:"size"`
	Events    []*Event      `json:"events"`
	StartTime time.Time     `json:"start_time"`
	EndTime   time.Time     `json:"end_time"`
	mutex     sync.RWMutex
}

// PatternMatch represents a matched pattern
type PatternMatch struct {
	PatternID   string                 `json:"pattern_id"`
	PatternName string                 `json:"pattern_name"`
	Events      []*Event               `json:"events"`
	Timestamp   time.Time              `json:"timestamp"`
	Confidence  float64                `json:"confidence"`
	Metadata    map[string]interface{} `json:"metadata"`
	Actions     []ActionResult         `json:"actions"`
}

// ActionResult represents the result of an action execution
type ActionResult struct {
	Type     string                 `json:"type"`
	Success  bool                   `json:"success"`
	Error    error                  `json:"error,omitempty"`
	Output   map[string]interface{} `json:"output,omitempty"`
	Duration time.Duration          `json:"duration"`
}

// CEPStats tracks CEP engine statistics
type CEPStats struct {
	EventsProcessed   int64                    `json:"events_processed"`
	PatternsMatched   int64                    `json:"patterns_matched"`
	CorrelationsFound int64                    `json:"correlations_found"`
	ThreatsDetected   int64                    `json:"threats_detected"`
	WindowsActive     int64                    `json:"windows_active"`
	PatternStats      map[string]*PatternStats `json:"pattern_stats"`
	LastReset         time.Time                `json:"last_reset"`
	mutex             sync.RWMutex
}

// PatternStats tracks statistics for individual patterns
type PatternStats struct {
	Evaluations   int64         `json:"evaluations"`
	Matches       int64         `json:"matches"`
	LastTriggered time.Time     `json:"last_triggered"`
	AvgLatency    time.Duration `json:"avg_latency"`
}

// EventCorrelator handles event correlation
type EventCorrelator struct {
	correlations map[string]*CorrelationGroup
	config       CorrelationConfig
	logger       *zap.Logger
	mutex        sync.RWMutex
}

// CorrelationGroup represents a group of correlated events
type CorrelationGroup struct {
	ID        string    `json:"id"`
	Events    []*Event  `json:"events"`
	StartTime time.Time `json:"start_time"`
	LastSeen  time.Time `json:"last_seen"`
	Score     float64   `json:"score"`
}

// CorrelationConfig configures event correlation
type CorrelationConfig struct {
	Enabled         bool          `json:"enabled"`
	Window          time.Duration `json:"window"`
	MaxGroups       int           `json:"max_groups"`
	ScoreThreshold  float64       `json:"score_threshold"`
	CorrelationKeys []string      `json:"correlation_keys"`
}

// ThreatDetector handles threat detection
type ThreatDetector struct {
	detectors map[string]*ThreatPattern
	config    ThreatDetectionConfig
	logger    *zap.Logger
	mutex     sync.RWMutex
}

// ThreatPattern represents a threat detection pattern
type ThreatPattern struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Type       string                 `json:"type"` // ddos, brute_force, anomaly, etc.
	Enabled    bool                   `json:"enabled"`
	Thresholds map[string]float64     `json:"thresholds"`
	Window     time.Duration          `json:"window"`
	Actions    []string               `json:"actions"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// ThreatDetectionConfig configures threat detection
type ThreatDetectionConfig struct {
	Enabled             bool          `json:"enabled"`
	DDoSDetection       bool          `json:"ddos_detection"`
	BruteForceDetection bool          `json:"brute_force_detection"`
	AnomalyDetection    bool          `json:"anomaly_detection"`
	UpdateInterval      time.Duration `json:"update_interval"`
}

// NewEngine creates a new CEP engine
func NewEngine(cfg config.CEPConfig, logger *zap.Logger) (*Engine, error) {
	engine := &Engine{
		config:   cfg,
		logger:   logger,
		patterns: make(map[string]*Pattern),
		windows:  make(map[string]*EventWindow),
		stats: &CEPStats{
			PatternStats: make(map[string]*PatternStats),
			LastReset:    time.Now(),
		},
		eventBuffer: make(chan *Event, cfg.BufferSize),
		stopCh:      make(chan struct{}),
	}

	// Initialize correlator
	correlatorConfig := CorrelationConfig{
		Enabled:         cfg.CorrelationEnabled,
		Window:          cfg.CorrelationWindow,
		MaxGroups:       cfg.MaxCorrelationGroups,
		ScoreThreshold:  cfg.CorrelationThreshold,
		CorrelationKeys: cfg.CorrelationKeys,
	}
	engine.correlator = NewEventCorrelator(correlatorConfig, logger)

	// Initialize threat detector
	detectorConfig := ThreatDetectionConfig{
		Enabled:             cfg.ThreatDetectionEnabled,
		DDoSDetection:       cfg.DDoSDetectionEnabled,
		BruteForceDetection: cfg.BruteForceDetectionEnabled,
		AnomalyDetection:    cfg.AnomalyDetectionEnabled,
		UpdateInterval:      cfg.ThreatUpdateInterval,
	}
	engine.detector = NewThreatDetector(detectorConfig, logger)

	// Load patterns if enabled
	if cfg.Enabled && cfg.PatternsDir != "" {
		if err := engine.LoadPatterns(); err != nil {
			return nil, fmt.Errorf("failed to load patterns: %w", err)
		}
	}

	return engine, nil
}

// Start starts the CEP engine
func (e *Engine) Start(ctx context.Context) error {
	if !e.config.Enabled {
		e.logger.Info("CEP engine is disabled")
		return nil
	}

	e.logger.Info("Starting CEP engine",
		zap.Int("buffer_size", e.config.BufferSize),
		zap.Int("patterns", len(e.patterns)))

	// Start event processing goroutine
	go e.processEvents(ctx)

	// Start correlator
	if e.correlator != nil {
		go e.correlator.Start(ctx)
	}

	// Start threat detector
	if e.detector != nil {
		go e.detector.Start(ctx)
	}

	// Start window cleanup goroutine
	go e.cleanupWindows(ctx)

	return nil
}

// Stop stops the CEP engine
func (e *Engine) Stop() {
	e.logger.Info("Stopping CEP engine")
	close(e.stopCh)

	if e.correlator != nil {
		e.correlator.Stop()
	}

	if e.detector != nil {
		e.detector.Stop()
	}
}

// ProcessEvent processes a single event through the CEP engine
func (e *Engine) ProcessEvent(ctx context.Context, eventData map[string]interface{}) ([]*PatternMatch, error) {
	if !e.config.Enabled {
		return nil, nil
	}

	event := &Event{
		ID:         generateEventID(),
		Type:       getEventType(eventData),
		Source:     getEventSource(eventData),
		Timestamp:  time.Now(),
		Data:       eventData,
		Metadata:   make(map[string]interface{}),
		Attributes: make(map[string]interface{}),
	}

	// Extract correlation ID if present
	if corrID, ok := eventData["correlation_id"].(string); ok {
		event.CorrelationID = corrID
	}

	select {
	case e.eventBuffer <- event:
		// Event queued successfully
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
		// Buffer full, process synchronously
		return e.processEventSync(ctx, event)
	}

	return nil, nil
}

// processEvents processes events from the buffer
func (e *Engine) processEvents(ctx context.Context) {
	for {
		select {
		case event := <-e.eventBuffer:
			matches, err := e.processEventSync(ctx, event)
			if err != nil {
				e.logger.Error("Failed to process event", zap.Error(err))
			}

			// Handle matches
			for _, match := range matches {
				e.handlePatternMatch(ctx, match)
			}

		case <-e.stopCh:
			return
		case <-ctx.Done():
			return
		}
	}
}

// processEventSync processes an event synchronously
func (e *Engine) processEventSync(ctx context.Context, event *Event) ([]*PatternMatch, error) {
	e.updateStats(1, 0, 0, 0)

	var matches []*PatternMatch

	// Add event to appropriate windows
	e.addEventToWindows(event)

	// Process through correlator
	if e.correlator != nil {
		e.correlator.ProcessEvent(event)
	}

	// Process through threat detector
	if e.detector != nil {
		threats := e.detector.ProcessEvent(event)
		e.updateStats(0, 0, 0, int64(len(threats)))
	}

	// Evaluate patterns
	e.mutex.RLock()
	patterns := make([]*Pattern, 0, len(e.patterns))
	for _, pattern := range e.patterns {
		if pattern.Enabled {
			patterns = append(patterns, pattern)
		}
	}
	e.mutex.RUnlock()

	for _, pattern := range patterns {
		match := e.evaluatePattern(ctx, pattern, event)
		if match != nil {
			matches = append(matches, match)
		}
	}

	e.updateStats(0, int64(len(matches)), 0, 0)
	return matches, nil
}

// evaluatePattern evaluates a pattern against an event
func (e *Engine) evaluatePattern(ctx context.Context, pattern *Pattern, event *Event) *PatternMatch {
	start := time.Now()

	// Get relevant window for this pattern
	windowID := fmt.Sprintf("%s_%s", pattern.ID, pattern.Window.Type)
	window := e.getOrCreateWindow(windowID, pattern.Window)

	// Check if pattern conditions are met
	if e.matchesPattern(pattern, window.Events, event) {
		match := &PatternMatch{
			PatternID:   pattern.ID,
			PatternName: pattern.Name,
			Events:      append(window.Events, event),
			Timestamp:   time.Now(),
			Confidence:  e.calculateConfidence(pattern, window.Events, event),
			Metadata:    make(map[string]interface{}),
		}

		// Execute actions
		match.Actions = e.executePatternActions(ctx, pattern.Actions, match)

		// Update pattern state
		pattern.mutex.Lock()
		pattern.LastTriggered = time.Now()
		pattern.TriggerCount++
		pattern.mutex.Unlock()

		// Update statistics
		e.updatePatternStats(pattern.ID, true, time.Since(start))

		return match
	}

	e.updatePatternStats(pattern.ID, false, time.Since(start))
	return nil
}

// Helper functions

func generateEventID() string {
	return fmt.Sprintf("cep_evt_%d", time.Now().UnixNano())
}

func getEventType(data map[string]interface{}) string {
	if eventType, ok := data["type"].(string); ok {
		return eventType
	}
	if classUID, ok := data["class_uid"].(float64); ok {
		return fmt.Sprintf("class_%d", int(classUID))
	}
	return "unknown"
}

func getEventSource(data map[string]interface{}) string {
	if source, ok := data["source"].(string); ok {
		return source
	}
	if metadata, ok := data["metadata"].(map[string]interface{}); ok {
		if product, ok := metadata["product"].(map[string]interface{}); ok {
			if name, ok := product["name"].(string); ok {
				return name
			}
		}
	}
	return "unknown"
}

// LoadPatterns loads patterns from the configured directory
func (e *Engine) LoadPatterns() error {
	e.logger.Info("Loading CEP patterns", zap.String("patterns_dir", e.config.PatternsDir))

	// Create sample patterns for demonstration
	ddosPattern := &Pattern{
		ID:          "ddos_detection",
		Name:        "DDoS Attack Detection",
		Description: "Detects potential DDoS attacks based on traffic patterns",
		Enabled:     true,
		Priority:    1,
		Conditions: []PatternCondition{
			{
				EventType: "network_activity",
				Filters: []ConditionFilter{
					{
						Field:    "class_uid",
						Operator: "eq",
						Value:    4001,
					},
				},
				Sequence:   1,
				Quantifier: "one_or_more",
				Count:      100,
				Within:     5 * time.Minute,
			},
		},
		Window: WindowConfig{
			Type: "time",
			Size: 5 * time.Minute,
		},
		Actions: []PatternAction{
			{
				Type:    "alert",
				Enabled: true,
				Parameters: map[string]interface{}{
					"severity": "critical",
					"message":  "Potential DDoS attack detected",
				},
			},
		},
	}

	bruteForcePattern := &Pattern{
		ID:          "brute_force_detection",
		Name:        "Brute Force Attack Detection",
		Description: "Detects brute force authentication attempts",
		Enabled:     true,
		Priority:    2,
		Conditions: []PatternCondition{
			{
				EventType: "authentication",
				Filters: []ConditionFilter{
					{
						Field:    "activity_name",
						Operator: "eq",
						Value:    "Logon",
					},
					{
						Field:    "status_id",
						Operator: "eq",
						Value:    2, // Failed
					},
				},
				Sequence:   1,
				Quantifier: "exactly_n",
				Count:      5,
				Within:     2 * time.Minute,
			},
		},
		Window: WindowConfig{
			Type: "time",
			Size: 2 * time.Minute,
		},
		Actions: []PatternAction{
			{
				Type:    "alert",
				Enabled: true,
				Parameters: map[string]interface{}{
					"severity": "high",
					"message":  "Brute force attack detected",
				},
			},
			{
				Type:    "block",
				Enabled: false,
				Parameters: map[string]interface{}{
					"duration": "15m",
				},
			},
		},
	}

	e.mutex.Lock()
	e.patterns[ddosPattern.ID] = ddosPattern
	e.patterns[bruteForcePattern.ID] = bruteForcePattern
	e.stats.PatternStats[ddosPattern.ID] = &PatternStats{}
	e.stats.PatternStats[bruteForcePattern.ID] = &PatternStats{}
	e.mutex.Unlock()

	e.logger.Info("CEP patterns loaded", zap.Int("count", len(e.patterns)))
	return nil
}

// addEventToWindows adds an event to appropriate windows
func (e *Engine) addEventToWindows(event *Event) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	for _, pattern := range e.patterns {
		if !pattern.Enabled {
			continue
		}

		windowID := fmt.Sprintf("%s_%s", pattern.ID, pattern.Window.Type)
		window := e.getOrCreateWindow(windowID, pattern.Window)

		window.mutex.Lock()
		window.Events = append(window.Events, event)

		// Trim window based on configuration
		switch pattern.Window.Type {
		case "time":
			cutoff := time.Now().Add(-pattern.Window.Size)
			var validEvents []*Event
			for _, evt := range window.Events {
				if evt.Timestamp.After(cutoff) {
					validEvents = append(validEvents, evt)
				}
			}
			window.Events = validEvents
		case "count":
			if len(window.Events) > pattern.Window.Count {
				window.Events = window.Events[len(window.Events)-pattern.Window.Count:]
			}
		}
		window.mutex.Unlock()
	}
}

// getOrCreateWindow gets or creates a window
func (e *Engine) getOrCreateWindow(windowID string, config WindowConfig) *EventWindow {
	if window, exists := e.windows[windowID]; exists {
		return window
	}

	window := &EventWindow{
		ID:        windowID,
		Type:      config.Type,
		Size:      config.Size,
		Events:    make([]*Event, 0),
		StartTime: time.Now(),
	}

	e.windows[windowID] = window
	return window
}

// matchesPattern checks if events match a pattern
func (e *Engine) matchesPattern(pattern *Pattern, windowEvents []*Event, newEvent *Event) bool {
	allEvents := append(windowEvents, newEvent)

	for _, condition := range pattern.Conditions {
		if !e.matchesCondition(condition, allEvents) {
			return false
		}
	}

	return true
}

// matchesCondition checks if events match a condition
func (e *Engine) matchesCondition(condition PatternCondition, events []*Event) bool {
	matchingEvents := 0

	for _, event := range events {
		if event.Type == condition.EventType || condition.EventType == "*" {
			if e.matchesFilters(condition.Filters, event) {
				matchingEvents++
			}
		}
	}

	switch condition.Quantifier {
	case "one":
		return matchingEvents >= 1
	case "one_or_more":
		return matchingEvents >= 1
	case "zero_or_more":
		return true // Always matches
	case "exactly_n":
		return matchingEvents == condition.Count
	default:
		return matchingEvents >= 1
	}
}

// matchesFilters checks if an event matches all filters
func (e *Engine) matchesFilters(filters []ConditionFilter, event *Event) bool {
	for _, filter := range filters {
		if !e.matchesFilter(filter, event) {
			return false
		}
	}
	return true
}

// matchesFilter checks if an event matches a single filter
func (e *Engine) matchesFilter(filter ConditionFilter, event *Event) bool {
	value := getNestedValue(event.Data, filter.Field)
	result := compareValues(value, filter.Value, filter.Operator)

	if filter.Negate {
		result = !result
	}

	return result
}

// calculateConfidence calculates confidence score for a pattern match
func (e *Engine) calculateConfidence(pattern *Pattern, events []*Event, newEvent *Event) float64 {
	// Simple confidence calculation based on number of matching conditions
	totalConditions := len(pattern.Conditions)
	matchingConditions := 0

	allEvents := append(events, newEvent)
	for _, condition := range pattern.Conditions {
		if e.matchesCondition(condition, allEvents) {
			matchingConditions++
		}
	}

	if totalConditions == 0 {
		return 1.0
	}

	return float64(matchingConditions) / float64(totalConditions)
}

// executePatternActions executes actions for a pattern match
func (e *Engine) executePatternActions(ctx context.Context, actions []PatternAction, match *PatternMatch) []ActionResult {
	var results []ActionResult

	for _, action := range actions {
		if !action.Enabled {
			continue
		}

		start := time.Now()
		result := ActionResult{
			Type: action.Type,
		}

		switch action.Type {
		case "alert":
			result = e.executeAlertAction(action, match)
		case "log":
			result = e.executeLogAction(action, match)
		case "block":
			result = e.executeBlockAction(action, match)
		case "enrich":
			result = e.executeEnrichAction(action, match)
		default:
			result.Error = fmt.Errorf("unsupported action type: %s", action.Type)
		}

		result.Duration = time.Since(start)
		results = append(results, result)
	}

	return results
}

// Action execution methods
func (e *Engine) executeAlertAction(action PatternAction, match *PatternMatch) ActionResult {
	result := ActionResult{Type: "alert", Success: true}

	alert := map[string]interface{}{
		"timestamp":    time.Now(),
		"pattern_id":   match.PatternID,
		"pattern_name": match.PatternName,
		"severity":     action.Parameters["severity"],
		"message":      action.Parameters["message"],
		"confidence":   match.Confidence,
		"event_count":  len(match.Events),
	}

	result.Output = alert
	e.logger.Warn("CEP alert triggered", zap.Any("alert", alert))

	return result
}

func (e *Engine) executeLogAction(action PatternAction, match *PatternMatch) ActionResult {
	result := ActionResult{Type: "log", Success: true}

	level := "info"
	if l, ok := action.Parameters["level"].(string); ok {
		level = l
	}

	message := fmt.Sprintf("CEP pattern matched: %s", match.PatternName)
	if m, ok := action.Parameters["message"].(string); ok {
		message = m
	}

	switch level {
	case "debug":
		e.logger.Debug(message, zap.Any("match", match))
	case "info":
		e.logger.Info(message, zap.Any("match", match))
	case "warn":
		e.logger.Warn(message, zap.Any("match", match))
	case "error":
		e.logger.Error(message, zap.Any("match", match))
	}

	return result
}

func (e *Engine) executeBlockAction(action PatternAction, match *PatternMatch) ActionResult {
	result := ActionResult{Type: "block", Success: true}

	duration := "5m"
	if d, ok := action.Parameters["duration"].(string); ok {
		duration = d
	}

	e.logger.Info("CEP block action triggered",
		zap.String("pattern", match.PatternName),
		zap.String("duration", duration))

	result.Output = map[string]interface{}{
		"blocked":  true,
		"duration": duration,
		"reason":   fmt.Sprintf("Pattern matched: %s", match.PatternName),
	}

	return result
}

func (e *Engine) executeEnrichAction(action PatternAction, match *PatternMatch) ActionResult {
	result := ActionResult{Type: "enrich", Success: true}

	// Add enrichment fields to the last event in the match
	if len(match.Events) > 0 {
		lastEvent := match.Events[len(match.Events)-1]
		if fields, ok := action.Parameters["fields"].(map[string]interface{}); ok {
			for k, v := range fields {
				lastEvent.Attributes[k] = v
			}
		}

		// Add pattern match metadata
		lastEvent.Attributes["cep_pattern_id"] = match.PatternID
		lastEvent.Attributes["cep_pattern_name"] = match.PatternName
		lastEvent.Attributes["cep_confidence"] = match.Confidence
		lastEvent.Attributes["cep_match_timestamp"] = match.Timestamp

		result.Output = map[string]interface{}{
			"enriched_event": lastEvent.ID,
			"fields_added":   len(lastEvent.Attributes),
		}
	}

	return result
}

// Helper functions for pattern matching
func getNestedValue(data map[string]interface{}, field string) interface{} {
	parts := strings.Split(field, ".")
	current := data

	for i, part := range parts {
		if i == len(parts)-1 {
			return current[part]
		}

		if next, ok := current[part].(map[string]interface{}); ok {
			current = next
		} else {
			return nil
		}
	}

	return nil
}

func compareValues(a, b interface{}, op string) bool {
	// Convert to strings for comparison
	aStr := fmt.Sprintf("%v", a)
	bStr := fmt.Sprintf("%v", b)

	// Try numeric comparison first
	if aNum, aErr := strconv.ParseFloat(aStr, 64); aErr == nil {
		if bNum, bErr := strconv.ParseFloat(bStr, 64); bErr == nil {
			switch op {
			case "eq":
				return aNum == bNum
			case "ne":
				return aNum != bNum
			case "gt":
				return aNum > bNum
			case "lt":
				return aNum < bNum
			case "gte":
				return aNum >= bNum
			case "lte":
				return aNum <= bNum
			}
		}
	}

	// Fall back to string comparison
	switch op {
	case "eq":
		return aStr == bStr
	case "ne":
		return aStr != bStr
	case "gt":
		return aStr > bStr
	case "lt":
		return aStr < bStr
	case "gte":
		return aStr >= bStr
	case "lte":
		return aStr <= bStr
	case "contains":
		return strings.Contains(aStr, bStr)
	case "starts_with":
		return strings.HasPrefix(aStr, bStr)
	case "ends_with":
		return strings.HasSuffix(aStr, bStr)
	}

	return false
}

// Management and utility methods

// handlePatternMatch handles a pattern match
func (e *Engine) handlePatternMatch(ctx context.Context, match *PatternMatch) {
	e.logger.Info("Pattern matched",
		zap.String("pattern_id", match.PatternID),
		zap.String("pattern_name", match.PatternName),
		zap.Float64("confidence", match.Confidence),
		zap.Int("event_count", len(match.Events)))
}

// cleanupWindows periodically cleans up old windows
func (e *Engine) cleanupWindows(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			e.performWindowCleanup()
		case <-e.stopCh:
			return
		case <-ctx.Done():
			return
		}
	}
}

// performWindowCleanup removes old windows
func (e *Engine) performWindowCleanup() {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	now := time.Now()
	for windowID, window := range e.windows {
		window.mutex.RLock()
		isEmpty := len(window.Events) == 0
		isOld := now.Sub(window.StartTime) > 10*time.Minute
		window.mutex.RUnlock()

		if isEmpty || isOld {
			delete(e.windows, windowID)
		}
	}
}

// updateStats updates CEP engine statistics
func (e *Engine) updateStats(events, patterns, correlations, threats int64) {
	e.stats.mutex.Lock()
	defer e.stats.mutex.Unlock()

	e.stats.EventsProcessed += events
	e.stats.PatternsMatched += patterns
	e.stats.CorrelationsFound += correlations
	e.stats.ThreatsDetected += threats
	e.stats.WindowsActive = int64(len(e.windows))
}

// updatePatternStats updates statistics for a specific pattern
func (e *Engine) updatePatternStats(patternID string, matched bool, latency time.Duration) {
	e.stats.mutex.Lock()
	defer e.stats.mutex.Unlock()

	if stats, exists := e.stats.PatternStats[patternID]; exists {
		stats.Evaluations++
		if matched {
			stats.Matches++
			stats.LastTriggered = time.Now()
		}
		// Update average latency
		if stats.Evaluations == 1 {
			stats.AvgLatency = latency
		} else {
			stats.AvgLatency = time.Duration((int64(stats.AvgLatency)*(stats.Evaluations-1) + int64(latency)) / stats.Evaluations)
		}
	}
}

// GetStats returns CEP engine statistics
func (e *Engine) GetStats() *CEPStats {
	e.stats.mutex.RLock()
	defer e.stats.mutex.RUnlock()

	// Create a copy to avoid race conditions
	stats := &CEPStats{
		EventsProcessed:   e.stats.EventsProcessed,
		PatternsMatched:   e.stats.PatternsMatched,
		CorrelationsFound: e.stats.CorrelationsFound,
		ThreatsDetected:   e.stats.ThreatsDetected,
		WindowsActive:     e.stats.WindowsActive,
		LastReset:         e.stats.LastReset,
		PatternStats:      make(map[string]*PatternStats),
	}

	for patternID, patternStats := range e.stats.PatternStats {
		stats.PatternStats[patternID] = &PatternStats{
			Evaluations:   patternStats.Evaluations,
			Matches:       patternStats.Matches,
			LastTriggered: patternStats.LastTriggered,
			AvgLatency:    patternStats.AvgLatency,
		}
	}

	return stats
}

// ResetStats resets CEP engine statistics
func (e *Engine) ResetStats() {
	e.stats.mutex.Lock()
	defer e.stats.mutex.Unlock()

	e.stats.EventsProcessed = 0
	e.stats.PatternsMatched = 0
	e.stats.CorrelationsFound = 0
	e.stats.ThreatsDetected = 0
	e.stats.WindowsActive = 0
	e.stats.LastReset = time.Now()

	for patternID := range e.stats.PatternStats {
		e.stats.PatternStats[patternID] = &PatternStats{}
	}

	e.logger.Info("CEP engine statistics reset")
}

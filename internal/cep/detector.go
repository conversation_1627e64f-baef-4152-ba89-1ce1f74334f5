package cep

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// ThreatDetection represents a detected threat
type ThreatDetection struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Description string                 `json:"description"`
	Event       *Event                 `json:"event"`
	Timestamp   time.Time              `json:"timestamp"`
	Confidence  float64                `json:"confidence"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// NewThreatDetector creates a new threat detector
func NewThreatDetector(config ThreatDetectionConfig, logger *zap.Logger) *ThreatDetector {
	detector := &ThreatDetector{
		detectors: make(map[string]*ThreatPattern),
		config:    config,
		logger:    logger,
	}

	// Initialize built-in threat patterns
	detector.initializePatterns()

	return detector
}

// Start starts the threat detector
func (d *ThreatDetector) Start(ctx context.Context) {
	if !d.config.Enabled {
		d.logger.Info("Threat detector is disabled")
		return
	}

	d.logger.Info("Starting threat detector",
		zap.Bool("ddos_detection", d.config.DDoSDetection),
		zap.Bool("brute_force_detection", d.config.BruteForceDetection),
		zap.Bool("anomaly_detection", d.config.AnomalyDetection))

	// Start update goroutine
	go d.updateDetectors(ctx)
}

// Stop stops the threat detector
func (d *ThreatDetector) Stop() {
	d.logger.Info("Stopping threat detector")
}

// ProcessEvent processes an event for threat detection
func (d *ThreatDetector) ProcessEvent(event *Event) []*ThreatDetection {
	if !d.config.Enabled {
		return nil
	}

	var threats []*ThreatDetection

	d.mutex.RLock()
	patterns := make([]*ThreatPattern, 0, len(d.detectors))
	for _, pattern := range d.detectors {
		if pattern.Enabled {
			patterns = append(patterns, pattern)
		}
	}
	d.mutex.RUnlock()

	for _, pattern := range patterns {
		if threat := d.evaluateThreatPattern(pattern, event); threat != nil {
			threats = append(threats, threat)
		}
	}

	return threats
}

// initializePatterns initializes built-in threat detection patterns
func (d *ThreatDetector) initializePatterns() {
	// DDoS Detection Pattern
	if d.config.DDoSDetection {
		ddosPattern := &ThreatPattern{
			ID:      "ddos_detection",
			Name:    "DDoS Attack Detection",
			Type:    "ddos",
			Enabled: true,
			Thresholds: map[string]float64{
				"requests_per_second": 100.0,
				"unique_sources":      10.0,
				"bytes_per_second":    1048576.0, // 1MB/s
			},
			Window:  5 * time.Minute,
			Actions: []string{"alert", "block"},
			Metadata: map[string]interface{}{
				"severity":    "critical",
				"description": "High volume of network requests indicating potential DDoS attack",
			},
		}
		d.detectors[ddosPattern.ID] = ddosPattern
	}

	// Brute Force Detection Pattern
	if d.config.BruteForceDetection {
		bruteForcePattern := &ThreatPattern{
			ID:      "brute_force_detection",
			Name:    "Brute Force Attack Detection",
			Type:    "brute_force",
			Enabled: true,
			Thresholds: map[string]float64{
				"failed_attempts":     5.0,
				"time_window_minutes": 2.0,
				"unique_usernames":    3.0,
			},
			Window:  2 * time.Minute,
			Actions: []string{"alert", "block"},
			Metadata: map[string]interface{}{
				"severity":    "high",
				"description": "Multiple failed authentication attempts indicating brute force attack",
			},
		}
		d.detectors[bruteForcePattern.ID] = bruteForcePattern
	}

	// Anomaly Detection Pattern
	if d.config.AnomalyDetection {
		anomalyPattern := &ThreatPattern{
			ID:      "anomaly_detection",
			Name:    "Behavioral Anomaly Detection",
			Type:    "anomaly",
			Enabled: true,
			Thresholds: map[string]float64{
				"deviation_score":    0.8,
				"confidence_level":   0.7,
				"baseline_deviation": 2.0,
			},
			Window:  10 * time.Minute,
			Actions: []string{"alert", "log"},
			Metadata: map[string]interface{}{
				"severity":    "medium",
				"description": "Unusual behavior pattern detected",
			},
		}
		d.detectors[anomalyPattern.ID] = anomalyPattern
	}

	d.logger.Info("Threat detection patterns initialized", zap.Int("count", len(d.detectors)))
}

// evaluateThreatPattern evaluates a threat pattern against an event
func (d *ThreatDetector) evaluateThreatPattern(pattern *ThreatPattern, event *Event) *ThreatDetection {
	switch pattern.Type {
	case "ddos":
		return d.evaluateDDoSPattern(pattern, event)
	case "brute_force":
		return d.evaluateBruteForcePattern(pattern, event)
	case "anomaly":
		return d.evaluateAnomalyPattern(pattern, event)
	default:
		return nil
	}
}

// evaluateDDoSPattern evaluates DDoS detection pattern
func (d *ThreatDetector) evaluateDDoSPattern(pattern *ThreatPattern, event *Event) *ThreatDetection {
	// Check if this is a network event
	if event.Type != "network_activity" && event.Type != "class_4001" {
		return nil
	}

	// Simple DDoS detection based on event frequency
	// In a real implementation, this would maintain state and track metrics over time

	// Check for high request rate indicators
	if bytesOut, ok := getNestedValue(event.Data, "traffic.bytes_out").(float64); ok {
		if bytesOut > pattern.Thresholds["bytes_per_second"] {
			return &ThreatDetection{
				ID:          fmt.Sprintf("ddos_%d", time.Now().UnixNano()),
				Type:        "ddos",
				Severity:    "critical",
				Description: "High bandwidth usage detected - potential DDoS attack",
				Event:       event,
				Timestamp:   time.Now(),
				Confidence:  0.8,
				Metadata: map[string]interface{}{
					"pattern_id":   pattern.ID,
					"bytes_out":    bytesOut,
					"threshold":    pattern.Thresholds["bytes_per_second"],
					"trigger_type": "bandwidth",
				},
			}
		}
	}

	return nil
}

// evaluateBruteForcePattern evaluates brute force detection pattern
func (d *ThreatDetector) evaluateBruteForcePattern(pattern *ThreatPattern, event *Event) *ThreatDetection {
	// Check if this is an authentication event
	activityName, ok := getNestedValue(event.Data, "activity_name").(string)
	if !ok || activityName != "Logon" {
		return nil
	}

	// Check if this is a failed authentication
	statusID, ok := getNestedValue(event.Data, "status_id").(float64)
	if !ok || statusID != 2 { // 2 = Failed
		return nil
	}

	// In a real implementation, this would track failed attempts over time
	// For now, we'll create a detection for any failed login
	return &ThreatDetection{
		ID:          fmt.Sprintf("brute_force_%d", time.Now().UnixNano()),
		Type:        "brute_force",
		Severity:    "high",
		Description: "Failed authentication attempt detected",
		Event:       event,
		Timestamp:   time.Now(),
		Confidence:  0.6,
		Metadata: map[string]interface{}{
			"pattern_id":    pattern.ID,
			"activity_name": activityName,
			"status_id":     statusID,
			"trigger_type":  "failed_auth",
		},
	}
}

// evaluateAnomalyPattern evaluates anomaly detection pattern
func (d *ThreatDetector) evaluateAnomalyPattern(pattern *ThreatPattern, event *Event) *ThreatDetection {
	// Simple anomaly detection based on event characteristics
	// In a real implementation, this would use machine learning models

	// Check for unusual event types or patterns
	if event.Type == "unknown" {
		return &ThreatDetection{
			ID:          fmt.Sprintf("anomaly_%d", time.Now().UnixNano()),
			Type:        "anomaly",
			Severity:    "medium",
			Description: "Unknown event type detected - potential anomaly",
			Event:       event,
			Timestamp:   time.Now(),
			Confidence:  0.5,
			Metadata: map[string]interface{}{
				"pattern_id":   pattern.ID,
				"event_type":   event.Type,
				"trigger_type": "unknown_event",
			},
		}
	}

	return nil
}

// updateDetectors periodically updates threat detection patterns
func (d *ThreatDetector) updateDetectors(ctx context.Context) {
	ticker := time.NewTicker(d.config.UpdateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			d.performUpdate()
		case <-ctx.Done():
			return
		}
	}
}

// performUpdate updates threat detection patterns
func (d *ThreatDetector) performUpdate() {
	d.mutex.Lock()
	defer d.mutex.Unlock()

	// In a real implementation, this would:
	// - Update threat intelligence feeds
	// - Adjust thresholds based on observed patterns
	// - Load new detection rules
	// - Update machine learning models

	d.logger.Debug("Threat detection patterns updated")
}

// GetDetectors returns current threat detection patterns
func (d *ThreatDetector) GetDetectors() map[string]*ThreatPattern {
	d.mutex.RLock()
	defer d.mutex.RUnlock()

	// Create a copy to avoid race conditions
	detectors := make(map[string]*ThreatPattern)
	for id, pattern := range d.detectors {
		detectors[id] = &ThreatPattern{
			ID:         pattern.ID,
			Name:       pattern.Name,
			Type:       pattern.Type,
			Enabled:    pattern.Enabled,
			Thresholds: make(map[string]float64),
			Window:     pattern.Window,
			Actions:    append([]string(nil), pattern.Actions...),
			Metadata:   make(map[string]interface{}),
		}

		// Copy thresholds
		for k, v := range pattern.Thresholds {
			detectors[id].Thresholds[k] = v
		}

		// Copy metadata
		for k, v := range pattern.Metadata {
			detectors[id].Metadata[k] = v
		}
	}

	return detectors
}

// GetDetectionStats returns threat detection statistics
func (d *ThreatDetector) GetDetectionStats() map[string]interface{} {
	d.mutex.RLock()
	defer d.mutex.RUnlock()

	enabledDetectors := 0
	for _, pattern := range d.detectors {
		if pattern.Enabled {
			enabledDetectors++
		}
	}

	return map[string]interface{}{
		"total_detectors":     len(d.detectors),
		"enabled_detectors":   enabledDetectors,
		"ddos_enabled":        d.config.DDoSDetection,
		"brute_force_enabled": d.config.BruteForceDetection,
		"anomaly_enabled":     d.config.AnomalyDetection,
		"update_interval":     d.config.UpdateInterval.String(),
	}
}

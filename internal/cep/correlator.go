package cep

import (
	"context"
	"crypto/md5"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// NewEventCorrelator creates a new event correlator
func NewEventCorrelator(config CorrelationConfig, logger *zap.Logger) *EventCorrelator {
	return &EventCorrelator{
		correlations: make(map[string]*CorrelationGroup),
		config:       config,
		logger:       logger,
	}
}

// Start starts the event correlator
func (c *EventCorrelator) Start(ctx context.Context) {
	if !c.config.Enabled {
		c.logger.Info("Event correlator is disabled")
		return
	}

	c.logger.Info("Starting event correlator",
		zap.Duration("window", c.config.Window),
		zap.Int("max_groups", c.config.MaxGroups),
		zap.Float64("threshold", c.config.ScoreThreshold))

	// Start cleanup goroutine
	go c.cleanupCorrelations(ctx)
}

// Stop stops the event correlator
func (c *EventCorrelator) Stop() {
	c.logger.Info("Stopping event correlator")
}

// ProcessEvent processes an event for correlation
func (c *EventCorrelator) ProcessEvent(event *Event) {
	if !c.config.Enabled {
		return
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Generate correlation key
	correlationKey := c.generateCorrelationKey(event)
	if correlationKey == "" {
		return
	}

	// Find or create correlation group
	group, exists := c.correlations[correlationKey]
	if !exists {
		group = &CorrelationGroup{
			ID:        fmt.Sprintf("corr_%s_%d", correlationKey, time.Now().UnixNano()),
			Events:    make([]*Event, 0),
			StartTime: event.Timestamp,
			LastSeen:  event.Timestamp,
			Score:     0.0,
		}
		c.correlations[correlationKey] = group
	}

	// Add event to group
	group.Events = append(group.Events, event)
	group.LastSeen = event.Timestamp
	group.Score = c.calculateCorrelationScore(group)

	c.logger.Debug("Event correlated",
		zap.String("correlation_id", group.ID),
		zap.String("correlation_key", correlationKey),
		zap.Int("event_count", len(group.Events)),
		zap.Float64("score", group.Score))
}

// generateCorrelationKey generates a correlation key for an event
func (c *EventCorrelator) generateCorrelationKey(event *Event) string {
	if len(c.config.CorrelationKeys) == 0 {
		return ""
	}

	var keyParts []string
	for _, key := range c.config.CorrelationKeys {
		if value := getNestedValue(event.Data, key); value != nil {
			keyParts = append(keyParts, fmt.Sprintf("%v", value))
		}
	}

	if len(keyParts) == 0 {
		return ""
	}

	// Create hash of key parts for consistent grouping
	keyString := fmt.Sprintf("%v", keyParts)
	hash := md5.Sum([]byte(keyString))
	return fmt.Sprintf("%x", hash)[:16]
}

// calculateCorrelationScore calculates the correlation score for a group
func (c *EventCorrelator) calculateCorrelationScore(group *CorrelationGroup) float64 {
	if len(group.Events) <= 1 {
		return 0.0
	}

	// Simple scoring based on event count and time window
	eventCount := float64(len(group.Events))
	timeSpan := group.LastSeen.Sub(group.StartTime).Seconds()

	// Higher score for more events in shorter time
	if timeSpan == 0 {
		timeSpan = 1 // Avoid division by zero
	}

	score := eventCount / timeSpan

	// Normalize to 0-1 range
	if score > 1.0 {
		score = 1.0
	}

	return score
}

// cleanupCorrelations periodically cleans up old correlation groups
func (c *EventCorrelator) cleanupCorrelations(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.performCleanup()
		case <-ctx.Done():
			return
		}
	}
}

// performCleanup removes old correlation groups
func (c *EventCorrelator) performCleanup() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	now := time.Now()
	cutoff := now.Add(-c.config.Window)

	for key, group := range c.correlations {
		if group.LastSeen.Before(cutoff) {
			delete(c.correlations, key)
		}
	}

	// Limit number of active groups
	if len(c.correlations) > c.config.MaxGroups {
		// Remove oldest groups
		type groupWithKey struct {
			key   string
			group *CorrelationGroup
		}

		var groups []groupWithKey
		for key, group := range c.correlations {
			groups = append(groups, groupWithKey{key: key, group: group})
		}

		// Sort by last seen time (oldest first)
		for i := 0; i < len(groups)-1; i++ {
			for j := i + 1; j < len(groups); j++ {
				if groups[i].group.LastSeen.After(groups[j].group.LastSeen) {
					groups[i], groups[j] = groups[j], groups[i]
				}
			}
		}

		// Remove excess groups
		excess := len(groups) - c.config.MaxGroups
		for i := 0; i < excess; i++ {
			delete(c.correlations, groups[i].key)
		}
	}
}

// GetCorrelations returns current correlation groups
func (c *EventCorrelator) GetCorrelations() map[string]*CorrelationGroup {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// Create a copy to avoid race conditions
	correlations := make(map[string]*CorrelationGroup)
	for key, group := range c.correlations {
		correlations[key] = &CorrelationGroup{
			ID:        group.ID,
			Events:    append([]*Event(nil), group.Events...),
			StartTime: group.StartTime,
			LastSeen:  group.LastSeen,
			Score:     group.Score,
		}
	}

	return correlations
}

// GetCorrelationStats returns correlation statistics
func (c *EventCorrelator) GetCorrelationStats() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	totalEvents := 0
	totalGroups := len(c.correlations)
	highScoreGroups := 0

	for _, group := range c.correlations {
		totalEvents += len(group.Events)
		if group.Score >= c.config.ScoreThreshold {
			highScoreGroups++
		}
	}

	return map[string]interface{}{
		"total_groups":       totalGroups,
		"total_events":       totalEvents,
		"high_score_groups":  highScoreGroups,
		"score_threshold":    c.config.ScoreThreshold,
		"correlation_window": c.config.Window.String(),
	}
}

package pipeline

import (
	"encoding/json"
	"fmt"
	"time"
)

// APIVersion represents the pipeline API version
type APIVersion string

const (
	APIVersionV1Beta1 APIVersion = "pipeline.magna.ciso.ibm.com/v1beta1"
	APIVersionV1Beta2 APIVersion = "pipeline.magna.ciso.ibm.com/v1beta2"
)

// Definition represents a pipeline definition
type Definition struct {
	APIVersion APIVersion `yaml:"apiVersion" json:"apiVersion"`
	Kind       string     `yaml:"kind" json:"kind"`
	Meta       ObjectMeta `yaml:"meta" json:"meta"`
	Spec       Spec       `yaml:"spec" json:"spec"`
}

// ObjectMeta contains metadata about the pipeline
type ObjectMeta struct {
	Name        string            `yaml:"name" json:"name"`
	Annotations map[string]string `yaml:"annotations,omitempty" json:"annotations,omitempty"`
}

// Spec contains the pipeline specification
type Spec struct {
	SchemaPath         *string    `yaml:"schemaPath,omitempty" json:"schemaPath,omitempty"`
	TransformationPath string     `yaml:"transformationPath" json:"transformationPath"`
	Examples           []PairSpec `yaml:"examples" json:"examples"`
}

// PairSpec represents an input/output example pair
type PairSpec struct {
	Input  string `yaml:"input" json:"input"`
	Output string `yaml:"output" json:"output"`
}

// TransformationContext represents the context passed to transformations
type TransformationContext struct {
	ProcessedTime   string `json:"processedTime"`
	SourceOwnerID   string `json:"sourceOwnerId"`
	EventUUID       string `json:"eventUuid"`
	LogString       string `json:"logString"`
	Dataset         string `json:"dataset"`
	Namespace       string `json:"namespace"`
}

// NewTransformationContext creates a new transformation context with default values
func NewTransformationContext(logString, dataset, namespace string) *TransformationContext {
	return &TransformationContext{
		ProcessedTime: time.Now().Format("2006-01-02T15:04:05.000Z"),
		SourceOwnerID: "00000000-0000-0000-0000-000000000000",
		EventUUID:     "00000000-0000-0000-0000-000000000000",
		LogString:     logString,
		Dataset:       dataset,
		Namespace:     namespace,
	}
}

// ToMap converts the transformation context to a map for use in transformations
func (tc *TransformationContext) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"processedTime":   tc.ProcessedTime,
		"sourceOwnerId":   tc.SourceOwnerID,
		"eventUuid":       tc.EventUUID,
		"logString":       tc.LogString,
		"dataset":         tc.Dataset,
		"namespace":       tc.Namespace,
	}
}

// ValidationResult represents the result of pipeline validation
type ValidationResult struct {
	Valid       bool                   `json:"valid"`
	Errors      []ValidationError      `json:"errors,omitempty"`
	Warnings    []ValidationWarning    `json:"warnings,omitempty"`
	Summary     ValidationSummary      `json:"summary"`
	ProcessedAt time.Time              `json:"processed_at"`
}

// ValidationError represents a validation error
type ValidationError struct {
	File        string `json:"file"`
	Path        string `json:"path,omitempty"`
	Message     string `json:"message"`
	SchemaType  string `json:"schema_type,omitempty"`
	Severity    string `json:"severity"`
}

// ValidationWarning represents a validation warning
type ValidationWarning struct {
	File    string `json:"file"`
	Path    string `json:"path,omitempty"`
	Message string `json:"message"`
}

// ValidationSummary provides a summary of validation results
type ValidationSummary struct {
	TotalExamples    int `json:"total_examples"`
	ValidExamples    int `json:"valid_examples"`
	InvalidExamples  int `json:"invalid_examples"`
	TotalErrors      int `json:"total_errors"`
	TotalWarnings    int `json:"total_warnings"`
	OCSFValidation   bool `json:"ocsf_validation"`
	SchemaValidation bool `json:"schema_validation"`
	ECSValidation    bool `json:"ecs_validation"`
}

// ApplyResult represents the result of applying a transformation
type ApplyResult struct {
	Input       json.RawMessage `json:"input"`
	Output      json.RawMessage `json:"output"`
	ProcessedAt time.Time       `json:"processed_at"`
	Duration    time.Duration   `json:"duration"`
	Success     bool            `json:"success"`
	Error       string          `json:"error,omitempty"`
}

// InferredSchema represents an inferred JSON schema
type InferredSchema struct {
	Schema      map[string]interface{} `json:"schema"`
	Examples    int                    `json:"examples_processed"`
	GeneratedAt time.Time              `json:"generated_at"`
	Version     string                 `json:"version"`
}

// UpdateResult represents the result of updating pipeline outputs
type UpdateResult struct {
	UpdatedFiles []string      `json:"updated_files"`
	ProcessedAt  time.Time     `json:"processed_at"`
	Duration     time.Duration `json:"duration"`
	Success      bool          `json:"success"`
	Errors       []string      `json:"errors,omitempty"`
}

// TransformationEngine interface for applying transformations
type TransformationEngine interface {
	Apply(context map[string]interface{}, input json.RawMessage) (json.RawMessage, error)
	ApplyWithContext(context *TransformationContext, input json.RawMessage) (json.RawMessage, error)
}

// SchemaValidator interface for validating against schemas
type SchemaValidator interface {
	ValidateOCSF(data json.RawMessage) []ValidationError
	ValidateCustom(schema map[string]interface{}, data json.RawMessage) []ValidationError
	ValidateECS(data json.RawMessage) []ValidationError
}

// PipelineManager manages pipeline operations
type PipelineManager struct {
	transformationEngine TransformationEngine
	schemaValidator      SchemaValidator
	workingDir           string
}

// NewPipelineManager creates a new pipeline manager
func NewPipelineManager(workingDir string, engine TransformationEngine, validator SchemaValidator) *PipelineManager {
	return &PipelineManager{
		transformationEngine: engine,
		schemaValidator:      validator,
		workingDir:           workingDir,
	}
}

// LoadDefinition loads a pipeline definition from a file
func (pm *PipelineManager) LoadDefinition(definitionPath string) (*Definition, error) {
	// Implementation will be added in the service file
	return nil, fmt.Errorf("not implemented")
}

// ValidatePipeline validates a pipeline definition and its examples
func (pm *PipelineManager) ValidatePipeline(definition *Definition) (*ValidationResult, error) {
	// Implementation will be added in the service file
	return nil, fmt.Errorf("not implemented")
}

// ApplyPipeline applies a pipeline transformation to input data
func (pm *PipelineManager) ApplyPipeline(definition *Definition, input json.RawMessage) (*ApplyResult, error) {
	// Implementation will be added in the service file
	return nil, fmt.Errorf("not implemented")
}

// InferSchema infers a JSON schema from pipeline output examples
func (pm *PipelineManager) InferSchema(definition *Definition) (*InferredSchema, error) {
	// Implementation will be added in the service file
	return nil, fmt.Errorf("not implemented")
}

// UpdatePipelineOutputs updates pipeline output files with current transformation results
func (pm *PipelineManager) UpdatePipelineOutputs(definition *Definition) (*UpdateResult, error) {
	// Implementation will be added in the service file
	return nil, fmt.Errorf("not implemented")
}

// PipelineStats represents pipeline processing statistics
type PipelineStats struct {
	TotalPipelines      int           `json:"total_pipelines"`
	ValidPipelines      int           `json:"valid_pipelines"`
	InvalidPipelines    int           `json:"invalid_pipelines"`
	TotalTransformations int          `json:"total_transformations"`
	AverageProcessingTime time.Duration `json:"average_processing_time"`
	LastProcessed       time.Time     `json:"last_processed"`
}

// GetStats returns pipeline processing statistics
func (pm *PipelineManager) GetStats() *PipelineStats {
	return &PipelineStats{
		LastProcessed: time.Now(),
	}
}

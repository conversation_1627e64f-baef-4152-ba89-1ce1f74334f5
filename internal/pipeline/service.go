package pipeline

import (
	"context"
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"
	"gopkg.in/yaml.v3"

	"github.com/gollm/core-gollmslake-go/internal/ocsf"
	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// Service provides pipeline management functionality
type Service struct {
	config          config.Config
	logger          *zap.Logger
	ocsfService     *ocsf.Service
	transformEngine *JSLTEngine
	workingDir      string
}

// NewService creates a new pipeline service
func NewService(cfg config.Config, logger *zap.Logger) (*Service, error) {
	// Initialize OCSF service for validation
	ocsfService, err := ocsf.NewService(cfg.OCSFSchemas, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create OCSF service: %w", err)
	}

	// Initialize transformation engine
	transformEngine, err := NewJSLTEngine(logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create transformation engine: %w", err)
	}

	return &Service{
		config:          cfg,
		logger:          logger,
		ocsfService:     ocsfService,
		transformEngine: transformEngine,
		workingDir:      ".",
	}, nil
}

// SetWorkingDirectory sets the working directory for pipeline operations
func (s *Service) SetWorkingDirectory(dir string) {
	s.workingDir = dir
}

// LoadDefinition loads a pipeline definition from a file
func (s *Service) LoadDefinition(definitionPath string) (*Definition, error) {
	fullPath := filepath.Join(s.workingDir, definitionPath)

	data, err := os.ReadFile(fullPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read pipeline definition: %w", err)
	}

	var definition Definition
	if err := yaml.Unmarshal(data, &definition); err != nil {
		return nil, fmt.Errorf("failed to parse pipeline definition: %w", err)
	}

	// Validate required fields
	if definition.APIVersion == "" {
		return nil, fmt.Errorf("apiVersion is required")
	}
	if definition.Kind != "Pipeline" {
		return nil, fmt.Errorf("kind must be 'Pipeline'")
	}
	if definition.Spec.TransformationPath == "" {
		return nil, fmt.Errorf("transformationPath is required")
	}

	return &definition, nil
}

// ValidatePipeline validates a pipeline definition and its examples
func (s *Service) ValidatePipeline(definition *Definition) (*ValidationResult, error) {
	start := time.Now()
	result := &ValidationResult{
		Valid:       true,
		Errors:      []ValidationError{},
		Warnings:    []ValidationWarning{},
		ProcessedAt: start,
	}

	// Load transformation
	transformation, err := s.loadTransformation(definition)
	if err != nil {
		result.Valid = false
		result.Errors = append(result.Errors, ValidationError{
			File:     definition.Spec.TransformationPath,
			Message:  fmt.Sprintf("Failed to load transformation: %v", err),
			Severity: "error",
		})
		return result, nil
	}

	// Load custom schema if specified
	var customSchema map[string]interface{}
	if definition.Spec.SchemaPath != nil {
		schema, err := s.loadSchema(*definition.Spec.SchemaPath)
		if err != nil {
			result.Warnings = append(result.Warnings, ValidationWarning{
				File:    *definition.Spec.SchemaPath,
				Message: fmt.Sprintf("Failed to load custom schema: %v", err),
			})
		} else {
			customSchema = schema
		}
	}

	// Validate each example
	result.Summary.TotalExamples = len(definition.Spec.Examples)
	for _, example := range definition.Spec.Examples {
		if err := s.validateExample(definition, example, transformation, customSchema, result); err != nil {
			s.logger.Error("Failed to validate example",
				zap.String("input", example.Input),
				zap.String("output", example.Output),
				zap.Error(err))
		}
	}

	// Update summary
	result.Summary.ValidExamples = result.Summary.TotalExamples - result.Summary.InvalidExamples
	result.Summary.TotalErrors = len(result.Errors)
	result.Summary.TotalWarnings = len(result.Warnings)
	result.Summary.OCSFValidation = true
	result.Summary.SchemaValidation = customSchema != nil
	result.Summary.ECSValidation = false // ECS validation not implemented yet

	if len(result.Errors) > 0 {
		result.Valid = false
	}

	return result, nil
}

// validateExample validates a single input/output example
func (s *Service) validateExample(definition *Definition, example PairSpec, transformation *JSLTTransformation, customSchema map[string]interface{}, result *ValidationResult) error {
	// Load input and expected output
	inputPath := filepath.Join(s.workingDir, example.Input)
	outputPath := filepath.Join(s.workingDir, example.Output)

	inputData, err := os.ReadFile(inputPath)
	if err != nil {
		result.Errors = append(result.Errors, ValidationError{
			File:     example.Input,
			Message:  fmt.Sprintf("Failed to read input file: %v", err),
			Severity: "error",
		})
		result.Summary.InvalidExamples++
		return err
	}

	_, err = os.ReadFile(outputPath)
	if err != nil {
		result.Errors = append(result.Errors, ValidationError{
			File:     example.Output,
			Message:  fmt.Sprintf("Failed to read output file: %v", err),
			Severity: "error",
		})
		result.Summary.InvalidExamples++
		return err
	}

	// Parse input JSON
	var inputJSON json.RawMessage
	if err := json.Unmarshal(inputData, &inputJSON); err != nil {
		result.Errors = append(result.Errors, ValidationError{
			File:     example.Input,
			Message:  fmt.Sprintf("Invalid JSON in input file: %v", err),
			Severity: "error",
		})
		result.Summary.InvalidExamples++
		return err
	}

	// Create transformation context
	context := NewTransformationContext(string(inputData), "contrib_pipelines.samples", "test")

	// Apply transformation
	transformedData, err := transformation.Apply(context.ToMap(), inputJSON)
	if err != nil {
		result.Errors = append(result.Errors, ValidationError{
			File:     example.Input,
			Message:  fmt.Sprintf("Transformation failed: %v", err),
			Severity: "error",
		})
		result.Summary.InvalidExamples++
		return err
	}

	// Validate transformed output against OCSF schemas
	if err := s.validateOCSF(transformedData, example.Input, result); err != nil {
		return err
	}

	// Validate against custom schema if provided
	if customSchema != nil {
		if err := s.validateCustomSchema(transformedData, customSchema, example.Input, result); err != nil {
			return err
		}
	}

	return nil
}

// validateOCSF validates data against OCSF schemas
func (s *Service) validateOCSF(data json.RawMessage, filename string, result *ValidationResult) error {
	// Parse the data to extract class_uid
	var eventData map[string]interface{}
	if err := json.Unmarshal(data, &eventData); err != nil {
		result.Errors = append(result.Errors, ValidationError{
			File:       filename,
			Message:    fmt.Sprintf("Failed to parse transformed data: %v", err),
			SchemaType: "ocsf",
			Severity:   "error",
		})
		result.Summary.InvalidExamples++
		return err
	}

	// Handle array output
	if dataArray, ok := eventData["data"].([]interface{}); ok {
		for i, item := range dataArray {
			itemData, err := json.Marshal(item)
			if err != nil {
				continue
			}
			if err := s.validateSingleOCSF(itemData, fmt.Sprintf("%s[%d]", filename, i), result); err != nil {
				return err
			}
		}
	} else {
		return s.validateSingleOCSF(data, filename, result)
	}

	return nil
}

// validateSingleOCSF validates a single event against OCSF schema
func (s *Service) validateSingleOCSF(data json.RawMessage, filename string, result *ValidationResult) error {
	// Parse the data to extract event
	var eventData map[string]interface{}
	if err := json.Unmarshal(data, &eventData); err != nil {
		result.Errors = append(result.Errors, ValidationError{
			File:       filename,
			Message:    fmt.Sprintf("Failed to parse event data: %v", err),
			SchemaType: "ocsf",
			Severity:   "error",
		})
		result.Summary.InvalidExamples++
		return err
	}

	ctx := context.Background()
	validationResult, err := s.ocsfService.ValidateEvent(ctx, eventData)
	if err != nil {
		result.Errors = append(result.Errors, ValidationError{
			File:       filename,
			Message:    fmt.Sprintf("OCSF validation failed: %v", err),
			SchemaType: "ocsf",
			Severity:   "error",
		})
		result.Summary.InvalidExamples++
		return err
	}

	if !validationResult.Valid {
		for _, validationErr := range validationResult.Errors {
			result.Errors = append(result.Errors, ValidationError{
				File:       filename,
				Path:       validationErr.Field,
				Message:    validationErr.Message,
				SchemaType: "ocsf",
				Severity:   "error",
			})
		}
		result.Summary.InvalidExamples++
	}

	return nil
}

// validateCustomSchema validates data against a custom JSON schema
func (s *Service) validateCustomSchema(data json.RawMessage, schema map[string]interface{}, filename string, result *ValidationResult) error {
	// Custom schema validation would be implemented here
	// For now, we'll just log that it's not implemented
	s.logger.Debug("Custom schema validation not yet implemented", zap.String("file", filename))
	return nil
}

// ApplyPipeline applies a pipeline transformation to input data
func (s *Service) ApplyPipeline(definition *Definition, input json.RawMessage) (*ApplyResult, error) {
	start := time.Now()

	// Load transformation
	transformation, err := s.loadTransformation(definition)
	if err != nil {
		return &ApplyResult{
			Input:       input,
			ProcessedAt: start,
			Duration:    time.Since(start),
			Success:     false,
			Error:       fmt.Sprintf("Failed to load transformation: %v", err),
		}, nil
	}

	// Create transformation context
	context := NewTransformationContext(string(input), "contrib_pipelines.samples", "test")

	// Apply transformation
	output, err := transformation.Apply(context.ToMap(), input)
	if err != nil {
		return &ApplyResult{
			Input:       input,
			ProcessedAt: start,
			Duration:    time.Since(start),
			Success:     false,
			Error:       fmt.Sprintf("Transformation failed: %v", err),
		}, nil
	}

	return &ApplyResult{
		Input:       input,
		Output:      output,
		ProcessedAt: start,
		Duration:    time.Since(start),
		Success:     true,
	}, nil
}

// loadTransformation loads a JSLT transformation from file
func (s *Service) loadTransformation(definition *Definition) (*JSLTTransformation, error) {
	transformationPath := filepath.Join(s.workingDir, definition.Spec.TransformationPath)

	data, err := os.ReadFile(transformationPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read transformation file: %w", err)
	}

	return NewJSLTTransformation(string(data))
}

// loadSchema loads a JSON schema from file
func (s *Service) loadSchema(schemaPath string) (map[string]interface{}, error) {
	fullPath := filepath.Join(s.workingDir, schemaPath)

	data, err := os.ReadFile(fullPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read schema file: %w", err)
	}

	var schema map[string]interface{}
	if strings.HasSuffix(schemaPath, ".yaml") || strings.HasSuffix(schemaPath, ".yml") {
		if err := yaml.Unmarshal(data, &schema); err != nil {
			return nil, fmt.Errorf("failed to parse YAML schema: %w", err)
		}
	} else {
		if err := json.Unmarshal(data, &schema); err != nil {
			return nil, fmt.Errorf("failed to parse JSON schema: %w", err)
		}
	}

	return schema, nil
}

// InferSchema infers a JSON schema from pipeline output examples
func (s *Service) InferSchema(definition *Definition) (*InferredSchema, error) {
	start := time.Now()

	// Collect all output examples
	var outputNodes []map[string]interface{}

	for _, example := range definition.Spec.Examples {
		outputPath := filepath.Join(s.workingDir, example.Output)

		data, err := os.ReadFile(outputPath)
		if err != nil {
			return nil, fmt.Errorf("failed to read output file %s: %w", example.Output, err)
		}

		var outputData interface{}
		if err := json.Unmarshal(data, &outputData); err != nil {
			return nil, fmt.Errorf("failed to parse output JSON %s: %w", example.Output, err)
		}

		// Handle array outputs
		if arr, ok := outputData.([]interface{}); ok {
			for _, item := range arr {
				if itemMap, ok := item.(map[string]interface{}); ok {
					outputNodes = append(outputNodes, itemMap)
				}
			}
		} else if objMap, ok := outputData.(map[string]interface{}); ok {
			outputNodes = append(outputNodes, objMap)
		}
	}

	// Generate schema from collected examples
	schema := s.generateSchemaFromExamples(outputNodes)

	return &InferredSchema{
		Schema:      schema,
		Examples:    len(outputNodes),
		GeneratedAt: start,
		Version:     "1.0.0",
	}, nil
}

// UpdatePipelineOutputs updates pipeline output files with current transformation results
func (s *Service) UpdatePipelineOutputs(definition *Definition) (*UpdateResult, error) {
	start := time.Now()
	result := &UpdateResult{
		UpdatedFiles: []string{},
		ProcessedAt:  start,
		Success:      true,
		Errors:       []string{},
	}

	// Load transformation
	transformation, err := s.loadTransformation(definition)
	if err != nil {
		result.Success = false
		result.Errors = append(result.Errors, fmt.Sprintf("Failed to load transformation: %v", err))
		result.Duration = time.Since(start)
		return result, nil
	}

	// Process each example
	for _, example := range definition.Spec.Examples {
		if err := s.updateSingleOutput(definition, example, transformation, result); err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Failed to update %s: %v", example.Output, err))
		}
	}

	if len(result.Errors) > 0 {
		result.Success = false
	}

	result.Duration = time.Since(start)
	return result, nil
}

// updateSingleOutput updates a single output file
func (s *Service) updateSingleOutput(definition *Definition, example PairSpec, transformation *JSLTTransformation, result *UpdateResult) error {
	// Load input
	inputPath := filepath.Join(s.workingDir, example.Input)
	inputData, err := os.ReadFile(inputPath)
	if err != nil {
		return fmt.Errorf("failed to read input file: %w", err)
	}

	// Parse input JSON
	var inputJSON json.RawMessage
	if err := json.Unmarshal(inputData, &inputJSON); err != nil {
		return fmt.Errorf("failed to parse input JSON: %w", err)
	}

	// Create transformation context
	context := NewTransformationContext(string(inputData), "contrib_pipelines.samples", "test")

	// Apply transformation
	outputData, err := transformation.Apply(context.ToMap(), inputJSON)
	if err != nil {
		return fmt.Errorf("transformation failed: %w", err)
	}

	// Write updated output
	outputPath := filepath.Join(s.workingDir, example.Output)

	// Pretty print JSON
	var prettyJSON interface{}
	if err := json.Unmarshal(outputData, &prettyJSON); err != nil {
		return fmt.Errorf("failed to parse output JSON: %w", err)
	}

	prettyData, err := json.MarshalIndent(prettyJSON, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to format output JSON: %w", err)
	}

	if err := os.WriteFile(outputPath, prettyData, 0644); err != nil {
		return fmt.Errorf("failed to write output file: %w", err)
	}

	result.UpdatedFiles = append(result.UpdatedFiles, example.Output)
	return nil
}

// generateSchemaFromExamples generates a JSON schema from example data
func (s *Service) generateSchemaFromExamples(examples []map[string]interface{}) map[string]interface{} {
	if len(examples) == 0 {
		return map[string]interface{}{
			"type": "object",
		}
	}

	// Merge all examples to find all possible fields
	allFields := make(map[string][]interface{})

	for _, example := range examples {
		for key, value := range example {
			if _, exists := allFields[key]; !exists {
				allFields[key] = []interface{}{}
			}
			allFields[key] = append(allFields[key], value)
		}
	}

	// Generate schema properties
	properties := make(map[string]interface{})
	required := []string{}

	for field, values := range allFields {
		fieldSchema := s.inferFieldSchema(values)
		properties[field] = fieldSchema

		// Mark as required if present in all examples
		if len(values) == len(examples) {
			required = append(required, field)
		}
	}

	schema := map[string]interface{}{
		"type":       "object",
		"properties": properties,
	}

	if len(required) > 0 {
		schema["required"] = required
	}

	return schema
}

// inferFieldSchema infers schema for a field based on its values
func (s *Service) inferFieldSchema(values []interface{}) map[string]interface{} {
	if len(values) == 0 {
		return map[string]interface{}{"type": "null"}
	}

	// Determine the most common type
	typeCounts := make(map[string]int)

	for _, value := range values {
		jsonType := s.getJSONType(value)
		typeCounts[jsonType]++
	}

	// Find the most common type
	maxCount := 0
	mostCommonType := "string"

	for jsonType, count := range typeCounts {
		if count > maxCount {
			maxCount = count
			mostCommonType = jsonType
		}
	}

	schema := map[string]interface{}{
		"type": mostCommonType,
	}

	// Add additional constraints based on type
	switch mostCommonType {
	case "string":
		// Could add pattern, minLength, maxLength, etc.
	case "number", "integer":
		// Could add minimum, maximum, etc.
	case "array":
		// Could infer item schema
		schema["items"] = map[string]interface{}{"type": "string"}
	case "object":
		// Could infer nested properties
		schema["additionalProperties"] = true
	}

	return schema
}

// getJSONType returns the JSON type of a value
func (s *Service) getJSONType(value interface{}) string {
	if value == nil {
		return "null"
	}

	switch value.(type) {
	case bool:
		return "boolean"
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
		return "integer"
	case float32, float64:
		return "number"
	case string:
		return "string"
	case []interface{}:
		return "array"
	case map[string]interface{}:
		return "object"
	default:
		return "string"
	}
}

// ListPipelines lists all pipeline definitions in a directory
func (s *Service) ListPipelines(dir string) ([]string, error) {
	var pipelines []string

	err := filepath.WalkDir(dir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if d.Name() == "pipeline.yaml" || d.Name() == "pipeline.yml" {
			relPath, err := filepath.Rel(dir, filepath.Dir(path))
			if err != nil {
				return err
			}
			pipelines = append(pipelines, relPath)
		}

		return nil
	})

	return pipelines, err
}

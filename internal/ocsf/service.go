package ocsf

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/patrickmn/go-cache"
	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// Service provides OCSF schema validation and management
type Service struct {
	loader *SchemaLoader
	cache  *cache.Cache
	config config.OCSFSchemasConfig
	logger *zap.Logger
	stats  *ValidationStats
	mutex  sync.RWMutex
}

// ValidationStats tracks validation statistics
type ValidationStats struct {
	TotalValidations   int64     `json:"total_validations"`
	SuccessfulValidations int64  `json:"successful_validations"`
	FailedValidations  int64     `json:"failed_validations"`
	ValidationsBySchema map[string]int64 `json:"validations_by_schema"`
	LastReset          time.Time `json:"last_reset"`
	mutex              sync.RWMutex
}

// SchemaInfo provides information about loaded schemas
type SchemaInfo struct {
	Name        string                 `json:"name"`
	Version     string                 `json:"version"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`
	ClassUID    int                    `json:"class_uid"`
	Type        string                 `json:"type"`
	Required    []string               `json:"required"`
	Properties  int                    `json:"properties_count"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// ValidationRequest represents a validation request
type ValidationRequest struct {
	Event      map[string]interface{} `json:"event"`
	SchemaName string                 `json:"schema_name,omitempty"`
	Options    ValidationOptions      `json:"options,omitempty"`
}

// ValidationOptions provides validation configuration
type ValidationOptions struct {
	Mode           string `json:"mode,omitempty"`           // strict, lenient, disabled
	CacheResult    bool   `json:"cache_result,omitempty"`
	IncludeWarnings bool  `json:"include_warnings,omitempty"`
}

// BatchValidationRequest represents a batch validation request
type BatchValidationRequest struct {
	Events  []map[string]interface{} `json:"events"`
	Options ValidationOptions        `json:"options,omitempty"`
}

// BatchValidationResponse represents a batch validation response
type BatchValidationResponse struct {
	Results    []ValidationResult `json:"results"`
	Summary    ValidationSummary  `json:"summary"`
	Duration   time.Duration      `json:"duration"`
	Timestamp  time.Time          `json:"timestamp"`
}

// ValidationSummary provides summary statistics for batch validation
type ValidationSummary struct {
	Total      int `json:"total"`
	Valid      int `json:"valid"`
	Invalid    int `json:"invalid"`
	Warnings   int `json:"warnings"`
	Errors     int `json:"errors"`
}

// NewService creates a new OCSF service
func NewService(cfg config.OCSFSchemasConfig, logger *zap.Logger) (*Service, error) {
	loader, err := NewSchemaLoader(cfg, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create schema loader: %w", err)
	}

	var cacheInstance *cache.Cache
	if cfg.CacheEnabled {
		cacheInstance = cache.New(cfg.CacheTTL, cfg.CacheTTL*2)
	}

	stats := &ValidationStats{
		ValidationsBySchema: make(map[string]int64),
		LastReset:          time.Now(),
	}

	return &Service{
		loader: loader,
		cache:  cacheInstance,
		config: cfg,
		logger: logger,
		stats:  stats,
	}, nil
}

// ValidateEvent validates a single event
func (s *Service) ValidateEvent(ctx context.Context, event map[string]interface{}) (*ValidationResult, error) {
	if !s.config.Enabled {
		return &ValidationResult{
			Valid:      true,
			SchemaName: "validation-disabled",
		}, nil
	}

	// Check cache if enabled
	if s.cache != nil {
		cacheKey := s.generateCacheKey(event)
		if cached, found := s.cache.Get(cacheKey); found {
			if result, ok := cached.(*ValidationResult); ok {
				s.updateStats(result.SchemaName, result.Valid)
				return result, nil
			}
		}
	}

	// Perform validation
	result, err := s.loader.ValidateEvent(ctx, event)
	if err != nil {
		s.updateStats("error", false)
		return nil, err
	}

	// Cache result if enabled
	if s.cache != nil && result != nil {
		cacheKey := s.generateCacheKey(event)
		s.cache.Set(cacheKey, result, cache.DefaultExpiration)
	}

	s.updateStats(result.SchemaName, result.Valid)
	return result, nil
}

// ValidateBatch validates multiple events
func (s *Service) ValidateBatch(ctx context.Context, events []map[string]interface{}) (*BatchValidationResponse, error) {
	start := time.Now()
	response := &BatchValidationResponse{
		Results:   make([]ValidationResult, 0, len(events)),
		Timestamp: start,
	}

	for _, event := range events {
		result, err := s.ValidateEvent(ctx, event)
		if err != nil {
			// Create error result
			result = &ValidationResult{
				Valid:      false,
				Errors:     []ValidationError{{Message: err.Error()}},
				SchemaName: "validation-error",
			}
		}
		response.Results = append(response.Results, *result)
	}

	// Calculate summary
	response.Summary = s.calculateSummary(response.Results)
	response.Duration = time.Since(start)

	return response, nil
}

// ValidateWithOptions validates an event with specific options
func (s *Service) ValidateWithOptions(ctx context.Context, req ValidationRequest) (*ValidationResult, error) {
	// Apply options to service configuration temporarily
	originalMode := s.config.ValidationMode
	if req.Options.Mode != "" {
		s.config.ValidationMode = req.Options.Mode
	}
	defer func() {
		s.config.ValidationMode = originalMode
	}()

	return s.ValidateEvent(ctx, req.Event)
}

// GetSchemaInfo returns information about a specific schema
func (s *Service) GetSchemaInfo(schemaName string) (*SchemaInfo, error) {
	schema, exists := s.loader.GetSchema(schemaName)
	if !exists {
		return nil, fmt.Errorf("schema not found: %s", schemaName)
	}

	return &SchemaInfo{
		Name:        schema.Name,
		Version:     schema.Version,
		Description: schema.Description,
		Category:    schema.Category,
		ClassUID:    schema.ClassUID,
		Type:        schema.Type,
		Required:    schema.Required,
		Properties:  len(schema.Properties),
		Metadata:    schema.Metadata,
	}, nil
}

// ListSchemas returns information about all loaded schemas
func (s *Service) ListSchemas() ([]SchemaInfo, error) {
	schemaNames := s.loader.ListSchemas()
	var schemas []SchemaInfo

	for _, name := range schemaNames {
		info, err := s.GetSchemaInfo(name)
		if err != nil {
			s.logger.Error("Failed to get schema info", zap.String("schema", name), zap.Error(err))
			continue
		}
		schemas = append(schemas, *info)
	}

	return schemas, nil
}

// GetValidationStats returns current validation statistics
func (s *Service) GetValidationStats() *ValidationStats {
	s.stats.mutex.RLock()
	defer s.stats.mutex.RUnlock()

	// Create a copy to avoid race conditions
	stats := &ValidationStats{
		TotalValidations:      s.stats.TotalValidations,
		SuccessfulValidations: s.stats.SuccessfulValidations,
		FailedValidations:     s.stats.FailedValidations,
		ValidationsBySchema:   make(map[string]int64),
		LastReset:            s.stats.LastReset,
	}

	for schema, count := range s.stats.ValidationsBySchema {
		stats.ValidationsBySchema[schema] = count
	}

	return stats
}

// ResetStats resets validation statistics
func (s *Service) ResetStats() {
	s.stats.mutex.Lock()
	defer s.stats.mutex.Unlock()

	s.stats.TotalValidations = 0
	s.stats.SuccessfulValidations = 0
	s.stats.FailedValidations = 0
	s.stats.ValidationsBySchema = make(map[string]int64)
	s.stats.LastReset = time.Now()
}

// ReloadSchemas reloads all schemas from disk
func (s *Service) ReloadSchemas() error {
	s.logger.Info("Reloading OCSF schemas")
	
	if err := s.loader.LoadSchemas(); err != nil {
		return fmt.Errorf("failed to reload schemas: %w", err)
	}

	// Clear cache if enabled
	if s.cache != nil {
		s.cache.Flush()
	}

	s.logger.Info("OCSF schemas reloaded successfully")
	return nil
}

// GetCacheStats returns cache statistics if caching is enabled
func (s *Service) GetCacheStats() map[string]interface{} {
	if s.cache == nil {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	return map[string]interface{}{
		"enabled":    true,
		"item_count": s.cache.ItemCount(),
	}
}

// generateCacheKey generates a cache key for an event
func (s *Service) generateCacheKey(event map[string]interface{}) string {
	// Create a deterministic key based on event content
	eventBytes, _ := json.Marshal(event)
	return fmt.Sprintf("ocsf_validation_%x", eventBytes)
}

// updateStats updates validation statistics
func (s *Service) updateStats(schemaName string, valid bool) {
	s.stats.mutex.Lock()
	defer s.stats.mutex.Unlock()

	s.stats.TotalValidations++
	if valid {
		s.stats.SuccessfulValidations++
	} else {
		s.stats.FailedValidations++
	}

	s.stats.ValidationsBySchema[schemaName]++
}

// calculateSummary calculates summary statistics for batch validation
func (s *Service) calculateSummary(results []ValidationResult) ValidationSummary {
	summary := ValidationSummary{
		Total: len(results),
	}

	for _, result := range results {
		if result.Valid {
			summary.Valid++
		} else {
			summary.Invalid++
		}
		summary.Errors += len(result.Errors)
		summary.Warnings += len(result.Warnings)
	}

	return summary
}

// IsEnabled returns whether OCSF validation is enabled
func (s *Service) IsEnabled() bool {
	return s.config.Enabled
}

// GetConfig returns the current OCSF configuration
func (s *Service) GetConfig() config.OCSFSchemasConfig {
	return s.config
}

package ocsf

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// ValidationMiddleware provides OCSF validation middleware for streaming pipeline
type ValidationMiddleware struct {
	service *Service
	logger  *zap.Logger
	config  MiddlewareConfig
}

// MiddlewareConfig configures the validation middleware
type MiddlewareConfig struct {
	Enabled               bool          `mapstructure:"enabled"`
	FailOnValidationError bool          `mapstructure:"fail_on_validation_error"`
	LogValidationErrors   bool          `mapstructure:"log_validation_errors"`
	LogValidationWarnings bool          `mapstructure:"log_validation_warnings"`
	AddValidationMetadata bool          `mapstructure:"add_validation_metadata"`
	ValidationTimeout     time.Duration `mapstructure:"validation_timeout"`
	SkipInvalidEvents     bool          `mapstructure:"skip_invalid_events"`
}

// ProcessingResult represents the result of event processing
type ProcessingResult struct {
	Event              map[string]interface{} `json:"event"`
	ValidationResult   *ValidationResult      `json:"validation_result,omitempty"`
	ProcessingMetadata map[string]interface{} `json:"processing_metadata"`
	ShouldContinue     bool                   `json:"should_continue"`
	Error              error                  `json:"error,omitempty"`
}

// NewValidationMiddleware creates a new OCSF validation middleware
func NewValidationMiddleware(service *Service, config MiddlewareConfig, logger *zap.Logger) *ValidationMiddleware {
	return &ValidationMiddleware{
		service: service,
		logger:  logger,
		config:  config,
	}
}

// ProcessEvent processes a single event through OCSF validation
func (vm *ValidationMiddleware) ProcessEvent(ctx context.Context, event map[string]interface{}) (*ProcessingResult, error) {
	result := &ProcessingResult{
		Event:              event,
		ProcessingMetadata: make(map[string]interface{}),
		ShouldContinue:     true,
	}

	if !vm.config.Enabled || !vm.service.IsEnabled() {
		result.ProcessingMetadata["ocsf_validation"] = "disabled"
		return result, nil
	}

	// Apply validation timeout if configured
	if vm.config.ValidationTimeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, vm.config.ValidationTimeout)
		defer cancel()
	}

	// Perform OCSF validation
	validationResult, err := vm.service.ValidateEvent(ctx, event)
	if err != nil {
		vm.logger.Error("OCSF validation failed", zap.Error(err))

		if vm.config.FailOnValidationError {
			result.Error = fmt.Errorf("OCSF validation failed: %w", err)
			result.ShouldContinue = false
			return result, err
		}

		result.ProcessingMetadata["ocsf_validation_error"] = err.Error()
		return result, nil
	}

	result.ValidationResult = validationResult

	// Handle validation results
	if !validationResult.Valid {
		vm.handleValidationFailure(event, validationResult, result)
	} else {
		vm.handleValidationSuccess(event, validationResult, result)
	}

	// Add validation metadata to event if configured
	if vm.config.AddValidationMetadata {
		vm.addValidationMetadata(event, validationResult)
	}

	return result, nil
}

// ProcessBatch processes a batch of events through OCSF validation
func (vm *ValidationMiddleware) ProcessBatch(ctx context.Context, events []map[string]interface{}) ([]*ProcessingResult, error) {
	if !vm.config.Enabled || !vm.service.IsEnabled() {
		// Return all events as-is when validation is disabled
		results := make([]*ProcessingResult, len(events))
		for i, event := range events {
			results[i] = &ProcessingResult{
				Event:              event,
				ProcessingMetadata: map[string]interface{}{"ocsf_validation": "disabled"},
				ShouldContinue:     true,
			}
		}
		return results, nil
	}

	// Apply validation timeout if configured
	if vm.config.ValidationTimeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, vm.config.ValidationTimeout)
		defer cancel()
	}

	// Perform batch validation
	batchResponse, err := vm.service.ValidateBatch(ctx, events)
	if err != nil {
		return nil, fmt.Errorf("batch OCSF validation failed: %w", err)
	}

	// Process results
	results := make([]*ProcessingResult, len(events))
	for i, event := range events {
		result := &ProcessingResult{
			Event:              event,
			ProcessingMetadata: make(map[string]interface{}),
			ShouldContinue:     true,
		}

		if i < len(batchResponse.Results) {
			validationResult := &batchResponse.Results[i]
			result.ValidationResult = validationResult

			if !validationResult.Valid {
				vm.handleValidationFailure(event, validationResult, result)
			} else {
				vm.handleValidationSuccess(event, validationResult, result)
			}

			// Add validation metadata if configured
			if vm.config.AddValidationMetadata {
				vm.addValidationMetadata(event, validationResult)
			}
		}

		results[i] = result
	}

	return results, nil
}

// handleValidationFailure handles validation failure
func (vm *ValidationMiddleware) handleValidationFailure(event map[string]interface{}, validationResult *ValidationResult, result *ProcessingResult) {
	if vm.config.LogValidationErrors {
		vm.logger.Warn("OCSF validation failed",
			zap.String("schema", validationResult.SchemaName),
			zap.Int("error_count", len(validationResult.Errors)),
			zap.Any("errors", validationResult.Errors))
	}

	result.ProcessingMetadata["ocsf_validation"] = "failed"
	result.ProcessingMetadata["ocsf_schema"] = validationResult.SchemaName
	result.ProcessingMetadata["ocsf_error_count"] = len(validationResult.Errors)

	if vm.config.SkipInvalidEvents {
		result.ShouldContinue = false
		vm.logger.Debug("Skipping invalid OCSF event", zap.String("schema", validationResult.SchemaName))
	}
}

// handleValidationSuccess handles validation success
func (vm *ValidationMiddleware) handleValidationSuccess(event map[string]interface{}, validationResult *ValidationResult, result *ProcessingResult) {
	if vm.config.LogValidationWarnings && len(validationResult.Warnings) > 0 {
		vm.logger.Debug("OCSF validation succeeded with warnings",
			zap.String("schema", validationResult.SchemaName),
			zap.Int("warning_count", len(validationResult.Warnings)),
			zap.Any("warnings", validationResult.Warnings))
	}

	result.ProcessingMetadata["ocsf_validation"] = "passed"
	result.ProcessingMetadata["ocsf_schema"] = validationResult.SchemaName
	result.ProcessingMetadata["ocsf_class_uid"] = validationResult.ClassUID

	if len(validationResult.Warnings) > 0 {
		result.ProcessingMetadata["ocsf_warning_count"] = len(validationResult.Warnings)
	}
}

// addValidationMetadata adds validation metadata to the event
func (vm *ValidationMiddleware) addValidationMetadata(event map[string]interface{}, validationResult *ValidationResult) {
	metadata := map[string]interface{}{
		"ocsf_validation_timestamp": time.Now().Unix(),
		"ocsf_schema_name":          validationResult.SchemaName,
		"ocsf_class_uid":            validationResult.ClassUID,
		"ocsf_validation_valid":     validationResult.Valid,
	}

	if len(validationResult.Errors) > 0 {
		metadata["ocsf_validation_errors"] = validationResult.Errors
	}

	if len(validationResult.Warnings) > 0 {
		metadata["ocsf_validation_warnings"] = validationResult.Warnings
	}

	// Add metadata to event
	if existingMetadata, ok := event["metadata"].(map[string]interface{}); ok {
		for k, v := range metadata {
			existingMetadata[k] = v
		}
	} else {
		event["metadata"] = metadata
	}
}

// ProcessorFunc defines a function type for processing single events
type ProcessorFunc func(ctx context.Context, event map[string]interface{}) (map[string]interface{}, error)

// BatchProcessorFunc defines a function type for processing event batches
type BatchProcessorFunc func(ctx context.Context, events []map[string]interface{}) ([]map[string]interface{}, error)

// StreamingProcessor creates a streaming processor function for OCSF validation
func (vm *ValidationMiddleware) StreamingProcessor() ProcessorFunc {
	return func(ctx context.Context, event map[string]interface{}) (map[string]interface{}, error) {
		result, err := vm.ProcessEvent(ctx, event)
		if err != nil {
			return nil, err
		}

		if !result.ShouldContinue {
			// Return nil to indicate the event should be dropped
			return nil, nil
		}

		return result.Event, nil
	}
}

// BatchStreamingProcessor creates a batch streaming processor function for OCSF validation
func (vm *ValidationMiddleware) BatchStreamingProcessor() BatchProcessorFunc {
	return func(ctx context.Context, events []map[string]interface{}) ([]map[string]interface{}, error) {
		results, err := vm.ProcessBatch(ctx, events)
		if err != nil {
			return nil, err
		}

		var processedEvents []map[string]interface{}
		for _, result := range results {
			if result.ShouldContinue {
				processedEvents = append(processedEvents, result.Event)
			}
		}

		return processedEvents, nil
	}
}

// GetStats returns middleware statistics
func (vm *ValidationMiddleware) GetStats() map[string]interface{} {
	stats := vm.service.GetValidationStats()

	return map[string]interface{}{
		"enabled":                vm.config.Enabled,
		"total_validations":      stats.TotalValidations,
		"successful_validations": stats.SuccessfulValidations,
		"failed_validations":     stats.FailedValidations,
		"validations_by_schema":  stats.ValidationsBySchema,
		"last_reset":             stats.LastReset,
	}
}

// CreateEventFromJSON creates an event from JSON string
func CreateEventFromJSON(jsonStr string) (map[string]interface{}, error) {
	var event map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &event); err != nil {
		return nil, fmt.Errorf("failed to parse JSON event: %w", err)
	}
	return event, nil
}

// CreateEventsFromJSONArray creates events from JSON array string
func CreateEventsFromJSONArray(jsonStr string) ([]map[string]interface{}, error) {
	var events []map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &events); err != nil {
		return nil, fmt.Errorf("failed to parse JSON events array: %w", err)
	}
	return events, nil
}

package ocsf

import (
	"context"
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/xeipuuv/gojsonschema"
	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// SchemaLoader manages OCSF schema loading and validation
type SchemaLoader struct {
	config     config.OCSFSchemasConfig
	logger     *zap.Logger
	schemas    map[string]*Schema
	validators map[string]*gojsonschema.Schema
	mutex      sync.RWMutex
	lastLoad   time.Time
}

// Schema represents an OCSF schema definition
type Schema struct {
	Name        string                 `json:"name"`
	Version     string                 `json:"version"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`
	ClassUID    int                    `json:"class_uid"`
	Type        string                 `json:"type"`
	Properties  map[string]Property    `json:"properties"`
	Required    []string               `json:"required"`
	Metadata    map[string]interface{} `json:"metadata"`
	RawSchema   map[string]interface{} `json:"-"`
}

// Property represents a schema property
type Property struct {
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Required    bool                   `json:"required"`
	Format      string                 `json:"format,omitempty"`
	Enum        []interface{}          `json:"enum,omitempty"`
	Properties  map[string]Property    `json:"properties,omitempty"`
	Items       *Property              `json:"items,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// ValidationResult represents the result of schema validation
type ValidationResult struct {
	Valid      bool                   `json:"valid"`
	Errors     []ValidationError      `json:"errors"`
	Warnings   []ValidationWarning    `json:"warnings"`
	SchemaName string                 `json:"schema_name"`
	ClassUID   int                    `json:"class_uid"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// ValidationError represents a validation error
type ValidationError struct {
	Field      string      `json:"field"`
	Message    string      `json:"message"`
	Value      interface{} `json:"value"`
	SchemaPath string      `json:"schema_path"`
}

// ValidationWarning represents a validation warning
type ValidationWarning struct {
	Field   string      `json:"field"`
	Message string      `json:"message"`
	Value   interface{} `json:"value"`
}

// NewSchemaLoader creates a new OCSF schema loader
func NewSchemaLoader(cfg config.OCSFSchemasConfig, logger *zap.Logger) (*SchemaLoader, error) {
	loader := &SchemaLoader{
		config:     cfg,
		logger:     logger,
		schemas:    make(map[string]*Schema),
		validators: make(map[string]*gojsonschema.Schema),
	}

	if cfg.Enabled {
		if err := loader.LoadSchemas(); err != nil {
			return nil, fmt.Errorf("failed to load OCSF schemas: %w", err)
		}

		// Start refresh goroutine if refresh interval is configured
		if cfg.RefreshInterval > 0 {
			go loader.refreshLoop()
		}
	}

	return loader, nil
}

// LoadSchemas loads all OCSF schemas from the configured path
func (sl *SchemaLoader) LoadSchemas() error {
	sl.mutex.Lock()
	defer sl.mutex.Unlock()

	sl.logger.Info("Loading OCSF schemas", zap.String("path", sl.config.SchemasPath))

	// Clear existing schemas
	sl.schemas = make(map[string]*Schema)
	sl.validators = make(map[string]*gojsonschema.Schema)

	// Walk through schema directory
	err := filepath.WalkDir(sl.config.SchemasPath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if d.IsDir() || !strings.HasSuffix(path, ".json") {
			return nil
		}

		if err := sl.loadSchemaFile(path); err != nil {
			sl.logger.Error("Failed to load schema file",
				zap.String("file", path),
				zap.Error(err))
			return err
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("failed to walk schema directory: %w", err)
	}

	sl.lastLoad = time.Now()
	sl.logger.Info("OCSF schemas loaded successfully",
		zap.Int("count", len(sl.schemas)),
		zap.Time("loaded_at", sl.lastLoad))

	return nil
}

// loadSchemaFile loads a single schema file
func (sl *SchemaLoader) loadSchemaFile(filePath string) error {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read schema file: %w", err)
	}

	var rawSchema map[string]interface{}
	if err := json.Unmarshal(content, &rawSchema); err != nil {
		return fmt.Errorf("failed to parse schema JSON: %w", err)
	}

	schema, err := sl.parseSchema(rawSchema)
	if err != nil {
		return fmt.Errorf("failed to parse schema: %w", err)
	}

	// Create JSON schema validator
	schemaLoader := gojsonschema.NewGoLoader(rawSchema)
	validator, err := gojsonschema.NewSchema(schemaLoader)
	if err != nil {
		return fmt.Errorf("failed to create schema validator: %w", err)
	}

	sl.schemas[schema.Name] = schema
	sl.validators[schema.Name] = validator

	sl.logger.Debug("Loaded schema",
		zap.String("name", schema.Name),
		zap.String("version", schema.Version),
		zap.Int("class_uid", schema.ClassUID))

	return nil
}

// parseSchema parses a raw schema into a Schema struct
func (sl *SchemaLoader) parseSchema(rawSchema map[string]interface{}) (*Schema, error) {
	schema := &Schema{
		RawSchema:  rawSchema,
		Properties: make(map[string]Property),
		Metadata:   make(map[string]interface{}),
	}

	// Extract basic fields
	if name, ok := rawSchema["name"].(string); ok {
		schema.Name = name
	}
	if version, ok := rawSchema["version"].(string); ok {
		schema.Version = version
	}
	if desc, ok := rawSchema["description"].(string); ok {
		schema.Description = desc
	}
	if category, ok := rawSchema["category"].(string); ok {
		schema.Category = category
	}
	if classUID, ok := rawSchema["class_uid"].(float64); ok {
		schema.ClassUID = int(classUID)
	}
	if schemaType, ok := rawSchema["type"].(string); ok {
		schema.Type = schemaType
	}

	// Extract required fields
	if required, ok := rawSchema["required"].([]interface{}); ok {
		for _, req := range required {
			if reqStr, ok := req.(string); ok {
				schema.Required = append(schema.Required, reqStr)
			}
		}
	}

	// Extract properties
	if properties, ok := rawSchema["properties"].(map[string]interface{}); ok {
		for propName, propDef := range properties {
			if propDefMap, ok := propDef.(map[string]interface{}); ok {
				property, err := sl.parseProperty(propDefMap)
				if err != nil {
					return nil, fmt.Errorf("failed to parse property %s: %w", propName, err)
				}
				schema.Properties[propName] = *property
			}
		}
	}

	// Extract metadata
	if metadata, ok := rawSchema["metadata"].(map[string]interface{}); ok {
		schema.Metadata = metadata
	}

	return schema, nil
}

// parseProperty parses a property definition
func (sl *SchemaLoader) parseProperty(propDef map[string]interface{}) (*Property, error) {
	property := &Property{
		Properties: make(map[string]Property),
		Metadata:   make(map[string]interface{}),
	}

	if propType, ok := propDef["type"].(string); ok {
		property.Type = propType
	}
	if desc, ok := propDef["description"].(string); ok {
		property.Description = desc
	}
	if format, ok := propDef["format"].(string); ok {
		property.Format = format
	}
	if required, ok := propDef["required"].(bool); ok {
		property.Required = required
	}

	// Handle enum values
	if enum, ok := propDef["enum"].([]interface{}); ok {
		property.Enum = enum
	}

	// Handle nested properties (for object types)
	if properties, ok := propDef["properties"].(map[string]interface{}); ok {
		for nestedName, nestedDef := range properties {
			if nestedDefMap, ok := nestedDef.(map[string]interface{}); ok {
				nestedProp, err := sl.parseProperty(nestedDefMap)
				if err != nil {
					return nil, fmt.Errorf("failed to parse nested property %s: %w", nestedName, err)
				}
				property.Properties[nestedName] = *nestedProp
			}
		}
	}

	// Handle array items
	if items, ok := propDef["items"].(map[string]interface{}); ok {
		itemProp, err := sl.parseProperty(items)
		if err != nil {
			return nil, fmt.Errorf("failed to parse array items: %w", err)
		}
		property.Items = itemProp
	}

	// Handle metadata
	if metadata, ok := propDef["metadata"].(map[string]interface{}); ok {
		property.Metadata = metadata
	}

	return property, nil
}

// ValidateEvent validates an event against OCSF schemas
func (sl *SchemaLoader) ValidateEvent(ctx context.Context, event map[string]interface{}) (*ValidationResult, error) {
	sl.mutex.RLock()
	defer sl.mutex.RUnlock()

	// Determine schema based on class_uid or activity_id
	schemaName, err := sl.determineSchema(event)
	if err != nil {
		return nil, fmt.Errorf("failed to determine schema: %w", err)
	}

	schema, exists := sl.schemas[schemaName]
	if !exists {
		return &ValidationResult{
			Valid:      false,
			Errors:     []ValidationError{{Message: fmt.Sprintf("schema not found: %s", schemaName)}},
			SchemaName: schemaName,
		}, nil
	}

	validator, exists := sl.validators[schemaName]
	if !exists {
		return &ValidationResult{
			Valid:      false,
			Errors:     []ValidationError{{Message: fmt.Sprintf("validator not found: %s", schemaName)}},
			SchemaName: schemaName,
		}, nil
	}

	// Validate using JSON schema
	documentLoader := gojsonschema.NewGoLoader(event)
	result, err := validator.Validate(documentLoader)
	if err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	validationResult := &ValidationResult{
		Valid:      result.Valid(),
		SchemaName: schemaName,
		ClassUID:   schema.ClassUID,
		Metadata:   schema.Metadata,
	}

	// Convert validation errors
	for _, err := range result.Errors() {
		validationResult.Errors = append(validationResult.Errors, ValidationError{
			Field:      err.Field(),
			Message:    err.Description(),
			Value:      err.Value(),
			SchemaPath: err.Context().String(),
		})
	}

	// Apply validation mode
	switch sl.config.ValidationMode {
	case "strict":
		// All errors are fatal
	case "lenient":
		// Convert some errors to warnings
		validationResult = sl.applyLenientMode(validationResult)
	case "disabled":
		// Always return valid
		validationResult.Valid = true
		validationResult.Errors = nil
	}

	return validationResult, nil
}

// determineSchema determines which schema to use for validation
func (sl *SchemaLoader) determineSchema(event map[string]interface{}) (string, error) {
	// Try to get class_uid first - handle both int and float64 types
	if classUIDValue, exists := event["class_uid"]; exists {
		var classUID int
		switch v := classUIDValue.(type) {
		case float64:
			classUID = int(v)
		case int:
			classUID = v
		case int64:
			classUID = int(v)
		default:
			sl.logger.Debug("class_uid has unexpected type", zap.Any("type", fmt.Sprintf("%T", v)), zap.Any("value", v))
		}

		if classUID > 0 {
			sl.logger.Debug("Looking for schema with class_uid", zap.Int("class_uid", classUID))
			for name, schema := range sl.schemas {
				sl.logger.Debug("Checking schema", zap.String("name", name), zap.Int("schema_class_uid", schema.ClassUID))
				if schema.ClassUID == classUID {
					sl.logger.Debug("Found matching schema", zap.String("name", name))
					return name, nil
				}
			}
			sl.logger.Debug("No matching schema found for class_uid", zap.Int("class_uid", classUID))
		} else {
			sl.logger.Debug("class_uid is not positive", zap.Int("class_uid", classUID))
		}
	} else {
		sl.logger.Debug("No class_uid found in event")
	}

	// Try to get activity_id
	if activityID, ok := event["activity_id"].(float64); ok {
		// Map activity_id to schema name (this would be configurable)
		_ = activityID // TODO: implement activity_id to schema mapping
	}

	// Try to get type_uid
	if typeUID, ok := event["type_uid"].(float64); ok {
		// Map type_uid to schema name
		_ = typeUID // TODO: implement type_uid to schema mapping
	}

	return "", fmt.Errorf("unable to determine schema from event")
}

// applyLenientMode converts some errors to warnings in lenient mode
func (sl *SchemaLoader) applyLenientMode(result *ValidationResult) *ValidationResult {
	var errors []ValidationError
	var warnings []ValidationWarning

	for _, err := range result.Errors {
		// Convert certain types of errors to warnings
		if sl.isLenientError(err) {
			warnings = append(warnings, ValidationWarning{
				Field:   err.Field,
				Message: err.Message,
				Value:   err.Value,
			})
		} else {
			errors = append(errors, err)
		}
	}

	result.Errors = errors
	result.Warnings = warnings
	result.Valid = len(errors) == 0

	return result
}

// isLenientError determines if an error should be converted to a warning in lenient mode
func (sl *SchemaLoader) isLenientError(err ValidationError) bool {
	// Convert missing optional fields to warnings
	if strings.Contains(err.Message, "required") {
		return false // Required fields are always errors
	}

	// Convert format errors to warnings
	if strings.Contains(err.Message, "format") {
		return true
	}

	// Convert additional properties errors to warnings
	if strings.Contains(err.Message, "additional") {
		return true
	}

	return false
}

// GetSchema returns a schema by name
func (sl *SchemaLoader) GetSchema(name string) (*Schema, bool) {
	sl.mutex.RLock()
	defer sl.mutex.RUnlock()

	schema, exists := sl.schemas[name]
	return schema, exists
}

// ListSchemas returns all loaded schema names
func (sl *SchemaLoader) ListSchemas() []string {
	sl.mutex.RLock()
	defer sl.mutex.RUnlock()

	var names []string
	for name := range sl.schemas {
		names = append(names, name)
	}
	return names
}

// refreshLoop periodically refreshes schemas
func (sl *SchemaLoader) refreshLoop() {
	ticker := time.NewTicker(sl.config.RefreshInterval)
	defer ticker.Stop()

	for range ticker.C {
		if err := sl.LoadSchemas(); err != nil {
			sl.logger.Error("Failed to refresh OCSF schemas", zap.Error(err))
		}
	}
}

package batch

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// JobManager manages job lifecycle and persistence
type JobManager struct {
	config    *config.Config
	logger    *zap.Logger
	jobs      map[string]*Job
	jobsMu    sync.RWMutex
	scheduler *Scheduler
	running   bool
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
	stats     *JobManagerStats
}

// JobManagerStats tracks job manager statistics
type JobManagerStats struct {
	ActiveJobs     int64   `json:"active_jobs"`
	QueuedJobs     int64   `json:"queued_jobs"`
	CompletedJobs  int64   `json:"completed_jobs"`
	FailedJobs     int64   `json:"failed_jobs"`
	ProcessingRate float64 `json:"processing_rate"`
	AverageLatency float64 `json:"average_latency"`
	mu             sync.RWMutex
}

// NewJobManager creates a new job manager
func NewJobManager(cfg *config.Config, logger *zap.Logger) (*JobManager, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &JobManager{
		config: cfg,
		logger: logger,
		jobs:   make(map[string]*Job),
		ctx:    ctx,
		cancel: cancel,
		stats:  &JobManagerStats{},
	}, nil
}

// Start starts the job manager
func (jm *JobManager) Start() error {
	if jm.running {
		return fmt.Errorf("job manager is already running")
	}

	jm.running = true
	jm.logger.Info("Starting job manager")

	// Start cleanup worker
	jm.wg.Add(1)
	go jm.cleanupWorker()

	// Start stats collector
	jm.wg.Add(1)
	go jm.statsCollector()

	return nil
}

// Stop stops the job manager
func (jm *JobManager) Stop() error {
	if !jm.running {
		return nil
	}

	jm.logger.Info("Stopping job manager")
	jm.cancel()
	jm.running = false

	// Wait for workers to finish
	jm.wg.Wait()

	return nil
}

// SetScheduler sets the scheduler reference
func (jm *JobManager) SetScheduler(scheduler *Scheduler) {
	jm.scheduler = scheduler
}

// SubmitJob submits a new job
func (jm *JobManager) SubmitJob(job *Job) error {
	if job == nil {
		return fmt.Errorf("job is required")
	}

	// Store job
	jm.jobsMu.Lock()
	jm.jobs[job.ID] = job
	jm.jobsMu.Unlock()

	// Submit to scheduler if available
	if jm.scheduler != nil {
		if err := jm.scheduler.ScheduleJob(job); err != nil {
			return fmt.Errorf("failed to schedule job: %w", err)
		}
	}

	jm.logger.Info("Job submitted",
		zap.String("job_id", job.ID),
		zap.String("job_name", job.Name),
		zap.String("job_type", job.Type),
	)

	return nil
}

// GetJob retrieves a job by ID
func (jm *JobManager) GetJob(jobID string) (*Job, error) {
	jm.jobsMu.RLock()
	defer jm.jobsMu.RUnlock()

	job, exists := jm.jobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job not found: %s", jobID)
	}

	// Return a copy to avoid race conditions
	jobCopy := *job
	return &jobCopy, nil
}

// ListJobs lists jobs with optional filtering
func (jm *JobManager) ListJobs(filter JobFilter) ([]*Job, error) {
	jm.jobsMu.RLock()
	defer jm.jobsMu.RUnlock()

	var filteredJobs []*Job

	for _, job := range jm.jobs {
		if jm.matchesFilter(job, filter) {
			// Add a copy to avoid race conditions
			jobCopy := *job
			filteredJobs = append(filteredJobs, &jobCopy)
		}
	}

	return filteredJobs, nil
}

// CancelJob cancels a job
func (jm *JobManager) CancelJob(jobID string) error {
	jm.jobsMu.Lock()
	defer jm.jobsMu.Unlock()

	job, exists := jm.jobs[jobID]
	if !exists {
		return fmt.Errorf("job not found: %s", jobID)
	}

	if job.Status == "completed" || job.Status == "failed" || job.Status == "cancelled" {
		return fmt.Errorf("cannot cancel job in status: %s", job.Status)
	}

	// Update job status
	job.Status = "cancelled"
	job.CompletedAt = time.Now()
	if !job.StartedAt.IsZero() {
		job.Duration = job.CompletedAt.Sub(job.StartedAt)
	}

	jm.logger.Info("Job cancelled",
		zap.String("job_id", jobID),
		zap.String("previous_status", job.Status),
	)

	return nil
}

// UpdateJobStatus updates a job's status
func (jm *JobManager) UpdateJobStatus(jobID, status string) error {
	jm.jobsMu.Lock()
	defer jm.jobsMu.Unlock()

	job, exists := jm.jobs[jobID]
	if !exists {
		return fmt.Errorf("job not found: %s", jobID)
	}

	oldStatus := job.Status
	job.Status = status

	// Update timestamps based on status
	switch status {
	case "running":
		if job.StartedAt.IsZero() {
			job.StartedAt = time.Now()
		}
	case "completed", "failed", "cancelled":
		if job.CompletedAt.IsZero() {
			job.CompletedAt = time.Now()
		}
		if !job.StartedAt.IsZero() {
			job.Duration = job.CompletedAt.Sub(job.StartedAt)
		}
	}

	jm.logger.Debug("Job status updated",
		zap.String("job_id", jobID),
		zap.String("old_status", oldStatus),
		zap.String("new_status", status),
	)

	return nil
}

// UpdateJobProgress updates a job's progress
func (jm *JobManager) UpdateJobProgress(jobID string, progress JobProgress) error {
	jm.jobsMu.Lock()
	defer jm.jobsMu.Unlock()

	job, exists := jm.jobs[jobID]
	if !exists {
		return fmt.Errorf("job not found: %s", jobID)
	}

	job.Progress = progress

	return nil
}

// GetStats returns job manager statistics
func (jm *JobManager) GetStats() *JobManagerStats {
	jm.stats.mu.RLock()
	defer jm.stats.mu.RUnlock()

	// Create a copy to avoid race conditions
	stats := *jm.stats
	return &stats
}

// matchesFilter checks if a job matches the given filter
func (jm *JobManager) matchesFilter(job *Job, filter JobFilter) bool {
	if filter.Status != "" && job.Status != filter.Status {
		return false
	}

	if filter.Type != "" && job.Type != filter.Type {
		return false
	}

	if filter.Name != "" && job.Name != filter.Name {
		return false
	}

	if !filter.Since.IsZero() && job.CreatedAt.Before(filter.Since) {
		return false
	}

	if !filter.Until.IsZero() && job.CreatedAt.After(filter.Until) {
		return false
	}

	if filter.Priority != 0 && job.Priority != filter.Priority {
		return false
	}

	return true
}

// cleanupWorker periodically cleans up old completed jobs
func (jm *JobManager) cleanupWorker() {
	defer jm.wg.Done()

	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-jm.ctx.Done():
			return
		case <-ticker.C:
			jm.cleanupOldJobs()
		}
	}
}

// cleanupOldJobs removes old completed jobs
func (jm *JobManager) cleanupOldJobs() {
	jm.jobsMu.Lock()
	defer jm.jobsMu.Unlock()

	cutoff := time.Now().Add(-24 * time.Hour) // Keep jobs for 24 hours
	var toDelete []string

	for jobID, job := range jm.jobs {
		if (job.Status == "completed" || job.Status == "failed" || job.Status == "cancelled") &&
			!job.CompletedAt.IsZero() && job.CompletedAt.Before(cutoff) {
			toDelete = append(toDelete, jobID)
		}
	}

	for _, jobID := range toDelete {
		delete(jm.jobs, jobID)
	}

	if len(toDelete) > 0 {
		jm.logger.Info("Cleaned up old jobs",
			zap.Int("count", len(toDelete)),
		)
	}
}

// statsCollector collects job statistics
func (jm *JobManager) statsCollector() {
	defer jm.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-jm.ctx.Done():
			return
		case <-ticker.C:
			jm.collectStats()
		}
	}
}

// collectStats collects and updates statistics
func (jm *JobManager) collectStats() {
	jm.jobsMu.RLock()
	defer jm.jobsMu.RUnlock()

	var activeJobs, queuedJobs, completedJobs, failedJobs int64
	var totalLatency time.Duration
	var latencyCount int64

	for _, job := range jm.jobs {
		switch job.Status {
		case "running":
			activeJobs++
		case "queued":
			queuedJobs++
		case "completed":
			completedJobs++
			if !job.StartedAt.IsZero() && !job.CompletedAt.IsZero() {
				totalLatency += job.CompletedAt.Sub(job.StartedAt)
				latencyCount++
			}
		case "failed":
			failedJobs++
		}
	}

	// Update stats
	jm.stats.mu.Lock()
	jm.stats.ActiveJobs = activeJobs
	jm.stats.QueuedJobs = queuedJobs
	jm.stats.CompletedJobs = completedJobs
	jm.stats.FailedJobs = failedJobs

	if latencyCount > 0 {
		jm.stats.AverageLatency = float64(totalLatency.Nanoseconds()) / float64(latencyCount) / 1e9 // Convert to seconds
	}

	// Calculate processing rate (jobs per minute)
	now := time.Now()
	oneMinuteAgo := now.Add(-1 * time.Minute)
	var recentCompletions int64

	for _, job := range jm.jobs {
		if (job.Status == "completed" || job.Status == "failed") &&
			!job.CompletedAt.IsZero() && job.CompletedAt.After(oneMinuteAgo) {
			recentCompletions++
		}
	}

	jm.stats.ProcessingRate = float64(recentCompletions)
	jm.stats.mu.Unlock()
}

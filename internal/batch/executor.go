package batch

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// Executor manages job execution with worker pools and resource management
type Executor struct {
	config      *config.Config
	logger      *zap.Logger
	workerPool  *WorkerPool
	processors  map[string]JobProcessor
	running     bool
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	metrics     *ExecutorMetrics
	activeJobs  map[string]*Job
	activeJobsMu sync.RWMutex
}

// ExecutorMetrics tracks executor performance
type ExecutorMetrics struct {
	ActiveJobs       int64     `json:"active_jobs"`
	CompletedJobs    int64     `json:"completed_jobs"`
	FailedJobs       int64     `json:"failed_jobs"`
	TotalProcessed   int64     `json:"total_processed"`
	AvgExecutionTime float64   `json:"avg_execution_time"`
	Throughput       float64   `json:"throughput"`
	WorkerUtilization float64  `json:"worker_utilization"`
	LastExecution    time.Time `json:"last_execution"`
	mu               sync.RWMutex
}

// JobProcessor interface for processing different job types
type JobProcessor interface {
	ProcessJob(ctx context.Context, job *Job) error
	GetJobType() string
	ValidateJob(job *Job) error
}

// WorkerPool manages a pool of workers for job execution
type WorkerPool struct {
	capacity    int
	workers     []*Worker
	jobQueue    chan *Job
	resultQueue chan *JobResult
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	logger      *zap.Logger
}

// Worker represents a single worker in the pool
type Worker struct {
	id        int
	processor JobProcessor
	logger    *zap.Logger
}

// NewExecutor creates a new job executor
func NewExecutor(cfg *config.Config, logger *zap.Logger) (*Executor, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	ctx, cancel := context.WithCancel(context.Background())

	// Create worker pool
	capacity := cfg.Batch.MaxWorkers
	if capacity <= 0 {
		capacity = 10 // Default capacity
	}

	workerPool, err := NewWorkerPool(capacity, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create worker pool: %w", err)
	}

	return &Executor{
		config:     cfg,
		logger:     logger,
		workerPool: workerPool,
		processors: make(map[string]JobProcessor),
		ctx:        ctx,
		cancel:     cancel,
		metrics:    &ExecutorMetrics{},
		activeJobs: make(map[string]*Job),
	}, nil
}

// NewWorkerPool creates a new worker pool
func NewWorkerPool(capacity int, logger *zap.Logger) (*WorkerPool, error) {
	if capacity <= 0 {
		return nil, fmt.Errorf("capacity must be positive")
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &WorkerPool{
		capacity:    capacity,
		workers:     make([]*Worker, capacity),
		jobQueue:    make(chan *Job, capacity*2), // Buffer for job queue
		resultQueue: make(chan *JobResult, capacity),
		ctx:         ctx,
		cancel:      cancel,
		logger:      logger,
	}, nil
}

// Start starts the executor
func (e *Executor) Start() error {
	if e.running {
		return fmt.Errorf("executor is already running")
	}

	e.running = true
	e.logger.Info("Starting job executor")

	// Start worker pool
	if err := e.workerPool.Start(); err != nil {
		return fmt.Errorf("failed to start worker pool: %w", err)
	}

	// Start result processor
	e.wg.Add(1)
	go e.resultProcessor()

	// Start metrics collector
	e.wg.Add(1)
	go e.metricsCollector()

	return nil
}

// Stop stops the executor
func (e *Executor) Stop() error {
	if !e.running {
		return nil
	}

	e.logger.Info("Stopping job executor")
	e.cancel()
	e.running = false

	// Stop worker pool
	if err := e.workerPool.Stop(); err != nil {
		e.logger.Error("Failed to stop worker pool", zap.Error(err))
	}

	// Wait for workers to finish
	e.wg.Wait()

	return nil
}

// RegisterProcessor registers a job processor for a specific job type
func (e *Executor) RegisterProcessor(processor JobProcessor) {
	e.processors[processor.GetJobType()] = processor
	e.logger.Info("Registered job processor",
		zap.String("job_type", processor.GetJobType()),
	)
}

// ExecuteJob executes a job
func (e *Executor) ExecuteJob(ctx context.Context, job *Job) error {
	if job == nil {
		return fmt.Errorf("job is required")
	}

	// Find processor for job type
	processor, exists := e.processors[job.Type]
	if !exists {
		return fmt.Errorf("no processor registered for job type: %s", job.Type)
	}

	// Validate job
	if err := processor.ValidateJob(job); err != nil {
		return fmt.Errorf("job validation failed: %w", err)
	}

	// Track active job
	e.activeJobsMu.Lock()
	e.activeJobs[job.ID] = job
	e.activeJobsMu.Unlock()

	// Submit to worker pool
	select {
	case e.workerPool.jobQueue <- job:
		e.logger.Debug("Job submitted to worker pool",
			zap.String("job_id", job.ID),
			zap.String("job_type", job.Type),
		)
		return nil
	case <-ctx.Done():
		// Remove from active jobs if context cancelled
		e.activeJobsMu.Lock()
		delete(e.activeJobs, job.ID)
		e.activeJobsMu.Unlock()
		return ctx.Err()
	}
}

// GetCapacity returns the executor capacity
func (e *Executor) GetCapacity() int {
	return e.workerPool.capacity
}

// GetActiveJobs returns the number of active jobs
func (e *Executor) GetActiveJobs() int {
	e.activeJobsMu.RLock()
	defer e.activeJobsMu.RUnlock()
	return len(e.activeJobs)
}

// GetMetrics returns executor metrics
func (e *Executor) GetMetrics() *ExecutorMetrics {
	e.metrics.mu.RLock()
	defer e.metrics.mu.RUnlock()

	// Create a copy to avoid race conditions
	metrics := *e.metrics
	return &metrics
}

// resultProcessor processes job results
func (e *Executor) resultProcessor() {
	defer e.wg.Done()

	for {
		select {
		case <-e.ctx.Done():
			return
		case result := <-e.workerPool.resultQueue:
			e.processJobResult(result)
		}
	}
}

// processJobResult processes a job result
func (e *Executor) processJobResult(result *JobResult) {
	// Remove from active jobs
	e.activeJobsMu.Lock()
	delete(e.activeJobs, result.JobID)
	e.activeJobsMu.Unlock()

	// Update metrics
	e.metrics.mu.Lock()
	if result.Status == "completed" {
		e.metrics.CompletedJobs++
	} else {
		e.metrics.FailedJobs++
	}
	e.metrics.TotalProcessed++
	e.metrics.LastExecution = time.Now()
	e.metrics.mu.Unlock()

	e.logger.Info("Job result processed",
		zap.String("job_id", result.JobID),
		zap.String("status", result.Status),
		zap.Duration("duration", result.Duration),
	)
}

// metricsCollector collects executor metrics
func (e *Executor) metricsCollector() {
	defer e.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-e.ctx.Done():
			return
		case <-ticker.C:
			e.collectMetrics()
		}
	}
}

// collectMetrics collects and calculates metrics
func (e *Executor) collectMetrics() {
	e.metrics.mu.Lock()
	defer e.metrics.mu.Unlock()

	e.metrics.ActiveJobs = int64(e.GetActiveJobs())
	e.metrics.WorkerUtilization = float64(e.GetActiveJobs()) / float64(e.GetCapacity())
}

// Start starts the worker pool
func (wp *WorkerPool) Start() error {
	wp.logger.Info("Starting worker pool", zap.Int("capacity", wp.capacity))

	// Create and start workers
	for i := 0; i < wp.capacity; i++ {
		worker := &Worker{
			id:     i,
			logger: wp.logger.With(zap.Int("worker_id", i)),
		}
		wp.workers[i] = worker

		wp.wg.Add(1)
		go wp.workerLoop(worker)
	}

	return nil
}

// Stop stops the worker pool
func (wp *WorkerPool) Stop() error {
	wp.logger.Info("Stopping worker pool")
	wp.cancel()

	// Close job queue
	close(wp.jobQueue)

	// Wait for workers to finish
	wp.wg.Wait()

	// Close result queue
	close(wp.resultQueue)

	return nil
}

// workerLoop is the main loop for a worker
func (wp *WorkerPool) workerLoop(worker *Worker) {
	defer wp.wg.Done()

	for {
		select {
		case <-wp.ctx.Done():
			return
		case job, ok := <-wp.jobQueue:
			if !ok {
				return // Job queue closed
			}
			wp.processJob(worker, job)
		}
	}
}

// processJob processes a job with a worker
func (wp *WorkerPool) processJob(worker *Worker, job *Job) {
	startTime := time.Now()
	
	worker.logger.Info("Processing job",
		zap.String("job_id", job.ID),
		zap.String("job_type", job.Type),
	)

	// Create job result
	result := &JobResult{
		JobID:     job.ID,
		StartTime: startTime,
	}

	// Process the job (this would be implemented by specific processors)
	// For now, simulate processing
	time.Sleep(100 * time.Millisecond) // Simulate work

	// Complete the result
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	result.Status = "completed"

	// Send result
	select {
	case wp.resultQueue <- result:
		worker.logger.Debug("Job result sent",
			zap.String("job_id", job.ID),
			zap.Duration("duration", result.Duration),
		)
	case <-wp.ctx.Done():
		return
	}
}

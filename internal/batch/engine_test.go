package batch

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

func TestNewEngine(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Batch: config.BatchConfig{
			Enabled:    true,
			MaxWorkers: 5,
			QueueSize:  50,
		},
	}

	engine, err := NewEngine(cfg, logger)
	require.NoError(t, err)
	assert.NotNil(t, engine)
	assert.Equal(t, cfg, engine.config)
	assert.Equal(t, logger, engine.logger)
	assert.False(t, engine.running)
}

func TestNewEngine_InvalidInputs(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Batch: config.BatchConfig{
			Enabled:    true,
			MaxWorkers: 5,
		},
	}

	// Test nil config
	engine, err := NewEngine(nil, logger)
	assert.Error(t, err)
	assert.Nil(t, engine)
	assert.Contains(t, err.Error(), "config is required")

	// Test nil logger
	engine, err = NewEngine(cfg, nil)
	assert.Error(t, err)
	assert.Nil(t, engine)
	assert.Contains(t, err.Error(), "logger is required")
}

func TestEngine_StartStop(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Batch: config.BatchConfig{
			Enabled:    true,
			MaxWorkers: 2,
			QueueSize:  10,
		},
	}

	engine, err := NewEngine(cfg, logger)
	require.NoError(t, err)

	// Test start
	err = engine.Start()
	assert.NoError(t, err)
	assert.True(t, engine.running)

	// Test start when already running
	err = engine.Start()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already running")

	// Test stop
	err = engine.Stop()
	assert.NoError(t, err)
	assert.False(t, engine.running)

	// Test stop when not running
	err = engine.Stop()
	assert.NoError(t, err)
}

func TestEngine_SubmitJob(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Batch: config.BatchConfig{
			Enabled:    true,
			MaxWorkers: 2,
			QueueSize:  10,
		},
	}

	engine, err := NewEngine(cfg, logger)
	require.NoError(t, err)

	err = engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	// Test valid job submission
	job := &Job{
		Name: "test-job",
		Type: "compaction",
		Config: JobConfig{
			Parallelism: 1,
			BatchSize:   100,
			Timeout:     1 * time.Minute,
		},
		Input: JobInput{
			Type:    "kafka",
			Sources: []string{"input-topic"},
			Format:  "json",
		},
		Output: JobOutput{
			Type:   "kafka",
			Target: "output-topic",
			Format: "json",
		},
	}

	ctx := context.Background()
	result, err := engine.SubmitJob(ctx, job)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.NotEmpty(t, result.JobID)
	assert.Equal(t, "queued", result.Status)

	// Test nil job
	result, err = engine.SubmitJob(ctx, nil)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "job is required")
}

func TestEngine_JobValidation(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Batch: config.BatchConfig{
			Enabled:    true,
			MaxWorkers: 2,
		},
	}

	engine, err := NewEngine(cfg, logger)
	require.NoError(t, err)

	tests := []struct {
		name    string
		job     *Job
		wantErr bool
		errMsg  string
	}{
		{
			name: "missing name",
			job: &Job{
				Type: "compaction",
				Input: JobInput{
					Sources: []string{"input"},
				},
				Output: JobOutput{
					Target: "output",
				},
			},
			wantErr: true,
			errMsg:  "job name is required",
		},
		{
			name: "missing type",
			job: &Job{
				Name: "test-job",
				Input: JobInput{
					Sources: []string{"input"},
				},
				Output: JobOutput{
					Target: "output",
				},
			},
			wantErr: true,
			errMsg:  "job type is required",
		},
		{
			name: "missing input sources",
			job: &Job{
				Name: "test-job",
				Type: "compaction",
				Output: JobOutput{
					Target: "output",
				},
			},
			wantErr: true,
			errMsg:  "at least one input source is required",
		},
		{
			name: "missing output target",
			job: &Job{
				Name: "test-job",
				Type: "compaction",
				Input: JobInput{
					Sources: []string{"input"},
				},
			},
			wantErr: true,
			errMsg:  "output target is required",
		},
		{
			name: "valid job",
			job: &Job{
				Name: "test-job",
				Type: "compaction",
				Input: JobInput{
					Sources: []string{"input"},
				},
				Output: JobOutput{
					Target: "output",
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := engine.validateJob(tt.job)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
				// Check defaults are set
				assert.Equal(t, 1, tt.job.Config.Parallelism)
				assert.Equal(t, 1000, tt.job.Config.BatchSize)
				assert.Equal(t, 1*time.Hour, tt.job.Config.Timeout)
			}
		})
	}
}

func TestEngine_GetJob(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Batch: config.BatchConfig{
			Enabled:    true,
			MaxWorkers: 2,
		},
	}

	engine, err := NewEngine(cfg, logger)
	require.NoError(t, err)

	err = engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	// Submit a job first
	job := &Job{
		Name: "test-job",
		Type: "compaction",
		Input: JobInput{
			Sources: []string{"input"},
		},
		Output: JobOutput{
			Target: "output",
		},
	}

	ctx := context.Background()
	result, err := engine.SubmitJob(ctx, job)
	require.NoError(t, err)

	// Test getting the job
	retrievedJob, err := engine.GetJob(result.JobID)
	assert.NoError(t, err)
	assert.NotNil(t, retrievedJob)
	assert.Equal(t, result.JobID, retrievedJob.ID)
	assert.Equal(t, "test-job", retrievedJob.Name)

	// Test getting non-existent job
	retrievedJob, err = engine.GetJob("non-existent")
	assert.Error(t, err)
	assert.Nil(t, retrievedJob)
}

func TestEngine_ListJobs(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Batch: config.BatchConfig{
			Enabled:    true,
			MaxWorkers: 2,
		},
	}

	engine, err := NewEngine(cfg, logger)
	require.NoError(t, err)

	err = engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	// Submit multiple jobs
	jobs := []*Job{
		{
			Name: "compaction-job",
			Type: "compaction",
			Input: JobInput{Sources: []string{"input1"}},
			Output: JobOutput{Target: "output1"},
		},
		{
			Name: "aggregation-job",
			Type: "aggregation",
			Input: JobInput{Sources: []string{"input2"}},
			Output: JobOutput{Target: "output2"},
		},
	}

	ctx := context.Background()
	for _, job := range jobs {
		_, err := engine.SubmitJob(ctx, job)
		require.NoError(t, err)
	}

	// Test listing all jobs
	allJobs, err := engine.ListJobs(JobFilter{})
	assert.NoError(t, err)
	assert.Len(t, allJobs, 2)

	// Test filtering by type
	compactionJobs, err := engine.ListJobs(JobFilter{Type: "compaction"})
	assert.NoError(t, err)
	assert.Len(t, compactionJobs, 1)
	assert.Equal(t, "compaction-job", compactionJobs[0].Name)

	// Test filtering by status
	queuedJobs, err := engine.ListJobs(JobFilter{Status: "queued"})
	assert.NoError(t, err)
	assert.Len(t, queuedJobs, 2)
}

func TestEngine_CancelJob(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Batch: config.BatchConfig{
			Enabled:    true,
			MaxWorkers: 2,
		},
	}

	engine, err := NewEngine(cfg, logger)
	require.NoError(t, err)

	err = engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	// Submit a job
	job := &Job{
		Name: "test-job",
		Type: "compaction",
		Input: JobInput{Sources: []string{"input"}},
		Output: JobOutput{Target: "output"},
	}

	ctx := context.Background()
	result, err := engine.SubmitJob(ctx, job)
	require.NoError(t, err)

	// Test cancelling the job
	err = engine.CancelJob(result.JobID)
	assert.NoError(t, err)

	// Verify job is cancelled
	cancelledJob, err := engine.GetJob(result.JobID)
	assert.NoError(t, err)
	assert.Equal(t, "cancelled", cancelledJob.Status)

	// Test cancelling non-existent job
	err = engine.CancelJob("non-existent")
	assert.Error(t, err)
}

func TestEngine_GetMetrics(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Batch: config.BatchConfig{
			Enabled:    true,
			MaxWorkers: 2,
		},
	}

	engine, err := NewEngine(cfg, logger)
	require.NoError(t, err)

	metrics := engine.GetMetrics()
	assert.NotNil(t, metrics)
	assert.Equal(t, int64(0), metrics.TotalJobs)
	assert.Equal(t, int64(0), metrics.ActiveJobs)
	assert.Equal(t, int64(0), metrics.CompletedJobs)
	assert.Equal(t, int64(0), metrics.FailedJobs)
}

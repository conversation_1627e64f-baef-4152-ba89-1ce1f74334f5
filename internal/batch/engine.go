package batch

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// Engine provides batch processing capabilities for large-scale data processing
type Engine struct {
	config     *config.Config
	logger     *zap.Logger
	scheduler  *Scheduler
	executor   *Executor
	jobManager *JobManager
	running    bool
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
	metrics    *EngineMetrics
}

// EngineMetrics tracks batch processing engine metrics
type EngineMetrics struct {
	TotalJobs        int64     `json:"total_jobs"`
	CompletedJobs    int64     `json:"completed_jobs"`
	FailedJobs       int64     `json:"failed_jobs"`
	ActiveJobs       int64     `json:"active_jobs"`
	QueuedJobs       int64     `json:"queued_jobs"`
	TotalRecords     int64     `json:"total_records"`
	ProcessingRate   float64   `json:"processing_rate"`
	AverageLatency   float64   `json:"average_latency"`
	LastJobTime      time.Time `json:"last_job_time"`
	LastCompletedJob time.Time `json:"last_completed_job"`
	mu               sync.RWMutex
}

// Job represents a batch processing job
type Job struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`   // compaction, aggregation, enrichment, analysis
	Status      string                 `json:"status"` // queued, running, completed, failed, cancelled
	Priority    int                    `json:"priority"`
	CreatedAt   time.Time              `json:"created_at"`
	StartedAt   time.Time              `json:"started_at"`
	CompletedAt time.Time              `json:"completed_at"`
	Duration    time.Duration          `json:"duration"`
	Config      JobConfig              `json:"config"`
	Input       JobInput               `json:"input"`
	Output      JobOutput              `json:"output"`
	Progress    JobProgress            `json:"progress"`
	Error       string                 `json:"error,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// JobConfig defines configuration for a batch job
type JobConfig struct {
	Parallelism      int                    `json:"parallelism"`
	BatchSize        int                    `json:"batch_size"`
	MemoryLimit      int64                  `json:"memory_limit"`
	Timeout          time.Duration          `json:"timeout"`
	RetryAttempts    int                    `json:"retry_attempts"`
	RetryDelay       time.Duration          `json:"retry_delay"`
	CheckpointPath   string                 `json:"checkpoint_path"`
	Parameters       map[string]interface{} `json:"parameters"`
	ResourceRequests ResourceRequests       `json:"resource_requests"`
}

// ResourceRequests defines resource requirements for a job
type ResourceRequests struct {
	CPU    string `json:"cpu"`
	Memory string `json:"memory"`
	Disk   string `json:"disk"`
}

// JobInput defines input sources for a batch job
type JobInput struct {
	Type       string                 `json:"type"` // kafka, iceberg, cassandra, file
	Sources    []string               `json:"sources"`
	Format     string                 `json:"format"` // json, parquet, avro, csv
	Schema     string                 `json:"schema,omitempty"`
	Filter     string                 `json:"filter,omitempty"`
	TimeRange  *TimeRange             `json:"time_range,omitempty"`
	Parameters map[string]interface{} `json:"parameters,omitempty"`
}

// JobOutput defines output destinations for a batch job
type JobOutput struct {
	Type       string                 `json:"type"` // kafka, iceberg, cassandra, file
	Target     string                 `json:"target"`
	Format     string                 `json:"format"`
	Schema     string                 `json:"schema,omitempty"`
	Partition  []string               `json:"partition,omitempty"`
	Parameters map[string]interface{} `json:"parameters,omitempty"`
}

// TimeRange defines a time range for data processing
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// JobProgress tracks job execution progress
type JobProgress struct {
	TotalRecords     int64     `json:"total_records"`
	ProcessedRecords int64     `json:"processed_records"`
	FailedRecords    int64     `json:"failed_records"`
	PercentComplete  float64   `json:"percent_complete"`
	EstimatedETA     time.Time `json:"estimated_eta"`
	CurrentPhase     string    `json:"current_phase"`
	Throughput       float64   `json:"throughput"` // records per second
}

// JobResult represents the result of a batch job execution
type JobResult struct {
	JobID           string                 `json:"job_id"`
	Status          string                 `json:"status"`
	StartTime       time.Time              `json:"start_time"`
	EndTime         time.Time              `json:"end_time"`
	Duration        time.Duration          `json:"duration"`
	RecordsRead     int64                  `json:"records_read"`
	RecordsWritten  int64                  `json:"records_written"`
	RecordsFailed   int64                  `json:"records_failed"`
	BytesRead       int64                  `json:"bytes_read"`
	BytesWritten    int64                  `json:"bytes_written"`
	Throughput      float64                `json:"throughput"`
	Error           string                 `json:"error,omitempty"`
	OutputLocations []string               `json:"output_locations"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// JobFilter defines filtering criteria for listing jobs
type JobFilter struct {
	Status   string    `json:"status,omitempty"`
	Type     string    `json:"type,omitempty"`
	Name     string    `json:"name,omitempty"`
	Since    time.Time `json:"since,omitempty"`
	Until    time.Time `json:"until,omitempty"`
	Priority int       `json:"priority,omitempty"`
}

// NewEngine creates a new batch processing engine
func NewEngine(cfg *config.Config, logger *zap.Logger) (*Engine, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	ctx, cancel := context.WithCancel(context.Background())

	// Create scheduler
	scheduler, err := NewScheduler(cfg, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create scheduler: %w", err)
	}

	// Create executor
	executor, err := NewExecutor(cfg, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create executor: %w", err)
	}

	// Create job manager
	jobManager, err := NewJobManager(cfg, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create job manager: %w", err)
	}

	return &Engine{
		config:     cfg,
		logger:     logger,
		scheduler:  scheduler,
		executor:   executor,
		jobManager: jobManager,
		ctx:        ctx,
		cancel:     cancel,
		metrics:    &EngineMetrics{},
	}, nil
}

// Start starts the batch processing engine
func (e *Engine) Start() error {
	if e.running {
		return fmt.Errorf("engine is already running")
	}

	e.running = true
	e.logger.Info("Starting batch processing engine")

	// Start components
	if err := e.scheduler.Start(); err != nil {
		return fmt.Errorf("failed to start scheduler: %w", err)
	}

	if err := e.executor.Start(); err != nil {
		return fmt.Errorf("failed to start executor: %w", err)
	}

	if err := e.jobManager.Start(); err != nil {
		return fmt.Errorf("failed to start job manager: %w", err)
	}

	// Start background workers
	e.wg.Add(1)
	go e.metricsCollector()

	e.wg.Add(1)
	go e.jobMonitor()

	return nil
}

// Stop stops the batch processing engine
func (e *Engine) Stop() error {
	if !e.running {
		return nil
	}

	e.logger.Info("Stopping batch processing engine")
	e.cancel()
	e.running = false

	// Stop components
	if err := e.scheduler.Stop(); err != nil {
		e.logger.Error("Failed to stop scheduler", zap.Error(err))
	}

	if err := e.executor.Stop(); err != nil {
		e.logger.Error("Failed to stop executor", zap.Error(err))
	}

	if err := e.jobManager.Stop(); err != nil {
		e.logger.Error("Failed to stop job manager", zap.Error(err))
	}

	// Wait for workers to finish
	e.wg.Wait()

	return nil
}

// SubmitJob submits a new batch job for execution
func (e *Engine) SubmitJob(ctx context.Context, job *Job) (*JobResult, error) {
	if job == nil {
		return nil, fmt.Errorf("job is required")
	}

	// Validate job
	if err := e.validateJob(job); err != nil {
		return nil, fmt.Errorf("job validation failed: %w", err)
	}

	// Set job metadata
	job.ID = e.generateJobID()
	job.Status = "queued"
	job.CreatedAt = time.Now()

	// Submit to job manager
	if err := e.jobManager.SubmitJob(job); err != nil {
		return nil, fmt.Errorf("failed to submit job: %w", err)
	}

	// Update metrics
	e.updateMetrics(job)

	e.logger.Info("Job submitted successfully",
		zap.String("job_id", job.ID),
		zap.String("job_name", job.Name),
		zap.String("job_type", job.Type),
		zap.Int("priority", job.Priority),
	)

	// For synchronous execution, wait for completion
	if ctx.Value("sync") == true {
		return e.waitForJobCompletion(ctx, job.ID)
	}

	// Return immediate result for async execution
	return &JobResult{
		JobID:     job.ID,
		Status:    "queued",
		StartTime: job.CreatedAt,
	}, nil
}

// GetJob retrieves a job by ID
func (e *Engine) GetJob(jobID string) (*Job, error) {
	return e.jobManager.GetJob(jobID)
}

// ListJobs lists all jobs with optional filtering
func (e *Engine) ListJobs(filter JobFilter) ([]*Job, error) {
	return e.jobManager.ListJobs(filter)
}

// CancelJob cancels a running or queued job
func (e *Engine) CancelJob(jobID string) error {
	return e.jobManager.CancelJob(jobID)
}

// GetMetrics returns current engine metrics
func (e *Engine) GetMetrics() *EngineMetrics {
	e.metrics.mu.RLock()
	defer e.metrics.mu.RUnlock()

	// Create a copy to avoid race conditions
	metrics := *e.metrics
	return &metrics
}

// validateJob validates a job configuration
func (e *Engine) validateJob(job *Job) error {
	if job.Name == "" {
		return fmt.Errorf("job name is required")
	}

	if job.Type == "" {
		return fmt.Errorf("job type is required")
	}

	if len(job.Input.Sources) == 0 {
		return fmt.Errorf("at least one input source is required")
	}

	if job.Output.Target == "" {
		return fmt.Errorf("output target is required")
	}

	if job.Config.Parallelism <= 0 {
		job.Config.Parallelism = 1
	}

	if job.Config.BatchSize <= 0 {
		job.Config.BatchSize = 1000
	}

	if job.Config.Timeout == 0 {
		job.Config.Timeout = 1 * time.Hour
	}

	return nil
}

// generateJobID generates a unique job ID
func (e *Engine) generateJobID() string {
	return fmt.Sprintf("job-%d", time.Now().UnixNano())
}

// updateMetrics updates engine metrics
func (e *Engine) updateMetrics(job *Job) {
	e.metrics.mu.Lock()
	defer e.metrics.mu.Unlock()

	e.metrics.TotalJobs++
	e.metrics.QueuedJobs++
	e.metrics.LastJobTime = time.Now()
}

// waitForJobCompletion waits for a job to complete
func (e *Engine) waitForJobCompletion(ctx context.Context, jobID string) (*JobResult, error) {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-ticker.C:
			job, err := e.GetJob(jobID)
			if err != nil {
				return nil, err
			}

			if job.Status == "completed" || job.Status == "failed" || job.Status == "cancelled" {
				return &JobResult{
					JobID:          job.ID,
					Status:         job.Status,
					StartTime:      job.StartedAt,
					EndTime:        job.CompletedAt,
					Duration:       job.Duration,
					RecordsRead:    job.Progress.TotalRecords,
					RecordsWritten: job.Progress.ProcessedRecords,
					RecordsFailed:  job.Progress.FailedRecords,
					Error:          job.Error,
				}, nil
			}
		}
	}
}

// metricsCollector collects and updates metrics
func (e *Engine) metricsCollector() {
	defer e.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-e.ctx.Done():
			return
		case <-ticker.C:
			e.collectMetrics()
		}
	}
}

// collectMetrics collects current metrics from job manager
func (e *Engine) collectMetrics() {
	stats := e.jobManager.GetStats()

	e.metrics.mu.Lock()
	defer e.metrics.mu.Unlock()

	e.metrics.ActiveJobs = stats.ActiveJobs
	e.metrics.QueuedJobs = stats.QueuedJobs
	e.metrics.CompletedJobs = stats.CompletedJobs
	e.metrics.FailedJobs = stats.FailedJobs
	e.metrics.ProcessingRate = stats.ProcessingRate
	e.metrics.AverageLatency = stats.AverageLatency
}

// jobMonitor monitors job execution and handles failures
func (e *Engine) jobMonitor() {
	defer e.wg.Done()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-e.ctx.Done():
			return
		case <-ticker.C:
			e.monitorJobs()
		}
	}
}

// monitorJobs monitors running jobs for timeouts and failures
func (e *Engine) monitorJobs() {
	jobs, err := e.ListJobs(JobFilter{Status: "running"})
	if err != nil {
		e.logger.Error("Failed to list running jobs", zap.Error(err))
		return
	}

	now := time.Now()
	for _, job := range jobs {
		// Check for timeout
		if job.Config.Timeout > 0 && now.Sub(job.StartedAt) > job.Config.Timeout {
			e.logger.Warn("Job timed out, cancelling",
				zap.String("job_id", job.ID),
				zap.Duration("timeout", job.Config.Timeout),
				zap.Duration("runtime", now.Sub(job.StartedAt)),
			)

			if err := e.CancelJob(job.ID); err != nil {
				e.logger.Error("Failed to cancel timed out job",
					zap.String("job_id", job.ID),
					zap.Error(err),
				)
			}
		}
	}
}

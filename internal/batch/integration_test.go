//go:build integration

package batch

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

func TestBatchEngineIntegration(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Batch: config.BatchConfig{
			Enabled:    true,
			MaxWorkers: 4,
			QueueSize:  20,
		},
	}

	engine, err := NewEngine(cfg, logger)
	require.NoError(t, err)

	// Register processors
	compactionProcessor := NewCompactionProcessor(cfg, logger)
	aggregationProcessor := NewAggregationProcessor(cfg, logger)

	engine.executor.RegisterProcessor(compactionProcessor)
	engine.executor.RegisterProcessor(aggregationProcessor)

	// Set up scheduler-executor relationship
	engine.scheduler.SetExecutor(engine.executor)
	engine.jobManager.SetScheduler(engine.scheduler)

	err = engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	t.Run("SingleJobExecution", func(t *testing.T) {
		job := &Job{
			Name:     "integration-compaction-job",
			Type:     "compaction",
			Priority: 5,
			Config: JobConfig{
				Parallelism: 2,
				BatchSize:   500,
				Timeout:     30 * time.Second,
				Parameters: map[string]interface{}{
					"target_file_size": "536870912",
					"min_file_size":    "268435456",
					"max_file_size":    "1073741824",
				},
			},
			Input: JobInput{
				Type:    "iceberg",
				Sources: []string{"test.table1", "test.table2"},
				Format:  "parquet",
			},
			Output: JobOutput{
				Type:   "iceberg",
				Target: "test.compacted_table",
				Format: "parquet",
			},
		}

		ctx := context.WithValue(context.Background(), "sync", true)
		result, err := engine.SubmitJob(ctx, job)
		require.NoError(t, err)
		assert.Equal(t, "completed", result.Status)
		assert.Greater(t, result.Duration, time.Duration(0))
	})

	t.Run("MultipleJobsWithPriority", func(t *testing.T) {
		jobs := []*Job{
			{
				Name:     "high-priority-job",
				Type:     "compaction",
				Priority: 10,
				Config: JobConfig{
					Parallelism: 1,
					BatchSize:   100,
					Timeout:     10 * time.Second,
					Parameters: map[string]interface{}{
						"target_file_size": "536870912",
					},
				},
				Input:  JobInput{Type: "iceberg", Sources: []string{"test.high_priority"}},
				Output: JobOutput{Type: "iceberg", Target: "test.high_priority_out"},
			},
			{
				Name:     "low-priority-job",
				Type:     "aggregation",
				Priority: 1,
				Config: JobConfig{
					Parallelism: 1,
					BatchSize:   100,
					Timeout:     10 * time.Second,
					Parameters: map[string]interface{}{
						"aggregation_functions": []string{"count", "sum"},
						"group_by_fields":       []string{"category"},
					},
				},
				Input:  JobInput{Type: "kafka", Sources: []string{"test.low_priority"}},
				Output: JobOutput{Type: "kafka", Target: "test.low_priority_out"},
			},
			{
				Name:     "medium-priority-job",
				Type:     "compaction",
				Priority: 5,
				Config: JobConfig{
					Parallelism: 1,
					BatchSize:   100,
					Timeout:     10 * time.Second,
					Parameters: map[string]interface{}{
						"target_file_size": "268435456",
					},
				},
				Input:  JobInput{Type: "iceberg", Sources: []string{"test.medium_priority"}},
				Output: JobOutput{Type: "iceberg", Target: "test.medium_priority_out"},
			},
		}

		var results []*JobResult
		var wg sync.WaitGroup

		for _, job := range jobs {
			wg.Add(1)
			go func(j *Job) {
				defer wg.Done()
				ctx := context.WithValue(context.Background(), "sync", true)
				result, err := engine.SubmitJob(ctx, j)
				require.NoError(t, err)
				results = append(results, result)
			}(job)
		}

		wg.Wait()

		// Verify all jobs completed
		assert.Len(t, results, 3)
		for _, result := range results {
			assert.Equal(t, "completed", result.Status)
		}

		// Verify metrics
		metrics := engine.GetMetrics()
		assert.GreaterOrEqual(t, metrics.CompletedJobs, int64(3))
		assert.Equal(t, int64(0), metrics.FailedJobs)
	})

	t.Run("ConcurrentJobSubmission", func(t *testing.T) {
		numJobs := 10
		var wg sync.WaitGroup
		var mu sync.Mutex
		var results []*JobResult

		for i := 0; i < numJobs; i++ {
			wg.Add(1)
			go func(jobIndex int) {
				defer wg.Done()

				job := &Job{
					Name:     fmt.Sprintf("concurrent-job-%d", jobIndex),
					Type:     "compaction",
					Priority: jobIndex % 3, // Vary priorities
					Config: JobConfig{
						Parallelism: 1,
						BatchSize:   100,
						Timeout:     15 * time.Second,
						Parameters: map[string]interface{}{
							"target_file_size": "536870912",
						},
					},
					Input:  JobInput{Type: "iceberg", Sources: []string{fmt.Sprintf("test.table_%d", jobIndex)}},
					Output: JobOutput{Type: "iceberg", Target: fmt.Sprintf("test.output_%d", jobIndex)},
				}

				ctx := context.WithValue(context.Background(), "sync", true)
				result, err := engine.SubmitJob(ctx, job)
				require.NoError(t, err)

				mu.Lock()
				results = append(results, result)
				mu.Unlock()
			}(i)
		}

		wg.Wait()

		// Verify all jobs completed
		assert.Len(t, results, numJobs)
		for _, result := range results {
			assert.Equal(t, "completed", result.Status)
			assert.Greater(t, result.Duration, time.Duration(0))
		}
	})

	t.Run("JobCancellation", func(t *testing.T) {
		// Submit a long-running job
		job := &Job{
			Name:     "long-running-job",
			Type:     "aggregation",
			Priority: 1,
			Config: JobConfig{
				Parallelism: 1,
				BatchSize:   100,
				Timeout:     60 * time.Second,
				Parameters: map[string]interface{}{
					"aggregation_functions": []string{"count", "avg"},
					"group_by_fields":       []string{"category", "region"},
				},
			},
			Input:  JobInput{Type: "kafka", Sources: []string{"test.large_dataset"}},
			Output: JobOutput{Type: "kafka", Target: "test.aggregated_output"},
		}

		ctx := context.Background()
		result, err := engine.SubmitJob(ctx, job)
		require.NoError(t, err)

		// Wait a bit for job to start
		time.Sleep(100 * time.Millisecond)

		// Cancel the job
		err = engine.CancelJob(result.JobID)
		require.NoError(t, err)

		// Verify job is cancelled
		cancelledJob, err := engine.GetJob(result.JobID)
		require.NoError(t, err)
		assert.Equal(t, "cancelled", cancelledJob.Status)
	})

	t.Run("JobTimeout", func(t *testing.T) {
		job := &Job{
			Name:     "timeout-job",
			Type:     "compaction",
			Priority: 1,
			Config: JobConfig{
				Parallelism: 1,
				BatchSize:   100,
				Timeout:     100 * time.Millisecond, // Very short timeout
				Parameters: map[string]interface{}{
					"target_file_size": "536870912",
				},
			},
			Input:  JobInput{Type: "iceberg", Sources: []string{"test.timeout_table"}},
			Output: JobOutput{Type: "iceberg", Target: "test.timeout_output"},
		}

		ctx := context.WithValue(context.Background(), "sync", true)
		result, err := engine.SubmitJob(ctx, job)

		// Job should either complete quickly or timeout
		if err != nil {
			assert.Contains(t, err.Error(), "timeout")
		} else {
			// If it completed, it should be very fast
			assert.Less(t, result.Duration, 1*time.Second)
		}
	})

	t.Run("MetricsAccuracy", func(t *testing.T) {
		initialMetrics := engine.GetMetrics()

		// Submit a few jobs
		numJobs := 3
		for i := 0; i < numJobs; i++ {
			job := &Job{
				Name:     fmt.Sprintf("metrics-test-job-%d", i),
				Type:     "compaction",
				Priority: 1,
				Config: JobConfig{
					Parallelism: 1,
					BatchSize:   100,
					Timeout:     10 * time.Second,
					Parameters: map[string]interface{}{
						"target_file_size": "536870912",
					},
				},
				Input:  JobInput{Type: "iceberg", Sources: []string{fmt.Sprintf("test.metrics_%d", i)}},
				Output: JobOutput{Type: "iceberg", Target: fmt.Sprintf("test.metrics_out_%d", i)},
			}

			ctx := context.WithValue(context.Background(), "sync", true)
			_, err := engine.SubmitJob(ctx, job)
			require.NoError(t, err)
		}

		finalMetrics := engine.GetMetrics()

		// Verify metrics increased
		assert.GreaterOrEqual(t, finalMetrics.TotalJobs, initialMetrics.TotalJobs+int64(numJobs))
		assert.GreaterOrEqual(t, finalMetrics.CompletedJobs, initialMetrics.CompletedJobs+int64(numJobs))
		assert.Greater(t, finalMetrics.ProcessingRate, float64(0))
	})
}

func TestBatchEngineFailureRecovery(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Batch: config.BatchConfig{
			Enabled:    true,
			MaxWorkers: 2,
			QueueSize:  10,
		},
	}

	engine, err := NewEngine(cfg, logger)
	require.NoError(t, err)

	// Register a failing processor for testing
	failingProcessor := &FailingProcessor{}
	engine.executor.RegisterProcessor(failingProcessor)

	engine.scheduler.SetExecutor(engine.executor)
	engine.jobManager.SetScheduler(engine.scheduler)

	err = engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	t.Run("JobFailureHandling", func(t *testing.T) {
		job := &Job{
			Name:     "failing-job",
			Type:     "failing",
			Priority: 1,
			Config: JobConfig{
				Parallelism:   1,
				BatchSize:     100,
				Timeout:       10 * time.Second,
				RetryAttempts: 2,
				RetryDelay:    100 * time.Millisecond,
			},
			Input:  JobInput{Type: "test", Sources: []string{"test.input"}},
			Output: JobOutput{Type: "test", Target: "test.output"},
		}

		ctx := context.WithValue(context.Background(), "sync", true)
		result, err := engine.SubmitJob(ctx, job)

		// Job should fail after retries
		if err != nil {
			assert.Contains(t, err.Error(), "failed")
		} else {
			assert.Equal(t, "failed", result.Status)
		}

		// Verify metrics show failed job
		metrics := engine.GetMetrics()
		assert.Greater(t, metrics.FailedJobs, int64(0))
	})
}

// FailingProcessor is a test processor that always fails
type FailingProcessor struct{}

func (fp *FailingProcessor) GetJobType() string {
	return "failing"
}

func (fp *FailingProcessor) ValidateJob(job *Job) error {
	return nil
}

func (fp *FailingProcessor) ProcessJob(ctx context.Context, job *Job) error {
	return fmt.Errorf("simulated job failure")
}

package batch

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// Scheduler manages job scheduling and prioritization
type Scheduler struct {
	config     *config.Config
	logger     *zap.Logger
	jobQueue   *PriorityQueue
	running    bool
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
	executor   JobExecutor
	metrics    *SchedulerMetrics
}

// SchedulerMetrics tracks scheduler performance
type SchedulerMetrics struct {
	QueuedJobs      int64     `json:"queued_jobs"`
	ScheduledJobs   int64     `json:"scheduled_jobs"`
	AvgWaitTime     float64   `json:"avg_wait_time"`
	AvgScheduleTime float64   `json:"avg_schedule_time"`
	LastScheduled   time.Time `json:"last_scheduled"`
	mu              sync.RWMutex
}

// JobExecutor interface for executing jobs
type JobExecutor interface {
	ExecuteJob(ctx context.Context, job *Job) error
	GetCapacity() int
	GetActiveJobs() int
}

// PriorityQueue implements a priority queue for jobs
type PriorityQueue struct {
	jobs []*Job
	mu   sync.RWMutex
}

// NewScheduler creates a new job scheduler
func NewScheduler(cfg *config.Config, logger *zap.Logger) (*Scheduler, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is required")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger is required")
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &Scheduler{
		config:   cfg,
		logger:   logger,
		jobQueue: NewPriorityQueue(),
		ctx:      ctx,
		cancel:   cancel,
		metrics:  &SchedulerMetrics{},
	}, nil
}

// NewPriorityQueue creates a new priority queue
func NewPriorityQueue() *PriorityQueue {
	return &PriorityQueue{
		jobs: make([]*Job, 0),
	}
}

// Start starts the scheduler
func (s *Scheduler) Start() error {
	if s.running {
		return fmt.Errorf("scheduler is already running")
	}

	s.running = true
	s.logger.Info("Starting job scheduler")

	// Start scheduling loop
	s.wg.Add(1)
	go s.schedulingLoop()

	// Start metrics collector
	s.wg.Add(1)
	go s.metricsCollector()

	return nil
}

// Stop stops the scheduler
func (s *Scheduler) Stop() error {
	if !s.running {
		return nil
	}

	s.logger.Info("Stopping job scheduler")
	s.cancel()
	s.running = false

	// Wait for workers to finish
	s.wg.Wait()

	return nil
}

// SetExecutor sets the job executor
func (s *Scheduler) SetExecutor(executor JobExecutor) {
	s.executor = executor
}

// ScheduleJob adds a job to the scheduling queue
func (s *Scheduler) ScheduleJob(job *Job) error {
	if job == nil {
		return fmt.Errorf("job is required")
	}

	// Set scheduling metadata
	job.Status = "queued"
	if job.CreatedAt.IsZero() {
		job.CreatedAt = time.Now()
	}

	// Add to priority queue
	s.jobQueue.Push(job)

	// Update metrics
	s.updateMetrics()

	s.logger.Info("Job scheduled",
		zap.String("job_id", job.ID),
		zap.String("job_name", job.Name),
		zap.Int("priority", job.Priority),
		zap.Int("queue_size", s.jobQueue.Len()),
	)

	return nil
}

// GetQueueSize returns the current queue size
func (s *Scheduler) GetQueueSize() int {
	return s.jobQueue.Len()
}

// GetMetrics returns scheduler metrics
func (s *Scheduler) GetMetrics() *SchedulerMetrics {
	s.metrics.mu.RLock()
	defer s.metrics.mu.RUnlock()

	// Create a copy to avoid race conditions
	metrics := *s.metrics
	return &metrics
}

// schedulingLoop is the main scheduling loop
func (s *Scheduler) schedulingLoop() {
	defer s.wg.Done()

	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.scheduleNextJobs()
		}
	}
}

// scheduleNextJobs schedules the next available jobs
func (s *Scheduler) scheduleNextJobs() {
	if s.executor == nil {
		return
	}

	// Check executor capacity
	capacity := s.executor.GetCapacity()
	activeJobs := s.executor.GetActiveJobs()
	availableSlots := capacity - activeJobs

	if availableSlots <= 0 {
		return
	}

	// Schedule jobs up to available capacity
	for i := 0; i < availableSlots; i++ {
		job := s.jobQueue.Pop()
		if job == nil {
			break // No more jobs in queue
		}

		// Update job status
		job.Status = "running"
		job.StartedAt = time.Now()

		// Execute job asynchronously
		go func(j *Job) {
			if err := s.executor.ExecuteJob(s.ctx, j); err != nil {
				s.logger.Error("Job execution failed",
					zap.String("job_id", j.ID),
					zap.Error(err),
				)
				j.Status = "failed"
				j.Error = err.Error()
			} else {
				j.Status = "completed"
			}
			j.CompletedAt = time.Now()
			j.Duration = j.CompletedAt.Sub(j.StartedAt)

			s.logger.Info("Job completed",
				zap.String("job_id", j.ID),
				zap.String("status", j.Status),
				zap.Duration("duration", j.Duration),
			)
		}(job)

		// Update metrics
		s.metrics.mu.Lock()
		s.metrics.ScheduledJobs++
		s.metrics.LastScheduled = time.Now()
		s.metrics.mu.Unlock()
	}
}

// updateMetrics updates scheduler metrics
func (s *Scheduler) updateMetrics() {
	s.metrics.mu.Lock()
	defer s.metrics.mu.Unlock()

	s.metrics.QueuedJobs = int64(s.jobQueue.Len())
}

// metricsCollector collects scheduler metrics
func (s *Scheduler) metricsCollector() {
	defer s.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.collectMetrics()
		}
	}
}

// collectMetrics collects and calculates metrics
func (s *Scheduler) collectMetrics() {
	s.metrics.mu.Lock()
	defer s.metrics.mu.Unlock()

	// Calculate average wait time and schedule time
	// This would be implemented based on historical data
	s.metrics.QueuedJobs = int64(s.jobQueue.Len())
}

// Push adds a job to the priority queue
func (pq *PriorityQueue) Push(job *Job) {
	pq.mu.Lock()
	defer pq.mu.Unlock()

	pq.jobs = append(pq.jobs, job)
	
	// Sort by priority (higher priority first) and creation time
	sort.Slice(pq.jobs, func(i, j int) bool {
		if pq.jobs[i].Priority != pq.jobs[j].Priority {
			return pq.jobs[i].Priority > pq.jobs[j].Priority
		}
		return pq.jobs[i].CreatedAt.Before(pq.jobs[j].CreatedAt)
	})
}

// Pop removes and returns the highest priority job
func (pq *PriorityQueue) Pop() *Job {
	pq.mu.Lock()
	defer pq.mu.Unlock()

	if len(pq.jobs) == 0 {
		return nil
	}

	job := pq.jobs[0]
	pq.jobs = pq.jobs[1:]
	return job
}

// Peek returns the highest priority job without removing it
func (pq *PriorityQueue) Peek() *Job {
	pq.mu.RLock()
	defer pq.mu.RUnlock()

	if len(pq.jobs) == 0 {
		return nil
	}

	return pq.jobs[0]
}

// Len returns the number of jobs in the queue
func (pq *PriorityQueue) Len() int {
	pq.mu.RLock()
	defer pq.mu.RUnlock()

	return len(pq.jobs)
}

// Remove removes a job from the queue by ID
func (pq *PriorityQueue) Remove(jobID string) bool {
	pq.mu.Lock()
	defer pq.mu.Unlock()

	for i, job := range pq.jobs {
		if job.ID == jobID {
			pq.jobs = append(pq.jobs[:i], pq.jobs[i+1:]...)
			return true
		}
	}
	return false
}

// List returns all jobs in the queue
func (pq *PriorityQueue) List() []*Job {
	pq.mu.RLock()
	defer pq.mu.RUnlock()

	// Return a copy to avoid race conditions
	jobs := make([]*Job, len(pq.jobs))
	copy(jobs, pq.jobs)
	return jobs
}

package schema

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gocql/gocql"
	"go.uber.org/zap"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/database"
)

// SchemaReconciler manages database schema reconciliation
type SchemaReconciler struct {
	config    config.SchemaReconcilerConfig
	logger    *zap.Logger
	cassandra *database.CassandraClient
	k8sClient kubernetes.Interface
	stopCh    chan struct{}
}

// SchemaDefinition represents a database schema definition
type SchemaDefinition struct {
	Name     string            `yaml:"name"`
	Type     string            `yaml:"type"` // cassandra, opensearch
	Version  string            `yaml:"version"`
	Keyspace string            `yaml:"keyspace,omitempty"`
	Tables   []TableDefinition `yaml:"tables,omitempty"`
	Indices  []IndexDefinition `yaml:"indices,omitempty"`
	Metadata map[string]string `yaml:"metadata,omitempty"`
}

// TableDefinition represents a Cassandra table definition
type TableDefinition struct {
	Name           string             `yaml:"name"`
	Columns        []ColumnDefinition `yaml:"columns"`
	PartitionKeys  []string           `yaml:"partition_keys"`
	ClusteringKeys []string           `yaml:"clustering_keys,omitempty"`
	Options        map[string]string  `yaml:"options,omitempty"`
}

// ColumnDefinition represents a table column
type ColumnDefinition struct {
	Name string `yaml:"name"`
	Type string `yaml:"type"`
}

// IndexDefinition represents an index definition
type IndexDefinition struct {
	Name    string            `yaml:"name"`
	Table   string            `yaml:"table"`
	Columns []string          `yaml:"columns"`
	Options map[string]string `yaml:"options,omitempty"`
}

// ReconciliationResult represents the result of a schema reconciliation
type ReconciliationResult struct {
	SchemaName string        `json:"schema_name"`
	Success    bool          `json:"success"`
	Changes    []string      `json:"changes"`
	Errors     []string      `json:"errors"`
	Duration   time.Duration `json:"duration"`
	Timestamp  time.Time     `json:"timestamp"`
}

// NewSchemaReconciler creates a new schema reconciler
func NewSchemaReconciler(cfg config.SchemaReconcilerConfig, cassandra *database.CassandraClient, logger *zap.Logger) (*SchemaReconciler, error) {
	var k8sClient kubernetes.Interface

	if cfg.Kubernetes.InCluster {
		// Create in-cluster config
		k8sConfig, err := rest.InClusterConfig()
		if err != nil {
			return nil, fmt.Errorf("failed to create in-cluster config: %w", err)
		}
		k8sClient, err = kubernetes.NewForConfig(k8sConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to create kubernetes client: %w", err)
		}
	} else if cfg.Kubernetes.ConfigPath != "" {
		// Create config from file
		k8sConfig, err := clientcmd.BuildConfigFromFlags("", cfg.Kubernetes.ConfigPath)
		if err != nil {
			return nil, fmt.Errorf("failed to create config from file: %w", err)
		}
		k8sClient, err = kubernetes.NewForConfig(k8sConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to create kubernetes client: %w", err)
		}
	}

	return &SchemaReconciler{
		config:    cfg,
		logger:    logger,
		cassandra: cassandra,
		k8sClient: k8sClient,
		stopCh:    make(chan struct{}),
	}, nil
}

// Start begins the schema reconciliation process
func (sr *SchemaReconciler) Start(ctx context.Context) error {
	if !sr.config.Enabled {
		sr.logger.Info("Schema reconciler is disabled")
		return nil
	}

	sr.logger.Info("Starting schema reconciler",
		zap.Duration("interval", sr.config.ReconcileInterval),
		zap.Bool("dry_run", sr.config.DryRun))

	// Initial reconciliation
	if err := sr.reconcileAll(ctx); err != nil {
		sr.logger.Error("Initial schema reconciliation failed", zap.Error(err))
		return err
	}

	// Start periodic reconciliation
	ticker := time.NewTicker(sr.config.ReconcileInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			sr.logger.Info("Schema reconciler stopped")
			return ctx.Err()
		case <-sr.stopCh:
			sr.logger.Info("Schema reconciler stopped")
			return nil
		case <-ticker.C:
			if err := sr.reconcileAll(ctx); err != nil {
				sr.logger.Error("Schema reconciliation failed", zap.Error(err))
			}
		}
	}
}

// Stop stops the schema reconciler
func (sr *SchemaReconciler) Stop() {
	close(sr.stopCh)
}

// reconcileAll performs reconciliation for all schema types
func (sr *SchemaReconciler) reconcileAll(ctx context.Context) error {
	sr.logger.Info("Starting schema reconciliation")
	start := time.Now()

	var allResults []ReconciliationResult

	// Reconcile Cassandra schemas
	if sr.config.Cassandra.SchemaPath != "" {
		results, err := sr.reconcileCassandraSchemas(ctx)
		if err != nil {
			sr.logger.Error("Cassandra schema reconciliation failed", zap.Error(err))
		}
		allResults = append(allResults, results...)
	}

	// Reconcile OpenSearch schemas
	if sr.config.OpenSearch.IndexTemplatesPath != "" {
		results, err := sr.reconcileOpenSearchSchemas(ctx)
		if err != nil {
			sr.logger.Error("OpenSearch schema reconciliation failed", zap.Error(err))
		}
		allResults = append(allResults, results...)
	}

	duration := time.Since(start)
	sr.logger.Info("Schema reconciliation completed",
		zap.Duration("duration", duration),
		zap.Int("total_schemas", len(allResults)))

	return nil
}

// reconcileCassandraSchemas reconciles Cassandra schemas
func (sr *SchemaReconciler) reconcileCassandraSchemas(ctx context.Context) ([]ReconciliationResult, error) {
	schemaFiles, err := sr.findSchemaFiles(sr.config.Cassandra.SchemaPath, "*.cql")
	if err != nil {
		return nil, fmt.Errorf("failed to find Cassandra schema files: %w", err)
	}

	var results []ReconciliationResult

	for _, schemaFile := range schemaFiles {
		result := sr.reconcileCassandraSchema(ctx, schemaFile)
		results = append(results, result)
	}

	return results, nil
}

// reconcileOpenSearchSchemas reconciles OpenSearch index templates
func (sr *SchemaReconciler) reconcileOpenSearchSchemas(ctx context.Context) ([]ReconciliationResult, error) {
	// Implementation for OpenSearch schema reconciliation
	// This would integrate with OpenSearch client to manage index templates
	sr.logger.Info("OpenSearch schema reconciliation not yet implemented")
	return []ReconciliationResult{}, nil
}

// findSchemaFiles finds schema files matching the pattern
func (sr *SchemaReconciler) findSchemaFiles(basePath, pattern string) ([]string, error) {
	var files []string

	err := filepath.Walk(basePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		matched, err := filepath.Match(pattern, info.Name())
		if err != nil {
			return err
		}

		if matched {
			files = append(files, path)
		}

		return nil
	})

	return files, err
}

// reconcileCassandraSchema reconciles a single Cassandra schema file
func (sr *SchemaReconciler) reconcileCassandraSchema(ctx context.Context, schemaFile string) ReconciliationResult {
	start := time.Now()
	result := ReconciliationResult{
		SchemaName: filepath.Base(schemaFile),
		Timestamp:  start,
	}

	sr.logger.Info("Reconciling Cassandra schema", zap.String("file", schemaFile))

	// Read schema file
	content, err := os.ReadFile(schemaFile)
	if err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("failed to read schema file: %v", err))
		result.Duration = time.Since(start)
		return result
	}

	// Parse and execute schema statements
	statements := sr.parseSchemaStatements(string(content))

	for _, stmt := range statements {
		if sr.config.DryRun {
			sr.logger.Info("DRY RUN: Would execute statement", zap.String("statement", stmt))
			result.Changes = append(result.Changes, fmt.Sprintf("DRY RUN: %s", stmt))
		} else {
			if err := sr.executeSchemaStatement(ctx, stmt); err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("failed to execute statement '%s': %v", stmt, err))
			} else {
				result.Changes = append(result.Changes, stmt)
			}
		}
	}

	result.Success = len(result.Errors) == 0
	result.Duration = time.Since(start)

	return result
}

// parseSchemaStatements parses schema statements from content
func (sr *SchemaReconciler) parseSchemaStatements(content string) []string {
	// Split by semicolon and clean up statements
	statements := strings.Split(content, ";")
	var cleanStatements []string

	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		// Skip empty statements and comments
		if stmt == "" || strings.HasPrefix(stmt, "--") || strings.HasPrefix(stmt, "//") {
			continue
		}
		cleanStatements = append(cleanStatements, stmt)
	}

	return cleanStatements
}

// executeSchemaStatement executes a single schema statement
func (sr *SchemaReconciler) executeSchemaStatement(ctx context.Context, statement string) error {
	// Apply timeout to context if configured
	if sr.config.Cassandra.MigrationTimeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, sr.config.Cassandra.MigrationTimeout)
		defer cancel()
	}

	query := sr.cassandra.Session.Query(statement)
	query = query.WithContext(ctx).Consistency(gocql.LocalQuorum)

	return query.Exec()
}

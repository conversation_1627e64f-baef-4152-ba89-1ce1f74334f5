package schema

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/database"
)

// Service provides schema management operations
type Service struct {
	reconciler *SchemaReconciler
	logger     *zap.Logger
	config     config.SchemaReconcilerConfig
}

// SchemaStatus represents the current status of schemas
type SchemaStatus struct {
	LastReconciliation time.Time              `json:"last_reconciliation"`
	TotalSchemas       int                    `json:"total_schemas"`
	SuccessfulSchemas  int                    `json:"successful_schemas"`
	FailedSchemas      int                    `json:"failed_schemas"`
	Results            []ReconciliationResult `json:"results"`
	Health             string                 `json:"health"` // healthy, degraded, unhealthy
}

// SchemaInfo provides information about a specific schema
type SchemaInfo struct {
	Name        string            `json:"name"`
	Type        string            `json:"type"`
	Version     string            `json:"version"`
	LastUpdated time.Time         `json:"last_updated"`
	Status      string            `json:"status"`
	Metadata    map[string]string `json:"metadata"`
}

// NewService creates a new schema management service
func NewService(cfg config.SchemaReconcilerConfig, cassandra *database.CassandraClient, logger *zap.Logger) (*Service, error) {
	reconciler, err := NewSchemaReconciler(cfg, cassandra, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create schema reconciler: %w", err)
	}

	return &Service{
		reconciler: reconciler,
		logger:     logger,
		config:     cfg,
	}, nil
}

// Start starts the schema management service
func (s *Service) Start(ctx context.Context) error {
	s.logger.Info("Starting schema management service")
	return s.reconciler.Start(ctx)
}

// Stop stops the schema management service
func (s *Service) Stop() {
	s.logger.Info("Stopping schema management service")
	s.reconciler.Stop()
}

// GetStatus returns the current status of schema management
func (s *Service) GetStatus(ctx context.Context) (*SchemaStatus, error) {
	// This would typically query a status store or cache
	// For now, return a basic status
	status := &SchemaStatus{
		LastReconciliation: time.Now(), // This should be stored/retrieved
		Health:             "healthy",
	}

	return status, nil
}

// ListSchemas returns information about all managed schemas
func (s *Service) ListSchemas(ctx context.Context) ([]SchemaInfo, error) {
	var schemas []SchemaInfo

	// This would typically query the schema registry or filesystem
	// For now, return basic schema information
	if s.config.Cassandra.SchemaPath != "" {
		schemaFiles, err := s.reconciler.findSchemaFiles(s.config.Cassandra.SchemaPath, "*.cql")
		if err != nil {
			return nil, fmt.Errorf("failed to list Cassandra schemas: %w", err)
		}

		for _, file := range schemaFiles {
			schemas = append(schemas, SchemaInfo{
				Name:        file,
				Type:        "cassandra",
				Version:     "unknown",  // Would be parsed from schema file
				LastUpdated: time.Now(), // Would be retrieved from file stats
				Status:      "active",
			})
		}
	}

	return schemas, nil
}

// ValidateSchema validates a schema definition without applying it
func (s *Service) ValidateSchema(ctx context.Context, schemaContent string, schemaType string) error {
	switch schemaType {
	case "cassandra":
		return s.validateCassandraSchema(ctx, schemaContent)
	case "opensearch":
		return s.validateOpenSearchSchema(ctx, schemaContent)
	default:
		return fmt.Errorf("unsupported schema type: %s", schemaType)
	}
}

// ApplySchema applies a schema definition
func (s *Service) ApplySchema(ctx context.Context, schemaContent string, schemaType string, dryRun bool) (*ReconciliationResult, error) {
	switch schemaType {
	case "cassandra":
		return s.applyCassandraSchema(ctx, schemaContent, dryRun)
	case "opensearch":
		return s.applyOpenSearchSchema(ctx, schemaContent, dryRun)
	default:
		return nil, fmt.Errorf("unsupported schema type: %s", schemaType)
	}
}

// BackupSchema creates a backup of the current schema
func (s *Service) BackupSchema(ctx context.Context, schemaName string) error {
	if !s.config.BackupEnabled {
		return fmt.Errorf("schema backup is disabled")
	}

	s.logger.Info("Creating schema backup", zap.String("schema", schemaName))

	// Implementation would depend on the schema type and backup strategy
	// For Cassandra, this might involve exporting schema definitions
	// For OpenSearch, this might involve exporting index templates

	return nil
}

// RestoreSchema restores a schema from backup
func (s *Service) RestoreSchema(ctx context.Context, schemaName string, backupID string) error {
	if !s.config.BackupEnabled {
		return fmt.Errorf("schema backup is disabled")
	}

	s.logger.Info("Restoring schema from backup",
		zap.String("schema", schemaName),
		zap.String("backup_id", backupID))

	// Implementation would restore schema from backup
	return nil
}

// GetSchemaHistory returns the history of changes for a schema
func (s *Service) GetSchemaHistory(ctx context.Context, schemaName string) ([]ReconciliationResult, error) {
	// This would typically query a history store
	// For now, return empty history
	return []ReconciliationResult{}, nil
}

// validateCassandraSchema validates a Cassandra schema
func (s *Service) validateCassandraSchema(ctx context.Context, schemaContent string) error {
	statements := s.reconciler.parseSchemaStatements(schemaContent)

	for _, stmt := range statements {
		// Basic syntax validation - in a real implementation, this would
		// use a CQL parser to validate syntax
		if stmt == "" {
			continue
		}

		// Check for dangerous operations in production
		if !s.config.DryRun {
			if containsDangerousOperation(stmt) {
				return fmt.Errorf("dangerous operation detected in statement: %s", stmt)
			}
		}
	}

	return nil
}

// validateOpenSearchSchema validates an OpenSearch schema
func (s *Service) validateOpenSearchSchema(ctx context.Context, schemaContent string) error {
	// Validate JSON structure for index templates
	var template map[string]interface{}
	if err := json.Unmarshal([]byte(schemaContent), &template); err != nil {
		return fmt.Errorf("invalid JSON schema: %w", err)
	}

	// Additional validation logic would go here
	return nil
}

// applyCassandraSchema applies a Cassandra schema
func (s *Service) applyCassandraSchema(ctx context.Context, schemaContent string, dryRun bool) (*ReconciliationResult, error) {
	start := time.Now()
	result := &ReconciliationResult{
		SchemaName: "inline-schema",
		Timestamp:  start,
	}

	statements := s.reconciler.parseSchemaStatements(schemaContent)

	for _, stmt := range statements {
		if dryRun {
			s.logger.Info("DRY RUN: Would execute statement", zap.String("statement", stmt))
			result.Changes = append(result.Changes, fmt.Sprintf("DRY RUN: %s", stmt))
		} else {
			if err := s.reconciler.executeSchemaStatement(ctx, stmt); err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("failed to execute statement '%s': %v", stmt, err))
			} else {
				result.Changes = append(result.Changes, stmt)
			}
		}
	}

	result.Success = len(result.Errors) == 0
	result.Duration = time.Since(start)

	return result, nil
}

// applyOpenSearchSchema applies an OpenSearch schema
func (s *Service) applyOpenSearchSchema(ctx context.Context, schemaContent string, dryRun bool) (*ReconciliationResult, error) {
	start := time.Now()
	result := &ReconciliationResult{
		SchemaName: "inline-opensearch-schema",
		Timestamp:  start,
	}

	if dryRun {
		result.Changes = append(result.Changes, "DRY RUN: Would apply OpenSearch schema")
	} else {
		// Implementation would apply the OpenSearch index template
		result.Errors = append(result.Errors, "OpenSearch schema application not yet implemented")
	}

	result.Success = len(result.Errors) == 0
	result.Duration = time.Since(start)

	return result, nil
}

// containsDangerousOperation checks if a statement contains dangerous operations
func containsDangerousOperation(statement string) bool {
	dangerousOps := []string{
		"DROP KEYSPACE",
		"DROP TABLE",
		"TRUNCATE",
		"DELETE FROM",
	}

	upperStmt := fmt.Sprintf(" %s ", strings.ToUpper(statement))
	for _, op := range dangerousOps {
		if strings.Contains(upperStmt, fmt.Sprintf(" %s ", op)) {
			return true
		}
	}

	return false
}

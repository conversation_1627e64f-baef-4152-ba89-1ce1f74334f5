package archive

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"sync"
	"time"

	"github.com/segmentio/kafka-go"
	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/models"
	"github.com/gollm/core-gollmslake-go/pkg/storage"
)

// Writer handles archiving of processed log entries to object storage
type Writer struct {
	config  *config.Config
	logger  *zap.Logger
	storage storage.Client
	reader  *kafka.Reader
	running bool
	wg      sync.WaitGroup
	ctx     context.Context
	cancel  context.CancelFunc
	buffer  *ArchiveBuffer
	metrics *WriterMetrics
}

// WriterMetrics tracks archiving metrics
type WriterMetrics struct {
	ProcessedCount int64 `json:"processed_count"`
	ArchivedCount  int64 `json:"archived_count"`
	ErrorCount     int64 `json:"error_count"`
	BytesArchived  int64 `json:"bytes_archived"`
	FilesCreated   int64 `json:"files_created"`
	mu             sync.RWMutex
}

// ArchiveBuffer buffers log entries before writing to storage
type ArchiveBuffer struct {
	entries   []*models.ProcessedLogEntry
	maxSize   int
	maxAge    time.Duration
	lastFlush time.Time
	mu        sync.Mutex
}

// NewWriter creates a new archive writer
func NewWriter(cfg *config.Config, logger *zap.Logger) (*Writer, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// Initialize storage client
	storageClient, err := storage.NewClient(cfg.ObjectStorage, logger)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to initialize storage client: %w", err)
	}

	// Initialize Kafka reader
	reader := kafka.NewReader(kafka.ReaderConfig{
		Brokers:     cfg.Kafka.Brokers,
		Topic:       "processed-logs", // Input topic for archiving
		GroupID:     "archive-writer",
		MinBytes:    1,
		MaxBytes:    10e6, // 10MB
		MaxWait:     time.Second,
		StartOffset: kafka.LastOffset,
	})

	// Initialize buffer
	buffer := &ArchiveBuffer{
		entries:   make([]*models.ProcessedLogEntry, 0),
		maxSize:   1000,            // Buffer up to 1000 entries
		maxAge:    5 * time.Minute, // Flush every 5 minutes
		lastFlush: time.Now(),
	}

	return &Writer{
		config:  cfg,
		logger:  logger,
		storage: storageClient,
		reader:  reader,
		ctx:     ctx,
		cancel:  cancel,
		buffer:  buffer,
		metrics: &WriterMetrics{},
	}, nil
}

// Start starts the archive writer
func (w *Writer) Start() error {
	if w.running {
		return fmt.Errorf("writer is already running")
	}

	w.running = true
	w.logger.Info("Starting archive writer")

	// Start processing goroutines
	for i := range 3 { // 3 workers
		w.wg.Add(1)
		go w.worker(i)
	}

	// Start buffer flusher
	w.wg.Add(1)
	go w.bufferFlusher()

	// Start metrics collection
	w.wg.Add(1)
	go w.metricsCollector()

	return nil
}

// Stop stops the archive writer
func (w *Writer) Stop() error {
	if !w.running {
		return nil
	}

	w.logger.Info("Stopping archive writer...")
	w.cancel()
	w.running = false

	// Flush remaining buffer
	if err := w.flushBuffer(); err != nil {
		w.logger.Error("Failed to flush buffer during shutdown", zap.Error(err))
	}

	// Wait for all workers to finish
	w.wg.Wait()

	// Close connections
	if err := w.reader.Close(); err != nil {
		w.logger.Error("Failed to close reader", zap.Error(err))
	}

	w.logger.Info("Archive writer stopped")
	return nil
}

// worker processes messages
func (w *Writer) worker(workerID int) {
	defer w.wg.Done()

	logger := w.logger.With(zap.Int("worker_id", workerID))
	logger.Info("Starting archive worker")

	for {
		select {
		case <-w.ctx.Done():
			logger.Info("Worker stopping")
			return
		default:
			w.processMessage(logger)
		}
	}
}

// processMessage processes a single message
func (w *Writer) processMessage(logger *zap.Logger) {
	ctx, cancel := context.WithTimeout(w.ctx, 30*time.Second)
	defer cancel()

	// Read message
	msg, err := w.reader.FetchMessage(ctx)
	if err != nil {
		if err != context.DeadlineExceeded {
			logger.Error("Failed to fetch message", zap.Error(err))
			w.incrementErrorCount()
		}
		return
	}

	// Parse processed log entry
	var processedEntry models.ProcessedLogEntry
	if err := json.Unmarshal(msg.Value, &processedEntry); err != nil {
		logger.Error("Failed to unmarshal processed entry", zap.Error(err))
		w.incrementErrorCount()
		w.commitMessage(msg, logger)
		return
	}

	// Add to buffer
	w.addToBuffer(&processedEntry)
	w.incrementProcessedCount()

	// Commit the message
	w.commitMessage(msg, logger)
}

// addToBuffer adds an entry to the archive buffer
func (w *Writer) addToBuffer(entry *models.ProcessedLogEntry) {
	w.buffer.mu.Lock()
	defer w.buffer.mu.Unlock()

	w.buffer.entries = append(w.buffer.entries, entry)

	// Check if buffer should be flushed
	if len(w.buffer.entries) >= w.buffer.maxSize {
		go func() {
			if err := w.flushBuffer(); err != nil {
				w.logger.Error("Failed to flush buffer", zap.Error(err))
			}
		}()
	}
}

// bufferFlusher periodically flushes the buffer
func (w *Writer) bufferFlusher() {
	defer w.wg.Done()

	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-w.ctx.Done():
			return
		case <-ticker.C:
			w.buffer.mu.Lock()
			shouldFlush := time.Since(w.buffer.lastFlush) >= w.buffer.maxAge && len(w.buffer.entries) > 0
			w.buffer.mu.Unlock()

			if shouldFlush {
				if err := w.flushBuffer(); err != nil {
					w.logger.Error("Failed to flush buffer", zap.Error(err))
				}
			}
		}
	}
}

// flushBuffer flushes the current buffer to storage
func (w *Writer) flushBuffer() error {
	w.buffer.mu.Lock()
	defer w.buffer.mu.Unlock()

	if len(w.buffer.entries) == 0 {
		return nil
	}

	entries := make([]*models.ProcessedLogEntry, len(w.buffer.entries))
	copy(entries, w.buffer.entries)
	w.buffer.entries = w.buffer.entries[:0]
	w.buffer.lastFlush = time.Now()

	// Archive entries in background
	go func() {
		if err := w.archiveEntries(entries); err != nil {
			w.logger.Error("Failed to archive entries", zap.Error(err))
			w.incrementErrorCount()
		}
	}()

	return nil
}

// archiveEntries archives a batch of entries to object storage
func (w *Writer) archiveEntries(entries []*models.ProcessedLogEntry) error {
	if len(entries) == 0 {
		return nil
	}

	// Group entries by date and source for efficient storage
	groups := w.groupEntries(entries)

	for key, groupEntries := range groups {
		if err := w.archiveGroup(key, groupEntries); err != nil {
			return fmt.Errorf("failed to archive group %s: %w", key, err)
		}
	}

	return nil
}

// groupEntries groups entries by date and source
func (w *Writer) groupEntries(entries []*models.ProcessedLogEntry) map[string][]*models.ProcessedLogEntry {
	groups := make(map[string][]*models.ProcessedLogEntry)

	for _, entry := range entries {
		// Create key: date/source
		date := entry.Timestamp.Format("2006-01-02")
		key := fmt.Sprintf("%s/%s", date, entry.Source)

		groups[key] = append(groups[key], entry)
	}

	return groups
}

// archiveGroup archives a group of entries
func (w *Writer) archiveGroup(key string, entries []*models.ProcessedLogEntry) error {
	// Create archive file content
	var buffer bytes.Buffer
	gzipWriter := gzip.NewWriter(&buffer)

	for _, entry := range entries {
		entryJSON, err := json.Marshal(entry)
		if err != nil {
			return fmt.Errorf("failed to marshal entry: %w", err)
		}

		if _, err := gzipWriter.Write(entryJSON); err != nil {
			return fmt.Errorf("failed to write to gzip: %w", err)
		}

		if _, err := gzipWriter.Write([]byte("\n")); err != nil {
			return fmt.Errorf("failed to write newline: %w", err)
		}
	}

	if err := gzipWriter.Close(); err != nil {
		return fmt.Errorf("failed to close gzip writer: %w", err)
	}

	// Generate file path
	timestamp := time.Now().Format("20060102-150405")
	fileName := fmt.Sprintf("%s-%s-%d.jsonl.gz", key, timestamp, len(entries))
	filePath := filepath.Join("archives", "logs", fileName)

	// Upload to storage
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	if err := w.storage.Upload(ctx, filePath, bytes.NewReader(buffer.Bytes())); err != nil {
		return fmt.Errorf("failed to upload archive: %w", err)
	}

	// Update metrics
	w.incrementArchivedCount(int64(len(entries)))
	w.incrementBytesArchived(int64(buffer.Len()))
	w.incrementFilesCreated()

	w.logger.Info("Archived entries",
		zap.String("file_path", filePath),
		zap.Int("entry_count", len(entries)),
		zap.Int("file_size", buffer.Len()),
	)

	return nil
}

// commitMessage commits a Kafka message
func (w *Writer) commitMessage(msg kafka.Message, logger *zap.Logger) {
	if err := w.reader.CommitMessages(w.ctx, msg); err != nil {
		logger.Error("Failed to commit message", zap.Error(err))
	}
}

// metricsCollector collects and reports metrics
func (w *Writer) metricsCollector() {
	defer w.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-w.ctx.Done():
			return
		case <-ticker.C:
			w.reportMetrics()
		}
	}
}

// reportMetrics reports current metrics
func (w *Writer) reportMetrics() {
	metrics := w.GetMetrics()
	w.logger.Info("Archive writer metrics",
		zap.Int64("processed_count", metrics.ProcessedCount),
		zap.Int64("archived_count", metrics.ArchivedCount),
		zap.Int64("error_count", metrics.ErrorCount),
		zap.Int64("bytes_archived", metrics.BytesArchived),
		zap.Int64("files_created", metrics.FilesCreated),
	)
}

// Metric increment functions
func (w *Writer) incrementProcessedCount() {
	w.metrics.mu.Lock()
	defer w.metrics.mu.Unlock()
	w.metrics.ProcessedCount++
}

func (w *Writer) incrementArchivedCount(count int64) {
	w.metrics.mu.Lock()
	defer w.metrics.mu.Unlock()
	w.metrics.ArchivedCount += count
}

func (w *Writer) incrementErrorCount() {
	w.metrics.mu.Lock()
	defer w.metrics.mu.Unlock()
	w.metrics.ErrorCount++
}

func (w *Writer) incrementBytesArchived(bytes int64) {
	w.metrics.mu.Lock()
	defer w.metrics.mu.Unlock()
	w.metrics.BytesArchived += bytes
}

func (w *Writer) incrementFilesCreated() {
	w.metrics.mu.Lock()
	defer w.metrics.mu.Unlock()
	w.metrics.FilesCreated++
}

// GetMetrics returns current metrics
func (w *Writer) GetMetrics() *WriterMetrics {
	w.metrics.mu.RLock()
	defer w.metrics.mu.RUnlock()
	return w.metrics
}

// Health returns the health status of the writer
func (w *Writer) Health() error {
	if !w.running {
		return fmt.Errorf("writer is not running")
	}

	// Check storage connectivity
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := w.storage.Health(ctx); err != nil {
		return fmt.Errorf("storage health check failed: %w", err)
	}

	return nil
}

// GetBufferStatus returns the current buffer status
func (w *Writer) GetBufferStatus() map[string]any {
	w.buffer.mu.Lock()
	defer w.buffer.mu.Unlock()

	return map[string]any{
		"entries_count":    len(w.buffer.entries),
		"max_size":         w.buffer.maxSize,
		"max_age_seconds":  w.buffer.maxAge.Seconds(),
		"last_flush":       w.buffer.lastFlush.Format(time.RFC3339),
		"time_since_flush": time.Since(w.buffer.lastFlush).Seconds(),
	}
}

application:
  name: "schema-reconciler"
  version: "1.0.0"
  environment: "dev"

logging:
  level: "info"
  format: "json"
  output_path: "stdout"

cassandra:
  hosts:
    - "localhost"
  port: 9042
  username: "cassandra"
  password: "cassandra"
  keyspace: "gollmslake"
  consistency: "LOCAL_QUORUM"
  timeout: "30s"
  retries: 3

postgres:
  host: "localhost"
  port: 5432
  database: "goll<PERSON>lake"
  username: "postgres"
  password: "postgres"
  ssl_mode: "disable"
  max_conns: 10
  max_idle_conns: 5
  conn_timeout: "30s"

kafka:
  brokers:
    - "localhost:9092"
  security_protocol: "PLAINTEXT"
  producer:
    acks: "all"
    retries: 3
    batch_size: 16384
    linger_ms: "1ms"
    buffer_memory: 33554432
    compression: "snappy"
    max_message_size: 1048576
  consumer:
    group_id: "schema-reconciler"
    auto_offset_reset: "earliest"
    enable_auto_commit: true
    auto_commit_interval: "1s"
    session_timeout: "30s"
    heartbeat_interval: "3s"
    max_poll_records: 500
    fetch_min_bytes: 1
    fetch_max_wait: "500ms"

object_storage:
  provider: "minio"
  endpoint: "localhost:9000"
  region: "us-east-1"
  access_key_id: "minioadmin"
  secret_access_key: "minioadmin"
  bucket: "gollmslake-schemas"
  use_ssl: false

external_api:
  address: "0.0.0.0"
  grpc_port: 8080
  http_port: 8081
  enable_reflection: true
  tls:
    enabled: false
  rate_limit:
    enabled: false
  auth:
    enabled: false

omnibus:
  enabled: false
  input_topics: []
  active_stages: []
  stages: {}
  transformations_dir: ""
  batching:
    enabled: false
    batch_size: 100
    flush_interval: "1s"
    max_memory_bytes: 10485760
    workers: 2

enrichment:
  enabled: false
  batch_size: 100
  workers: 2
  timeout: "30s"
  retry_attempts: 3
  sources: {}

rules:
  enabled: false
  rules_dir: ""
  buffer_size: 1000
  processing_delay: "100ms"
  ml_models: {}

metrics:
  enabled: true
  address: "0.0.0.0:9090"
  path: "/metrics"

tracing:
  enabled: false
  service_name: "schema-reconciler"
  endpoint: ""
  sample_rate: 0.1

archive:
  enabled: false
  buffer_size: 1000
  flush_interval: "5m"
  compression: true
  retention: "720h"  # 30 days

storage:
  type: "minio"
  bucket: "gollmslake-archive"
  region: "us-east-1"

observation:
  workers: 2
  buffer_size: 1000
  batch_size: 100

# Schema Reconciler Configuration
schema_reconciler:
  enabled: true
  reconcile_interval: "5m"
  dry_run: false
  backup_enabled: true
  
  kubernetes:
    in_cluster: false
    config_path: "~/.kube/config"
    namespace: "data-processing"
    label_selector: "app=gollmslake"
  
  cassandra:
    schema_path: "./schemas/cassandra"
    migration_timeout: "60s"
    backup_keyspace: "gollmslake_backup"
    replication_factor: 3
  
  opensearch:
    index_templates_path: "./schemas/opensearch"
    migration_timeout: "30s"
    backup_indices: true

# OCSF Schema Configuration
ocsf_schemas:
  enabled: true
  schemas_path: "./schemas/ocsf"
  version: "1.3.0"
  cache_enabled: true
  cache_ttl: "1h"
  validation_mode: "strict"
  custom_schemas:
    - "custom_security_event"
    - "custom_network_event"
  refresh_interval: "15m"

# Complex Event Processing Configuration
cep:
  enabled: true
  buffer_size: 10000
  patterns_dir: "./patterns/cep"

  # Event Correlation Settings
  correlation_enabled: true
  correlation_window: "10m"
  max_correlation_groups: 1000
  correlation_threshold: 0.7
  correlation_keys:
    - "src_endpoint.ip"
    - "dst_endpoint.ip"
    - "actor.user.name"
    - "device.hostname"

  # Threat Detection Settings
  threat_detection_enabled: true
  ddos_detection_enabled: true
  brute_force_detection_enabled: true
  anomaly_detection_enabled: true
  threat_update_interval: "5m"

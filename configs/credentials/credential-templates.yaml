# Credential Management Templates
# This file provides templates and best practices for managing credentials securely

# Environment Variable Templates
environment_variables:
  # Database Credentials
  database:
    # Cassandra
    - name: "CASSANDRA_USERNAME"
      description: "Cassandra database username"
      required: true
      sensitive: false
    - name: "CASSANDRA_PASSWORD"
      description: "Cassandra database password"
      required: true
      sensitive: true
    - name: "CA<PERSON><PERSON><PERSON>A_KEYSPACE"
      description: "Cassandra keyspace name"
      required: true
      sensitive: false
      default: "gollmslake_prod"
    
    # PostgreSQL
    - name: "POSTGRES_HOST"
      description: "PostgreSQL host address"
      required: true
      sensitive: false
    - name: "POSTGRES_USERNAME"
      description: "PostgreSQL username"
      required: true
      sensitive: false
    - name: "POSTGRES_PASSWORD"
      description: "PostgreSQL password"
      required: true
      sensitive: true
    - name: "POSTGRES_DATABASE"
      description: "PostgreSQL database name"
      required: true
      sensitive: false
      default: "gollmslake_prod"
  
  # Messaging Credentials
  messaging:
    # Kafka
    - name: "KAFKA_USERNAME"
      description: "Kafka SASL username"
      required: true
      sensitive: false
    - name: "<PERSON>AFKA_PASSWORD"
      description: "Kafka SASL password"
      required: true
      sensitive: true
    - name: "KAFKA_CONSUMER_GROUP"
      description: "Kafka consumer group ID"
      required: true
      sensitive: false
      default: "datalake-prod"
  
  # Cloud Storage Credentials
  storage:
    # AWS
    - name: "AWS_ACCESS_KEY_ID"
      description: "AWS access key ID for S3 access"
      required: true
      sensitive: false
    - name: "AWS_SECRET_ACCESS_KEY"
      description: "AWS secret access key for S3 access"
      required: true
      sensitive: true
    - name: "AWS_REGION"
      description: "AWS region for resources"
      required: true
      sensitive: false
      default: "us-east-1"
    - name: "AWS_ASSUME_ROLE_ARN"
      description: "AWS IAM role ARN to assume"
      required: false
      sensitive: false
    - name: "AWS_EXTERNAL_ID"
      description: "External ID for AWS role assumption"
      required: false
      sensitive: true
  
  # Data Lake Credentials
  data_lake:
    - name: "ICEBERG_CATALOG_URI"
      description: "Iceberg REST catalog endpoint URI"
      required: true
      sensitive: false
    - name: "DATA_LAKE_WAREHOUSE_URI"
      description: "Data lake warehouse S3 URI"
      required: true
      sensitive: false
    - name: "DATA_LAKE_BUCKET"
      description: "S3 bucket name for data lake"
      required: true
      sensitive: false
  
  # Authentication Credentials
  authentication:
    - name: "JWT_SECRET"
      description: "JWT signing secret (256-bit key)"
      required: true
      sensitive: true
      validation: "length >= 32"
    - name: "JWT_ISSUER"
      description: "JWT token issuer"
      required: true
      sensitive: false
    - name: "JWT_AUDIENCE"
      description: "JWT token audience"
      required: true
      sensitive: false
    - name: "JWKS_URL"
      description: "JSON Web Key Set URL"
      required: false
      sensitive: false
  
  # TLS/SSL Certificates
  tls:
    - name: "TLS_CERT_PATH"
      description: "Path to TLS certificate file"
      required: true
      sensitive: false
      default: "/etc/ssl/certs/gollmslake.crt"
    - name: "TLS_KEY_PATH"
      description: "Path to TLS private key file"
      required: true
      sensitive: false
      default: "/etc/ssl/private/gollmslake.key"
    - name: "TLS_CA_PATH"
      description: "Path to TLS CA certificate file"
      required: true
      sensitive: false
      default: "/etc/ssl/certs/ca-bundle.crt"
  
  # Monitoring and Observability
  monitoring:
    - name: "JAEGER_ENDPOINT"
      description: "Jaeger tracing endpoint"
      required: false
      sensitive: false
    - name: "SECURITY_SLACK_WEBHOOK"
      description: "Slack webhook for security alerts"
      required: false
      sensitive: true
    - name: "PAGERDUTY_INTEGRATION_KEY"
      description: "PagerDuty integration key"
      required: false
      sensitive: true

# Kubernetes Secrets Templates
kubernetes_secrets:
  # Database Secrets
  database_secrets:
    apiVersion: v1
    kind: Secret
    metadata:
      name: gollmslake-database-secrets
      namespace: gollmslake-prod
      labels:
        app: gollmslake
        component: database
    type: Opaque
    data:
      # Base64 encoded values (use: echo -n 'value' | base64)
      cassandra-username: # base64 encoded username
      cassandra-password: # base64 encoded password
      postgres-username: # base64 encoded username
      postgres-password: # base64 encoded password
  
  # Messaging Secrets
  messaging_secrets:
    apiVersion: v1
    kind: Secret
    metadata:
      name: gollmslake-messaging-secrets
      namespace: gollmslake-prod
      labels:
        app: gollmslake
        component: messaging
    type: Opaque
    data:
      kafka-username: # base64 encoded username
      kafka-password: # base64 encoded password
  
  # Storage Secrets
  storage_secrets:
    apiVersion: v1
    kind: Secret
    metadata:
      name: gollmslake-storage-secrets
      namespace: gollmslake-prod
      labels:
        app: gollmslake
        component: storage
    type: Opaque
    data:
      aws-access-key-id: # base64 encoded access key
      aws-secret-access-key: # base64 encoded secret key
      aws-external-id: # base64 encoded external ID
  
  # Authentication Secrets
  auth_secrets:
    apiVersion: v1
    kind: Secret
    metadata:
      name: gollmslake-auth-secrets
      namespace: gollmslake-prod
      labels:
        app: gollmslake
        component: auth
    type: Opaque
    data:
      jwt-secret: # base64 encoded JWT secret (256-bit)
      oauth2-client-secret: # base64 encoded OAuth2 client secret
  
  # TLS Secrets
  tls_secrets:
    apiVersion: v1
    kind: Secret
    metadata:
      name: gollmslake-tls-secrets
      namespace: gollmslake-prod
      labels:
        app: gollmslake
        component: tls
    type: kubernetes.io/tls
    data:
      tls.crt: # base64 encoded certificate
      tls.key: # base64 encoded private key
      ca.crt: # base64 encoded CA certificate

# AWS Secrets Manager Templates
aws_secrets_manager:
  # Database Credentials
  database_credentials:
    name: "gollmslake/prod/database"
    description: "Database credentials for production"
    secret_string: |
      {
        "cassandra_username": "prod_cassandra_user",
        "cassandra_password": "CHANGE_ME_STRONG_PASSWORD",
        "postgres_username": "prod_postgres_user",
        "postgres_password": "CHANGE_ME_STRONG_PASSWORD"
      }
    tags:
      - Key: "Environment"
        Value: "production"
      - Key: "Application"
        Value: "gollmslake"
      - Key: "Component"
        Value: "database"
  
  # Messaging Credentials
  messaging_credentials:
    name: "gollmslake/prod/messaging"
    description: "Messaging credentials for production"
    secret_string: |
      {
        "kafka_username": "prod_kafka_user",
        "kafka_password": "CHANGE_ME_STRONG_PASSWORD"
      }
    tags:
      - Key: "Environment"
        Value: "production"
      - Key: "Application"
        Value: "gollmslake"
      - Key: "Component"
        Value: "messaging"
  
  # Authentication Secrets
  auth_secrets:
    name: "gollmslake/prod/auth"
    description: "Authentication secrets for production"
    secret_string: |
      {
        "jwt_secret": "CHANGE_ME_256_BIT_SECRET_KEY_HERE",
        "oauth2_client_secret": "CHANGE_ME_OAUTH2_CLIENT_SECRET"
      }
    tags:
      - Key: "Environment"
        Value: "production"
      - Key: "Application"
        Value: "gollmslake"
      - Key: "Component"
        Value: "auth"

# HashiCorp Vault Templates
vault_secrets:
  # KV v2 Engine Structure
  kv_structure:
    # Database secrets
    secret/gollmslake/prod/database:
      cassandra_username: "prod_cassandra_user"
      cassandra_password: "VAULT_GENERATED_PASSWORD"
      postgres_username: "prod_postgres_user"
      postgres_password: "VAULT_GENERATED_PASSWORD"
    
    # Messaging secrets
    secret/gollmslake/prod/messaging:
      kafka_username: "prod_kafka_user"
      kafka_password: "VAULT_GENERATED_PASSWORD"
    
    # Authentication secrets
    secret/gollmslake/prod/auth:
      jwt_secret: "VAULT_GENERATED_256_BIT_KEY"
      oauth2_client_secret: "VAULT_GENERATED_SECRET"
  
  # Vault Policies
  policies:
    gollmslake_read_policy: |
      # Allow reading gollmslake secrets
      path "secret/data/gollmslake/prod/*" {
        capabilities = ["read"]
      }
      
      path "secret/metadata/gollmslake/prod/*" {
        capabilities = ["list", "read"]
      }
    
    gollmslake_admin_policy: |
      # Allow full access to gollmslake secrets
      path "secret/data/gollmslake/prod/*" {
        capabilities = ["create", "read", "update", "delete", "list"]
      }
      
      path "secret/metadata/gollmslake/prod/*" {
        capabilities = ["create", "read", "update", "delete", "list"]
      }

# Credential Rotation Configuration
credential_rotation:
  # Automatic Rotation Schedule
  rotation_schedule:
    database_passwords:
      enabled: true
      interval: "90d"  # Rotate every 90 days
      notification_days: 7  # Notify 7 days before rotation
    
    api_keys:
      enabled: true
      interval: "30d"  # Rotate every 30 days
      notification_days: 3
    
    certificates:
      enabled: true
      interval: "365d"  # Rotate annually
      notification_days: 30
  
  # Rotation Hooks
  rotation_hooks:
    pre_rotation:
      - "backup_current_credentials"
      - "validate_new_credentials"
    
    post_rotation:
      - "update_application_config"
      - "restart_services"
      - "verify_connectivity"
      - "cleanup_old_credentials"

# Security Best Practices
security_best_practices:
  password_requirements:
    min_length: 16
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_special_chars: true
    forbidden_patterns:
      - "password"
      - "123456"
      - "qwerty"
      - company_name
  
  key_management:
    encryption_at_rest: true
    key_rotation: true
    key_escrow: false
    hardware_security_module: true  # For production
  
  access_control:
    principle_of_least_privilege: true
    multi_factor_authentication: true
    regular_access_reviews: true
    automated_deprovisioning: true

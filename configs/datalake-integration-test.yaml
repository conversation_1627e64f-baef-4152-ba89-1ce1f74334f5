# Data Lake Integration Test Configuration
# This configuration is used for integration testing with TestContainers

# Application Configuration
app:
  name: "core-gollmslake-go-integration-test"
  version: "1.0.0"
  environment: "test"

# Logging Configuration
logging:
  level: "debug"
  format: "json"
  output: "stdout"

# Object Storage Configuration (MinIO for testing)
object_storage:
  provider: "s3"
  endpoint: "http://localhost:9000"
  region: "us-east-1"
  access_key_id: "minioadmin"
  secret_access_key: "minioadmin"
  bucket: "warehouse"
  use_ssl: false

# Storage Configuration
storage:
  type: "s3"
  bucket: "warehouse"
  region: "us-east-1"

# Data Lake Configuration
data_lake:
  catalog:
    type: "rest"
    uri: "http://localhost:8181"
    properties:
      warehouse: "s3://warehouse/"
      io-impl: "org.apache.iceberg.aws.s3.S3FileIO"
      s3.endpoint: "http://localhost:9000"
      s3.access-key-id: "minioadmin"
      s3.secret-access-key: "minioadmin"
      s3.path-style-access: "true"
      s3.region: "us-east-1"

# Test-specific configurations
test:
  # Integration test settings
  integration:
    timeout: "300s"
    container_startup_timeout: "120s"
    health_check_retries: 30
    health_check_delay: "10s"
    
  # Performance test settings
  performance:
    concurrent_workers: 10
    operations_per_worker: 5
    timeout: "600s"
    
  # Chaos test settings
  chaos:
    timeout_duration: "100ms"
    error_injection_rate: 0.1
    recovery_attempts: 5

# Metrics Configuration (for testing)
metrics:
  enabled: true
  port: 9090
  path: "/metrics"

# Health Check Configuration
health:
  enabled: true
  port: 8080
  path: "/health"

# Tracing Configuration (disabled for tests)
tracing:
  enabled: false

# Database configurations (not used in data lake tests but required by config)
database:
  cassandra:
    hosts: ["localhost:9042"]
    keyspace: "test_keyspace"
    consistency: "LOCAL_QUORUM"
    timeout: "30s"
    
  postgres:
    host: "localhost"
    port: 5432
    database: "test_db"
    username: "test_user"
    password: "test_password"
    ssl_mode: "disable"

# Kafka configuration (not used in data lake tests but required by config)
kafka:
  brokers: ["localhost:9092"]
  consumer_group: "test-consumer-group"
  topics:
    input: "test-input-topic"
    output: "test-output-topic"
    error: "test-error-topic"

# External API configuration (minimal for tests)
external_api:
  timeout: "30s"
  retry_attempts: 3
  retry_delay: "1s"

# Enhanced Archival Service Configuration
# This configuration enables comprehensive archival functionality with
# retention policy management, compliance reporting, and lifecycle management

# Basic service configuration
service:
  name: "gollmslake-archival-service"
  version: "1.0.0"
  environment: "production"

# Logging configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"

# Metrics and observability
metrics:
  enabled: true
  port: 9090
  path: "/metrics"

tracing:
  enabled: true
  service_name: "archival-service"
  endpoint: "http://jaeger:14268/api/traces"
  sample_rate: 0.1

# Enhanced Archive Configuration
archive:
  enabled: true
  buffer_size: 10000
  flush_interval: "5m"
  compression: true
  retention: "720h"  # 30 days default
  
  # Policy Manager Configuration
  policy_manager:
    enabled: true
    policy_check_interval: "1h"
    default_retention: "720h"  # 30 days
    policies_path: "/etc/gollmslake/policies"
  
  # Compliance Reporting Configuration
  compliance_report:
    enabled: true
    report_interval: "24h"  # Daily reports
    reports_path: "/var/lib/gollmslake/compliance/reports"
    regulations:
      - "GDPR"
      - "SOX"
      - "HIPAA"
      - "PCI-DSS"
    alert_threshold: 5  # Alert if more than 5 violations
  
  # Lifecycle Manager Configuration
  lifecycle_manager:
    enabled: true
    cleanup_interval: "1h"
    metadata_path: "/var/lib/gollmslake/archival/metadata"
    storage_tiers:
      - "hot"
      - "warm"
      - "cold"
      - "archive"
    compression_enabled: true

# Object Storage Configuration
object_storage:
  type: "s3"  # s3, minio, cos
  bucket: "gollmslake-archive"
  region: "us-east-1"
  endpoint: ""  # Leave empty for AWS S3
  access_key: "${AWS_ACCESS_KEY_ID}"
  secret_key: "${AWS_SECRET_ACCESS_KEY}"
  use_ssl: true
  
  # Advanced storage settings
  multipart_threshold: "64MB"
  multipart_chunk_size: "16MB"
  max_retries: 3
  retry_delay: "1s"

# Storage tier configuration for lifecycle management
storage_tiers:
  hot:
    description: "High-performance storage for recent data"
    cost_per_gb: 0.023
    access_time: "immediate"
    durability: "99.999999999%"
  
  warm:
    description: "Infrequent access storage for older data"
    cost_per_gb: 0.0125
    access_time: "minutes"
    durability: "99.999999999%"
  
  cold:
    description: "Archive storage for long-term retention"
    cost_per_gb: 0.004
    access_time: "hours"
    durability: "99.999999999%"
  
  archive:
    description: "Deep archive for compliance and legal hold"
    cost_per_gb: 0.00099
    access_time: "12-48 hours"
    durability: "99.999999999%"

# Data classification and compliance requirements
data_classifications:
  public:
    description: "Publicly available information"
    encryption_required: false
    retention_min: "30d"
    retention_max: "1y"
    
  internal:
    description: "Internal business information"
    encryption_required: true
    retention_min: "90d"
    retention_max: "7y"
    
  confidential:
    description: "Sensitive business information"
    encryption_required: true
    retention_min: "1y"
    retention_max: "10y"
    audit_required: true
    
  restricted:
    description: "Highly sensitive information"
    encryption_required: true
    retention_min: "7y"
    retention_max: "25y"
    audit_required: true
    immutable: true

# Regulatory compliance settings
compliance_regulations:
  GDPR:
    description: "General Data Protection Regulation"
    max_retention: "6y"
    right_to_erasure: true
    data_portability: true
    audit_trail: true
    
  SOX:
    description: "Sarbanes-Oxley Act"
    min_retention: "7y"
    immutable_storage: true
    audit_trail: true
    
  HIPAA:
    description: "Health Insurance Portability and Accountability Act"
    min_retention: "6y"
    encryption_required: true
    access_logging: true
    
  PCI_DSS:
    description: "Payment Card Industry Data Security Standard"
    min_retention: "1y"
    max_retention: "3y"
    encryption_required: true
    secure_deletion: true

# Default retention policies by data source and type
default_policies:
  # OCSF data policies
  ocsf:
    detection_finding:
      retention: "90d"
      storage_tier: "warm"
      compression: true
      
    network_activity:
      retention: "30d"
      storage_tier: "hot"
      compression: true
      
    system_activity:
      retention: "180d"
      storage_tier: "cold"
      compression: true

  # Security data policies
  security:
    auditlog:
      retention: "2555d"  # 7 years for SOX compliance
      storage_tier: "cold"
      compression: true
      immutable: true
      regulations: ["SOX", "PCI-DSS"]
      
    alerts:
      retention: "365d"
      storage_tier: "warm"
      compression: true
      
    incidents:
      retention: "2555d"  # 7 years
      storage_tier: "cold"
      compression: true
      immutable: true

  # Raw log policies by source
  raw_logs:
    akamai:
      retention: "30d"
      storage_tier: "standard"
      compression: true
      
    crowdstrike:
      retention: "90d"
      storage_tier: "standard"
      compression: true
      regulations: ["SOX"]
      
    office365:
      retention: "365d"
      storage_tier: "cold"
      compression: true
      regulations: ["GDPR", "SOX"]
      
    aws_cloudtrail:
      retention: "2555d"  # 7 years for compliance
      storage_tier: "cold"
      compression: true
      immutable: true
      regulations: ["SOX"]

# Alerting configuration for compliance violations
alerting:
  enabled: true
  
  # Webhook for sending alerts
  webhook:
    url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
    timeout: "30s"
    
  # Email alerts
  email:
    enabled: false
    smtp_host: "smtp.company.com"
    smtp_port: 587
    from: "<EMAIL>"
    to: ["<EMAIL>", "<EMAIL>"]
    
  # Alert rules
  rules:
    - name: "High violation rate"
      condition: "violations_per_hour > 10"
      severity: "critical"
      
    - name: "Policy enforcement failure"
      condition: "policy_enforcement_failures > 5"
      severity: "high"
      
    - name: "Storage quota exceeded"
      condition: "storage_usage_percent > 90"
      severity: "warning"

# Performance and resource limits
performance:
  max_concurrent_uploads: 50
  max_concurrent_downloads: 20
  upload_timeout: "10m"
  download_timeout: "5m"
  
  # Memory limits
  max_memory_usage: "2GB"
  buffer_memory_limit: "512MB"
  
  # Rate limiting
  max_requests_per_second: 1000
  burst_size: 2000

# Health check configuration
health:
  enabled: true
  port: 8080
  path: "/health"
  
  checks:
    - name: "storage"
      interval: "30s"
      timeout: "10s"
      
    - name: "policy_manager"
      interval: "60s"
      timeout: "5s"
      
    - name: "compliance_reporter"
      interval: "300s"
      timeout: "30s"

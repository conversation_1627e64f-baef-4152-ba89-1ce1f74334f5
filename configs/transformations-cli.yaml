# Minimal configuration for transformations CLI
omnibus:
  enabled: true
  input_topics: ["input"]
  active_stages: ["transformation"]
  stages:
    transformation:
      enabled: true
      type: "jslt"
  transformations_dir: "./transformations"
  batching:
    enabled: false
    queue_depth: 100

rules:
  enabled: false
  rules_dir: "./rules"
  workers: 1
  buffer_size: 100

ocsf_schemas:
  enabled: true
  schemas_dir: "./schemas/ocsf"
  validation_mode: "lenient"
  cache_enabled: true
  cache_ttl: "5m"
  cache_size: 1000

kafka:
  brokers: ["localhost:9092"]
  consumer_group: "transformations-cli"
  batch_size: 100
  batch_timeout: "1s"

logging:
  level: "info"
  format: "json"
  output: "stderr"

metrics:
  enabled: false
  address: "localhost:8080"
  path: "/metrics"

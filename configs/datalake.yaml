application:
  name: "core-gollmslake-go"
  version: "1.0.0"
  environment: "dev"

logging:
  level: "info"
  format: "json"
  output_path: "logs/datalake.log"

# Data Lake Integration Configuration
data_lake:
  enabled: true
  catalog:
    type: "rest"  # Options: rest, glue, sql
    uri: "http://localhost:8181"  # REST catalog endpoint
    properties:
      warehouse: "s3://data-lake-warehouse/"
      io-impl: "org.apache.iceberg.aws.s3.S3FileIO"
      s3.endpoint: "http://localhost:9000"  # MinIO endpoint
      s3.access-key-id: "minioadmin"
      s3.secret-access-key: "minioadmin"
      s3.path-style-access: "true"

# Object Storage Configuration (for S3/MinIO)
object_storage:
  provider: "minio"
  endpoint: "localhost:9000"
  region: "us-east-1"
  access_key_id: "minioadmin"
  secret_access_key: "minioadmin"
  bucket: "data-lake-warehouse"
  use_ssl: false

# Cassandra Configuration (required by base config)
cassandra:
  hosts: ["localhost"]
  port: 9042
  username: "cassandra"
  password: "cassandra"
  keyspace: "gollmslake"
  consistency: "QUORUM"
  timeout: "10s"
  retries: 3

# PostgreSQL Configuration (required by base config)
postgres:
  host: "localhost"
  port: 5432
  database: "gollmslake"
  username: "postgres"
  password: "postgres"
  ssl_mode: "disable"
  max_conns: 10
  max_idle_conns: 5
  conn_timeout: "30s"

# Kafka Configuration (required by base config)
kafka:
  brokers: ["localhost:9092"]
  security_protocol: "PLAINTEXT"
  producer:
    acks: "all"
    retries: 3
    batch_size: 16384
    linger_ms: "1ms"
    buffer_memory: 33554432
    compression: "snappy"
    max_message_size: 1048576
  consumer:
    group_id: "datalake-consumer"
    auto_offset_reset: "earliest"
    enable_auto_commit: true
    auto_commit_interval: "1s"
    session_timeout: "30s"
    heartbeat_interval: "3s"
    max_poll_records: 500
    fetch_min_bytes: 1
    fetch_max_wait: "500ms"

# External API Configuration (required by base config)
external_api:
  address: "0.0.0.0"
  grpc_port: 8080
  http_port: 8081
  enable_reflection: true
  rate_limit:
    enabled: false
    rps: 100
    burst: 200
  auth:
    enabled: false

# Omnibus Configuration (required by base config)
omnibus:
  enabled: false
  input_topics: ["raw-logs"]
  active_stages: ["parse"]
  transformations_dir: "transformations"
  stages:
    parse:
      "@type": "parse"
      scripts_dir: "scripts/parse"
  batching:
    enabled: false
    batch_size: 100
    flush_interval: "1s"
    max_memory_bytes: 10485760
    workers: 2
    queue_depth: 100
    retry_attempts: 3
    retry_delay: "100ms"
    enable_dlq: false

# Enrichment Configuration (required by base config)
enrichment:
  enabled: false
  batch_size: 100
  workers: 4
  timeout: "30s"
  retry_attempts: 3

# Rules Configuration (required by base config)
rules:
  enabled: false
  rules_dir: "rules"
  buffer_size: 1000
  workers: 4
  processing_delay: "100ms"

# Metrics Configuration
metrics:
  enabled: true
  address: "0.0.0.0:9090"
  path: "/metrics"

# Tracing Configuration
tracing:
  enabled: false
  service_name: "datalake-service"
  endpoint: "http://localhost:14268/api/traces"
  sample_rate: 0.1

# Archive Configuration (optional)
archive:
  enabled: false
  buffer_size: 1000
  flush_interval: "5m"
  compression: true
  retention: "30d"

# Storage Configuration (optional)
storage:
  type: "s3"
  bucket: "data-lake-warehouse"
  region: "us-east-1"

# Observation Configuration (optional)
observation:
  workers: 2
  buffer_size: 1000
  batch_size: 100

# Schema Reconciler Configuration (optional)
schema_reconciler:
  enabled: false
  reconcile_interval: "1h"
  dry_run: true
  backup_enabled: true
  kubernetes:
    in_cluster: false
    config_path: "~/.kube/config"
    namespace: "default"
    label_selector: "app=gollmslake"
  cassandra:
    schema_path: "schemas/cassandra"
    migration_timeout: "5m"
    backup_keyspace: "gollmslake_backup"
    replication_factor: 1
  opensearch:
    index_templates_path: "schemas/opensearch"
    migration_timeout: "5m"
    backup_indices: true

# OCSF Schemas Configuration (optional)
ocsf_schemas:
  enabled: false
  schemas_path: "schemas/ocsf"
  version: "1.0.0"
  cache_enabled: true
  cache_ttl: "1h"
  validation_mode: "lenient"
  refresh_interval: "24h"

# Batch Processing Configuration (optional)
batch:
  enabled: false
  max_workers: 4
  queue_size: 1000
  job_timeout: "1h"
  retry_attempts: 3
  retry_delay: "30s"
  cleanup_interval: "1h"
  metrics_interval: "1m"
  checkpoint_enabled: true
  checkpoint_dir: "checkpoints"

# Complex Event Processing Configuration (optional)
cep:
  enabled: false
  buffer_size: 10000
  patterns_dir: "patterns"
  correlation_enabled: true
  correlation_window: "5m"
  max_correlation_groups: 1000
  correlation_threshold: 0.8
  correlation_keys: ["src_ip", "user_id", "session_id"]
  threat_detection_enabled: true
  ddos_detection_enabled: true
  brute_force_detection_enabled: true
  anomaly_detection_enabled: true
  threat_update_interval: "1h"

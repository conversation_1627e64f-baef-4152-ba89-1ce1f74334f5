# Production Data Lake Configuration
# This configuration is optimized for production environments with security, performance, and reliability

application:
  name: "core-gollmslake-go"
  version: "${APP_VERSION:-1.0.0}"
  environment: "prod"

logging:
  level: "info"
  format: "json"
  output_path: "/var/log/gollmslake/datalake.log"

# Data Lake Integration Configuration - Production Ready
data_lake:
  enabled: true
  catalog:
    type: "rest"  # Production: Use REST catalog with proper authentication
    uri: "${ICEBERG_CATALOG_URI}"  # e.g., https://iceberg-catalog.company.com
    properties:
      warehouse: "${DATA_LAKE_WAREHOUSE_URI}"  # e.g., s3://company-data-lake/
      io-impl: "org.apache.iceberg.aws.s3.S3FileIO"
      # AWS S3 Configuration
      s3.endpoint: "${S3_ENDPOINT:-https://s3.amazonaws.com}"
      s3.access-key-id: "${AWS_ACCESS_KEY_ID}"
      s3.secret-access-key: "${AWS_SECRET_ACCESS_KEY}"
      s3.region: "${AWS_REGION:-us-east-1}"
      s3.path-style-access: "false"  # Use virtual-hosted-style for AWS S3
      # Security and Performance
      client.assume-role.arn: "${AWS_ASSUME_ROLE_ARN}"
      client.assume-role.region: "${AWS_REGION:-us-east-1}"
      client.assume-role.external-id: "${AWS_EXTERNAL_ID}"
      # Connection pooling and timeouts
      client.connection-timeout-ms: "30000"
      client.socket-timeout-ms: "60000"
      client.max-connections: "200"
      client.max-error-retry: "3"

# Object Storage Configuration - Production S3
object_storage:
  provider: "s3"
  endpoint: "${S3_ENDPOINT:-https://s3.amazonaws.com}"
  region: "${AWS_REGION:-us-east-1}"
  access_key_id: "${AWS_ACCESS_KEY_ID}"
  secret_access_key: "${AWS_SECRET_ACCESS_KEY}"
  bucket: "${DATA_LAKE_BUCKET}"
  use_ssl: true

# Cassandra Configuration - Production Cluster
cassandra:
  hosts: 
    - "${CASSANDRA_HOST_1}"
    - "${CASSANDRA_HOST_2}"
    - "${CASSANDRA_HOST_3}"
  port: 9042
  username: "${CASSANDRA_USERNAME}"
  password: "${CASSANDRA_PASSWORD}"
  keyspace: "${CASSANDRA_KEYSPACE:-gollmslake_prod}"
  consistency: "QUORUM"
  timeout: "30s"
  retries: 5

# PostgreSQL Configuration - Production Database
postgres:
  host: "${POSTGRES_HOST}"
  port: 5432
  database: "${POSTGRES_DATABASE:-gollmslake_prod}"
  username: "${POSTGRES_USERNAME}"
  password: "${POSTGRES_PASSWORD}"
  ssl_mode: "require"  # Production: Always use SSL
  max_conns: 50
  max_idle_conns: 10
  conn_timeout: "30s"

# Kafka Configuration - Production Cluster with Security
kafka:
  brokers: 
    - "${KAFKA_BROKER_1}"
    - "${KAFKA_BROKER_2}"
    - "${KAFKA_BROKER_3}"
  security_protocol: "SASL_SSL"  # Production: Use SASL_SSL
  sasl_mechanism: "SCRAM-SHA-512"
  sasl_username: "${KAFKA_USERNAME}"
  sasl_password: "${KAFKA_PASSWORD}"
  tls:
    enabled: true
    cert_file: "${KAFKA_CLIENT_CERT_PATH}"
    key_file: "${KAFKA_CLIENT_KEY_PATH}"
    ca_file: "${KAFKA_CA_CERT_PATH}"
    insecure_skip_verify: false
  producer:
    acks: "all"  # Production: Wait for all replicas
    retries: 10
    batch_size: 65536  # Larger batch for production throughput
    linger_ms: "10ms"
    buffer_memory: 134217728  # 128MB buffer
    compression: "lz4"  # Better compression for production
    max_message_size: 10485760  # 10MB max message
  consumer:
    group_id: "${KAFKA_CONSUMER_GROUP:-datalake-prod}"
    auto_offset_reset: "earliest"
    enable_auto_commit: false  # Production: Manual commit for reliability
    session_timeout: "30s"
    heartbeat_interval: "10s"
    max_poll_records: 1000
    fetch_min_bytes: 1024
    fetch_max_wait: "1s"

# External API Configuration - Production with Security
external_api:
  address: "0.0.0.0"
  grpc_port: 8080
  http_port: 8081
  enable_reflection: false  # Production: Disable reflection
  tls:
    enabled: true
    cert_file: "${TLS_CERT_PATH}"
    key_file: "${TLS_KEY_PATH}"
    ca_file: "${TLS_CA_PATH}"
    insecure_skip_verify: false
  rate_limit:
    enabled: true
    rps: 1000  # Production rate limit
    burst: 2000
  auth:
    enabled: true
    jwt_secret: "${JWT_SECRET}"
    jwks_url: "${JWKS_URL}"
    issuer: "${JWT_ISSUER}"
    audience: "${JWT_AUDIENCE}"
    access_token_ttl: "1h"
    refresh_token_ttl: "24h"

# Metrics Configuration - Production Monitoring
metrics:
  enabled: true
  address: "0.0.0.0:9090"
  path: "/metrics"

# Tracing Configuration - Production Observability
tracing:
  enabled: true
  service_name: "datalake-service-prod"
  endpoint: "${JAEGER_ENDPOINT}"
  sample_rate: 0.01  # Production: Lower sample rate

# Archive Configuration - Production Retention
archive:
  enabled: true
  buffer_size: 10000
  flush_interval: "1m"
  compression: true
  retention: "2555d"  # 7 years for compliance
  policy_manager:
    enabled: true
    policy_check_interval: "1h"
    default_retention: "2555d"
    policies_path: "/etc/gollmslake/policies"
  compliance_report:
    enabled: true
    report_interval: "24h"
    reports_path: "/var/log/gollmslake/compliance"
    regulations: ["GDPR", "SOX", "HIPAA"]
    alert_threshold: 95
  lifecycle_manager:
    enabled: true
    cleanup_interval: "6h"
    metadata_path: "/var/lib/gollmslake/metadata"
    storage_tiers: ["hot", "warm", "cold", "archive"]
    compression_enabled: true

# Storage Configuration - Production S3
storage:
  type: "s3"
  bucket: "${DATA_LAKE_BUCKET}"
  region: "${AWS_REGION:-us-east-1}"

# Observation Configuration - Production Scale
observation:
  workers: 10
  buffer_size: 50000
  batch_size: 1000

# Schema Reconciler Configuration - Production
schema_reconciler:
  enabled: true
  reconcile_interval: "1h"
  dry_run: false
  backup_enabled: true
  kubernetes:
    in_cluster: true  # Production: Run in cluster
    namespace: "${K8S_NAMESPACE:-gollmslake-prod}"
    label_selector: "app=gollmslake,env=prod"
  cassandra:
    schema_path: "/etc/gollmslake/schemas/cassandra"
    migration_timeout: "10m"
    backup_keyspace: "gollmslake_backup"
    replication_factor: 3
  opensearch:
    index_templates_path: "/etc/gollmslake/schemas/opensearch"
    migration_timeout: "10m"
    backup_indices: true

# OCSF Schemas Configuration - Production
ocsf_schemas:
  enabled: true
  schemas_path: "/etc/gollmslake/schemas/ocsf"
  version: "${OCSF_VERSION:-1.3.0}"
  cache_enabled: true
  cache_ttl: "4h"
  validation_mode: "strict"  # Production: Strict validation
  refresh_interval: "12h"

# Batch Processing Configuration - Production Scale
batch:
  enabled: true
  max_workers: 20
  queue_size: 10000
  job_timeout: "4h"
  retry_attempts: 5
  retry_delay: "1m"
  cleanup_interval: "30m"
  metrics_interval: "30s"
  checkpoint_enabled: true
  checkpoint_dir: "/var/lib/gollmslake/checkpoints"

# Complex Event Processing Configuration - Production
cep:
  enabled: true
  buffer_size: 100000
  patterns_dir: "/etc/gollmslake/patterns"
  correlation_enabled: true
  correlation_window: "10m"
  max_correlation_groups: 10000
  correlation_threshold: 0.85
  correlation_keys: ["src_ip", "user_id", "session_id", "device_id"]
  threat_detection_enabled: true
  ddos_detection_enabled: true
  brute_force_detection_enabled: true
  anomaly_detection_enabled: true
  threat_update_interval: "30m"

# Omnibus Configuration - Production Disabled (Data Lake Focus)
omnibus:
  enabled: false
  input_topics: []
  active_stages: []
  transformations_dir: "/etc/gollmslake/transformations"
  stages: {}
  batching:
    enabled: false

# Enrichment Configuration - Production Disabled (Data Lake Focus)
enrichment:
  enabled: false
  batch_size: 1000
  workers: 10
  timeout: "60s"
  retry_attempts: 5

# Rules Configuration - Production Disabled (Data Lake Focus)
rules:
  enabled: false
  rules_dir: "/etc/gollmslake/rules"
  buffer_size: 10000
  workers: 10
  processing_delay: "50ms"

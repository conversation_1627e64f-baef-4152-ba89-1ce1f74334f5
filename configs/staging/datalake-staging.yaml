# Staging Data Lake Configuration
# This configuration is for staging/pre-production environments with production-like settings but reduced scale

application:
  name: "core-gollmslake-go"
  version: "${APP_VERSION:-1.0.0}"
  environment: "staging"

logging:
  level: "debug"  # More verbose logging for staging
  format: "json"
  output_path: "/var/log/gollmslake/datalake-staging.log"

# Data Lake Integration Configuration - Staging
data_lake:
  enabled: true
  catalog:
    type: "rest"
    uri: "${ICEBERG_CATALOG_URI:-http://iceberg-catalog-staging:8181}"
    properties:
      warehouse: "${DATA_LAKE_WAREHOUSE_URI:-s3://staging-data-lake/}"
      io-impl: "org.apache.iceberg.aws.s3.S3FileIO"
      s3.endpoint: "${S3_ENDPOINT:-https://s3.amazonaws.com}"
      s3.access-key-id: "${AWS_ACCESS_KEY_ID}"
      s3.secret-access-key: "${AWS_SECRET_ACCESS_KEY}"
      s3.region: "${AWS_REGION:-us-east-1}"
      s3.path-style-access: "false"
      # Staging: Reduced connection limits
      client.connection-timeout-ms: "15000"
      client.socket-timeout-ms: "30000"
      client.max-connections: "50"
      client.max-error-retry: "3"

# Object Storage Configuration - Staging S3
object_storage:
  provider: "s3"
  endpoint: "${S3_ENDPOINT:-https://s3.amazonaws.com}"
  region: "${AWS_REGION:-us-east-1}"
  access_key_id: "${AWS_ACCESS_KEY_ID}"
  secret_access_key: "${AWS_SECRET_ACCESS_KEY}"
  bucket: "${DATA_LAKE_BUCKET:-staging-data-lake}"
  use_ssl: true

# Cassandra Configuration - Staging Cluster
cassandra:
  hosts: 
    - "${CASSANDRA_HOST:-cassandra-staging}"
  port: 9042
  username: "${CASSANDRA_USERNAME:-cassandra}"
  password: "${CASSANDRA_PASSWORD}"
  keyspace: "${CASSANDRA_KEYSPACE:-gollmslake_staging}"
  consistency: "LOCAL_QUORUM"  # Staging: Relaxed consistency
  timeout: "20s"
  retries: 3

# PostgreSQL Configuration - Staging Database
postgres:
  host: "${POSTGRES_HOST:-postgres-staging}"
  port: 5432
  database: "${POSTGRES_DATABASE:-gollmslake_staging}"
  username: "${POSTGRES_USERNAME:-postgres}"
  password: "${POSTGRES_PASSWORD}"
  ssl_mode: "prefer"  # Staging: Prefer SSL but allow fallback
  max_conns: 20
  max_idle_conns: 5
  conn_timeout: "20s"

# Kafka Configuration - Staging Cluster
kafka:
  brokers: 
    - "${KAFKA_BROKER:-kafka-staging:9092}"
  security_protocol: "${KAFKA_SECURITY_PROTOCOL:-SASL_PLAINTEXT}"
  sasl_mechanism: "${KAFKA_SASL_MECHANISM:-PLAIN}"
  sasl_username: "${KAFKA_USERNAME}"
  sasl_password: "${KAFKA_PASSWORD}"
  tls:
    enabled: false  # Staging: Simplified TLS setup
  producer:
    acks: "1"  # Staging: Faster acknowledgment
    retries: 5
    batch_size: 32768
    linger_ms: "5ms"
    buffer_memory: 67108864  # 64MB buffer
    compression: "snappy"
    max_message_size: 5242880  # 5MB max message
  consumer:
    group_id: "${KAFKA_CONSUMER_GROUP:-datalake-staging}"
    auto_offset_reset: "earliest"
    enable_auto_commit: true  # Staging: Auto-commit for simplicity
    auto_commit_interval: "5s"
    session_timeout: "30s"
    heartbeat_interval: "10s"
    max_poll_records: 500
    fetch_min_bytes: 512
    fetch_max_wait: "500ms"

# External API Configuration - Staging
external_api:
  address: "0.0.0.0"
  grpc_port: 8080
  http_port: 8081
  enable_reflection: true  # Staging: Enable reflection for testing
  tls:
    enabled: false  # Staging: Simplified setup
  rate_limit:
    enabled: true
    rps: 500  # Staging: Moderate rate limit
    burst: 1000
  auth:
    enabled: true
    jwt_secret: "${JWT_SECRET}"
    issuer: "${JWT_ISSUER:-staging-issuer}"
    audience: "${JWT_AUDIENCE:-staging-audience}"
    access_token_ttl: "2h"  # Staging: Longer TTL for testing
    refresh_token_ttl: "48h"

# Metrics Configuration - Staging
metrics:
  enabled: true
  address: "0.0.0.0:9090"
  path: "/metrics"

# Tracing Configuration - Staging
tracing:
  enabled: true
  service_name: "datalake-service-staging"
  endpoint: "${JAEGER_ENDPOINT:-http://jaeger-staging:14268/api/traces}"
  sample_rate: 0.1  # Staging: Higher sample rate for debugging

# Archive Configuration - Staging
archive:
  enabled: true
  buffer_size: 5000
  flush_interval: "30s"
  compression: true
  retention: "90d"  # Staging: Shorter retention
  policy_manager:
    enabled: true
    policy_check_interval: "2h"
    default_retention: "90d"
    policies_path: "/etc/gollmslake/policies"
  compliance_report:
    enabled: true
    report_interval: "12h"
    reports_path: "/var/log/gollmslake/compliance"
    regulations: ["GDPR"]
    alert_threshold: 90
  lifecycle_manager:
    enabled: true
    cleanup_interval: "12h"
    metadata_path: "/var/lib/gollmslake/metadata"
    storage_tiers: ["hot", "warm"]
    compression_enabled: true

# Storage Configuration - Staging
storage:
  type: "s3"
  bucket: "${DATA_LAKE_BUCKET:-staging-data-lake}"
  region: "${AWS_REGION:-us-east-1}"

# Observation Configuration - Staging Scale
observation:
  workers: 4
  buffer_size: 10000
  batch_size: 500

# Schema Reconciler Configuration - Staging
schema_reconciler:
  enabled: true
  reconcile_interval: "30m"
  dry_run: false
  backup_enabled: true
  kubernetes:
    in_cluster: true
    namespace: "${K8S_NAMESPACE:-gollmslake-staging}"
    label_selector: "app=gollmslake,env=staging"
  cassandra:
    schema_path: "/etc/gollmslake/schemas/cassandra"
    migration_timeout: "5m"
    backup_keyspace: "gollmslake_backup"
    replication_factor: 1  # Staging: Single replica
  opensearch:
    index_templates_path: "/etc/gollmslake/schemas/opensearch"
    migration_timeout: "5m"
    backup_indices: true

# OCSF Schemas Configuration - Staging
ocsf_schemas:
  enabled: true
  schemas_path: "/etc/gollmslake/schemas/ocsf"
  version: "${OCSF_VERSION:-1.3.0}"
  cache_enabled: true
  cache_ttl: "2h"
  validation_mode: "lenient"  # Staging: Lenient for testing
  refresh_interval: "6h"

# Batch Processing Configuration - Staging Scale
batch:
  enabled: true
  max_workers: 8
  queue_size: 5000
  job_timeout: "2h"
  retry_attempts: 3
  retry_delay: "30s"
  cleanup_interval: "1h"
  metrics_interval: "1m"
  checkpoint_enabled: true
  checkpoint_dir: "/var/lib/gollmslake/checkpoints"

# Complex Event Processing Configuration - Staging
cep:
  enabled: true
  buffer_size: 20000
  patterns_dir: "/etc/gollmslake/patterns"
  correlation_enabled: true
  correlation_window: "5m"
  max_correlation_groups: 2000
  correlation_threshold: 0.8
  correlation_keys: ["src_ip", "user_id", "session_id"]
  threat_detection_enabled: true
  ddos_detection_enabled: true
  brute_force_detection_enabled: true
  anomaly_detection_enabled: true
  threat_update_interval: "1h"

# Omnibus Configuration - Staging Disabled
omnibus:
  enabled: false
  input_topics: []
  active_stages: []
  transformations_dir: "/etc/gollmslake/transformations"
  stages: {}
  batching:
    enabled: false

# Enrichment Configuration - Staging Disabled
enrichment:
  enabled: false
  batch_size: 500
  workers: 4
  timeout: "30s"
  retry_attempts: 3

# Rules Configuration - Staging Disabled
rules:
  enabled: false
  rules_dir: "/etc/gollmslake/rules"
  buffer_size: 5000
  workers: 4
  processing_delay: "100ms"

# Security Configuration Template
# This file contains security-related configuration templates and best practices

# TLS/SSL Configuration Templates
tls_templates:
  # Production TLS Configuration
  production:
    enabled: true
    cert_file: "/etc/ssl/certs/gollmslake.crt"
    key_file: "/etc/ssl/private/gollmslake.key"
    ca_file: "/etc/ssl/certs/ca-bundle.crt"
    insecure_skip_verify: false
    min_version: "1.2"  # Minimum TLS 1.2
    cipher_suites:
      - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
      - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
      - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
    
  # Staging TLS Configuration
  staging:
    enabled: true
    cert_file: "/etc/ssl/certs/gollmslake-staging.crt"
    key_file: "/etc/ssl/private/gollmslake-staging.key"
    ca_file: "/etc/ssl/certs/ca-bundle.crt"
    insecure_skip_verify: false
    min_version: "1.2"

# Authentication Configuration Templates
auth_templates:
  # JWT-based Authentication (Production)
  jwt_production:
    enabled: true
    jwt_secret: "${JWT_SECRET}"  # Must be 256-bit key
    jwks_url: "${JWKS_URL}"
    issuer: "${JWT_ISSUER}"
    audience: "${JWT_AUDIENCE}"
    access_token_ttl: "15m"  # Short-lived access tokens
    refresh_token_ttl: "7d"
    algorithm: "RS256"  # Use RSA signatures
    public_key_path: "/etc/ssl/keys/jwt-public.pem"
    private_key_path: "/etc/ssl/keys/jwt-private.pem"
    
  # OAuth2/OIDC Configuration
  oauth2:
    enabled: true
    provider_url: "${OAUTH2_PROVIDER_URL}"
    client_id: "${OAUTH2_CLIENT_ID}"
    client_secret: "${OAUTH2_CLIENT_SECRET}"
    redirect_url: "${OAUTH2_REDIRECT_URL}"
    scopes: ["openid", "profile", "email"]
    
  # mTLS (Mutual TLS) Configuration
  mtls:
    enabled: true
    client_cert_file: "/etc/ssl/certs/client.crt"
    client_key_file: "/etc/ssl/private/client.key"
    ca_file: "/etc/ssl/certs/ca.crt"
    verify_client_cert: true

# Authorization Configuration Templates
authorization_templates:
  # Role-Based Access Control (RBAC)
  rbac:
    enabled: true
    roles_config_path: "/etc/gollmslake/rbac/roles.yaml"
    permissions_config_path: "/etc/gollmslake/rbac/permissions.yaml"
    default_role: "viewer"
    admin_roles: ["admin", "super-admin"]
    
  # Attribute-Based Access Control (ABAC)
  abac:
    enabled: false
    policy_engine: "opa"  # Open Policy Agent
    policy_path: "/etc/gollmslake/abac/policies"
    decision_cache_ttl: "5m"

# Encryption Configuration Templates
encryption_templates:
  # Data at Rest Encryption
  data_at_rest:
    enabled: true
    algorithm: "AES-256-GCM"
    key_management: "aws-kms"  # or "vault", "local"
    kms_key_id: "${KMS_KEY_ID}"
    key_rotation_interval: "90d"
    
  # Data in Transit Encryption
  data_in_transit:
    enabled: true
    force_tls: true
    min_tls_version: "1.2"
    
  # Field-Level Encryption (for sensitive data)
  field_level:
    enabled: true
    encrypted_fields:
      - "user.email"
      - "user.phone"
      - "payment.card_number"
      - "personal.ssn"
    encryption_key: "${FIELD_ENCRYPTION_KEY}"

# Secrets Management Configuration
secrets_management:
  # AWS Secrets Manager
  aws_secrets_manager:
    enabled: true
    region: "${AWS_REGION}"
    secret_prefix: "gollmslake/"
    refresh_interval: "1h"
    
  # HashiCorp Vault
  vault:
    enabled: false
    address: "${VAULT_ADDR}"
    token: "${VAULT_TOKEN}"
    mount_path: "secret/gollmslake"
    
  # Kubernetes Secrets
  kubernetes:
    enabled: true
    namespace: "${K8S_NAMESPACE}"
    secret_name: "gollmslake-secrets"

# Network Security Configuration
network_security:
  # IP Allowlisting
  ip_allowlist:
    enabled: true
    allowed_cidrs:
      - "10.0.0.0/8"      # Internal network
      - "**********/12"   # Private network
      - "***********/16"  # Local network
    
  # Rate Limiting
  rate_limiting:
    enabled: true
    global_rps: 10000
    per_ip_rps: 100
    burst_size: 200
    window_size: "1m"
    
  # DDoS Protection
  ddos_protection:
    enabled: true
    max_connections_per_ip: 100
    connection_timeout: "30s"
    slow_loris_protection: true

# Audit and Compliance Configuration
audit_config:
  enabled: true
  log_level: "info"
  log_format: "json"
  log_destination: "/var/log/gollmslake/audit.log"
  
  # Events to audit
  audit_events:
    - "authentication"
    - "authorization"
    - "data_access"
    - "configuration_change"
    - "admin_action"
    
  # Compliance frameworks
  compliance:
    gdpr:
      enabled: true
      data_retention: "6y"
      right_to_erasure: true
      consent_tracking: true
    
    sox:
      enabled: true
      financial_data_protection: true
      change_management: true
      access_controls: true
    
    hipaa:
      enabled: false
      phi_encryption: true
      access_logging: true
      minimum_necessary: true

# Security Headers Configuration
security_headers:
  # HTTP Security Headers
  http_headers:
    strict_transport_security: "max-age=31536000; includeSubDomains"
    content_security_policy: "default-src 'self'; script-src 'self' 'unsafe-inline'"
    x_frame_options: "DENY"
    x_content_type_options: "nosniff"
    x_xss_protection: "1; mode=block"
    referrer_policy: "strict-origin-when-cross-origin"
    
  # CORS Configuration
  cors:
    enabled: true
    allowed_origins: ["https://app.company.com"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE"]
    allowed_headers: ["Authorization", "Content-Type"]
    max_age: "86400"

# Certificate Management
certificate_management:
  # Automatic Certificate Management
  acme:
    enabled: true
    ca_server: "https://acme-v02.api.letsencrypt.org/directory"
    email: "${ACME_EMAIL}"
    domains: ["${DOMAIN_NAME}"]
    
  # Certificate Rotation
  rotation:
    enabled: true
    check_interval: "24h"
    renewal_threshold: "30d"  # Renew 30 days before expiry
    
  # Certificate Monitoring
  monitoring:
    enabled: true
    alert_threshold: "7d"  # Alert 7 days before expiry
    notification_webhook: "${CERT_ALERT_WEBHOOK}"

# Security Scanning Configuration
security_scanning:
  # Vulnerability Scanning
  vulnerability_scan:
    enabled: true
    scan_interval: "24h"
    severity_threshold: "medium"
    
  # Dependency Scanning
  dependency_scan:
    enabled: true
    scan_on_startup: true
    update_check_interval: "12h"
    
  # Container Scanning
  container_scan:
    enabled: true
    registry_scanning: true
    runtime_scanning: true

# Incident Response Configuration
incident_response:
  enabled: true
  
  # Automated Response
  automated_response:
    enabled: true
    block_suspicious_ips: true
    rate_limit_on_anomaly: true
    alert_on_failed_auth: true
    
  # Notification Configuration
  notifications:
    slack_webhook: "${SECURITY_SLACK_WEBHOOK}"
    email_alerts: ["<EMAIL>"]
    pagerduty_key: "${PAGERDUTY_INTEGRATION_KEY}"
    
  # Forensics
  forensics:
    enabled: true
    log_retention: "1y"
    evidence_collection: true
    chain_of_custody: true

# Environment-Specific Overrides
environments:
  production:
    auth_templates: "jwt_production"
    tls_templates: "production"
    audit_config:
      log_level: "info"
    rate_limiting:
      per_ip_rps: 50  # Stricter in production
      
  staging:
    auth_templates: "jwt_production"
    tls_templates: "staging"
    audit_config:
      log_level: "debug"
    rate_limiting:
      per_ip_rps: 200  # More lenient in staging
      
  development:
    auth_templates: "jwt_production"
    tls_templates: "staging"
    audit_config:
      enabled: false  # Disable audit in dev
    rate_limiting:
      enabled: false  # Disable rate limiting in dev

patterns:
  - id: "advanced_ddos_detection"
    name: "Advanced DDoS Attack Detection"
    description: "Detects sophisticated DDoS attacks using multiple indicators"
    enabled: true
    priority: 1
    category: "network_security"
    tags: ["ddos", "network", "critical"]
    conditions:
      - event_type: "network_activity"
        filters:
          - field: "class_uid"
            operator: "eq"
            value: 4001
          - field: "traffic.bytes_out"
            operator: "gt"
            value: 1048576  # 1MB
        sequence: 1
        quantifier: "one_or_more"
        count: 50
        within: "2m"
      - event_type: "network_activity"
        filters:
          - field: "src_endpoint.ip"
            operator: "regex"
            value: "^(?!10\\.|192\\.168\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.).*"
        sequence: 2
        quantifier: "one_or_more"
        count: 10
        within: "1m"
    window:
      type: "time"
      size: "5m"
      slide: "1m"
    actions:
      - type: "alert"
        enabled: true
        parameters:
          severity: "critical"
          message: "Advanced DDoS attack detected - high volume from multiple external sources"
          notify_channels: ["security-team", "network-ops"]
          escalate: true
      - type: "enrich"
        enabled: true
        parameters:
          fields:
            threat_category: "ddos_attack"
            attack_vector: "volumetric"
            confidence_level: "high"
            mitigation_required: true
      - type: "block"
        enabled: false
        parameters:
          duration: "10m"
          scope: "source_ip"
    metadata:
      created_by: "security-team"
      last_updated: "2024-01-15"
      version: "2.0"
      false_positive_rate: "low"

  - id: "credential_stuffing_detection"
    name: "Credential Stuffing Attack Detection"
    description: "Detects credential stuffing attacks across multiple accounts"
    enabled: true
    priority: 2
    category: "authentication"
    tags: ["credential-stuffing", "authentication", "high"]
    conditions:
      - event_type: "authentication"
        filters:
          - field: "activity_name"
            operator: "eq"
            value: "Logon"
          - field: "status_id"
            operator: "eq"
            value: 2  # Failed
          - field: "actor.user.name"
            operator: "ne"
            value: null
        sequence: 1
        quantifier: "exactly_n"
        count: 3
        within: "30s"
      - event_type: "authentication"
        filters:
          - field: "src_endpoint.ip"
            operator: "eq"
            value: "{{correlation.src_ip}}"
          - field: "actor.user.name"
            operator: "ne"
            value: "{{correlation.previous_user}}"
        sequence: 2
        quantifier: "one_or_more"
        count: 5
        within: "2m"
    window:
      type: "time"
      size: "10m"
    actions:
      - type: "alert"
        enabled: true
        parameters:
          severity: "high"
          message: "Credential stuffing attack detected - multiple failed logins across different accounts"
      - type: "enrich"
        enabled: true
        parameters:
          fields:
            attack_type: "credential_stuffing"
            affected_accounts: "{{event.unique_users}}"
            source_reputation: "suspicious"
      - type: "log"
        enabled: true
        parameters:
          level: "warn"
          message: "Security incident logged for investigation"
    correlation:
      enabled: true
      window: "15m"
      correlate_by: ["src_endpoint.ip"]
      require_all: false

  - id: "lateral_movement_detection"
    name: "Lateral Movement Detection"
    description: "Detects potential lateral movement within the network"
    enabled: true
    priority: 2
    category: "network_security"
    tags: ["lateral-movement", "network", "medium"]
    conditions:
      - event_type: "network_activity"
        filters:
          - field: "class_uid"
            operator: "eq"
            value: 4001
          - field: "src_endpoint.ip"
            operator: "regex"
            value: "^(10\\.|192\\.168\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.).*"
          - field: "dst_endpoint.port"
            operator: "in"
            value: [22, 3389, 5985, 5986]  # SSH, RDP, WinRM
        sequence: 1
        quantifier: "one_or_more"
        count: 3
        within: "5m"
      - event_type: "authentication"
        filters:
          - field: "activity_name"
            operator: "eq"
            value: "Logon"
          - field: "status_id"
            operator: "eq"
            value: 1  # Success
          - field: "src_endpoint.ip"
            operator: "eq"
            value: "{{correlation.src_ip}}"
        sequence: 2
        quantifier: "one"
        within: "2m"
    window:
      type: "time"
      size: "15m"
    actions:
      - type: "alert"
        enabled: true
        parameters:
          severity: "medium"
          message: "Potential lateral movement detected - internal network scanning followed by authentication"
      - type: "enrich"
        enabled: true
        parameters:
          fields:
            behavior_category: "lateral_movement"
            investigation_priority: "medium"
            network_segment: "internal"

  - id: "data_exfiltration_pattern"
    name: "Data Exfiltration Pattern Detection"
    description: "Detects patterns indicative of data exfiltration"
    enabled: true
    priority: 1
    category: "data_loss"
    tags: ["data-exfiltration", "network", "critical"]
    conditions:
      - event_type: "file_activity"
        filters:
          - field: "class_uid"
            operator: "in"
            value: [1001, 1002]  # File events
          - field: "activity_name"
            operator: "in"
            value: ["Read", "Copy"]
          - field: "file.size"
            operator: "gt"
            value: 10485760  # 10MB
        sequence: 1
        quantifier: "one_or_more"
        count: 5
        within: "10m"
      - event_type: "network_activity"
        filters:
          - field: "class_uid"
            operator: "eq"
            value: 4001
          - field: "traffic.bytes_out"
            operator: "gt"
            value: 52428800  # 50MB
          - field: "dst_endpoint.ip"
            operator: "regex"
            value: "^(?!10\\.|192\\.168\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.).*"
        sequence: 2
        quantifier: "one"
        within: "5m"
    window:
      type: "time"
      size: "20m"
    actions:
      - type: "alert"
        enabled: true
        parameters:
          severity: "critical"
          message: "Potential data exfiltration detected - large file access followed by external data transfer"
          immediate_response: true
      - type: "block"
        enabled: false
        parameters:
          duration: "30m"
          scope: "user_session"
      - type: "enrich"
        enabled: true
        parameters:
          fields:
            incident_type: "data_exfiltration"
            data_classification: "sensitive"
            response_required: "immediate"

  - id: "privilege_escalation_sequence"
    name: "Privilege Escalation Sequence"
    description: "Detects sequences of events indicating privilege escalation attempts"
    enabled: true
    priority: 1
    category: "privilege_abuse"
    tags: ["privilege-escalation", "security", "high"]
    conditions:
      - event_type: "process_activity"
        filters:
          - field: "class_uid"
            operator: "eq"
            value: 1007  # Process activity
          - field: "activity_name"
            operator: "in"
            value: ["Launch", "Create"]
          - field: "process.name"
            operator: "regex"
            value: ".*(sudo|runas|powershell|cmd).*"
        sequence: 1
        quantifier: "one"
      - event_type: "account_change"
        filters:
          - field: "activity_name"
            operator: "in"
            value: ["Group Change", "Privilege Use"]
          - field: "status_id"
            operator: "eq"
            value: 1  # Success
        sequence: 2
        quantifier: "one"
        within: "5m"
    window:
      type: "time"
      size: "10m"
    actions:
      - type: "alert"
        enabled: true
        parameters:
          severity: "high"
          message: "Privilege escalation sequence detected"
      - type: "log"
        enabled: true
        parameters:
          level: "error"
          message: "Critical security event - privilege escalation attempt"

pattern_groups:
  - id: "critical_security_patterns"
    name: "Critical Security Patterns"
    description: "High-priority security patterns requiring immediate attention"
    enabled: true
    priority: 1
    patterns:
      - "advanced_ddos_detection"
      - "data_exfiltration_pattern"
      - "privilege_escalation_sequence"
    metadata:
      escalation_policy: "immediate"
      notification_channels: ["security-team", "soc", "incident-response"]
      sla_response_time: "5m"

  - id: "authentication_security_patterns"
    name: "Authentication Security Patterns"
    description: "Patterns focused on authentication-related threats"
    enabled: true
    priority: 2
    patterns:
      - "credential_stuffing_detection"
    metadata:
      review_frequency: "daily"
      tuning_required: true

  - id: "network_security_patterns"
    name: "Network Security Patterns"
    description: "Patterns for network-based threat detection"
    enabled: true
    priority: 2
    patterns:
      - "advanced_ddos_detection"
      - "lateral_movement_detection"
    metadata:
      baseline_learning_period: "7d"
      false_positive_threshold: "5%"

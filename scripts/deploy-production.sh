#!/bin/bash

# Production Deployment Script for Gollmslake Data Lake Integration Service
# This script automates the deployment process with proper validation and rollback capabilities

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NAMESPACE="gollmslake-prod"
APP_NAME="gollmslake-datalake"
DEPLOYMENT_TIMEOUT="600s"
HEALTH_CHECK_TIMEOUT="300s"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handling
cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Deployment failed with exit code $exit_code"
        log_info "Check logs and consider rollback if necessary"
    fi
    exit $exit_code
}

trap cleanup EXIT

# Validation functions
validate_prerequisites() {
    log_info "Validating prerequisites..."
    
    # Check required tools
    local required_tools=("kubectl" "helm" "aws" "jq" "yq")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "Required tool '$tool' is not installed"
            exit 1
        fi
    done
    
    # Check kubectl context
    local current_context
    current_context=$(kubectl config current-context)
    log_info "Current kubectl context: $current_context"
    
    if [[ ! "$current_context" =~ prod ]]; then
        log_warning "Current context doesn't appear to be production. Continue? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_error "Deployment cancelled"
            exit 1
        fi
    fi
    
    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check namespace exists
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_error "Namespace '$NAMESPACE' does not exist"
        exit 1
    fi
    
    log_success "Prerequisites validated"
}

validate_configuration() {
    log_info "Validating configuration files..."
    
    local config_files=(
        "$PROJECT_ROOT/configs/production/datalake-production.yaml"
        "$PROJECT_ROOT/configs/security/security-config.yaml"
        "$PROJECT_ROOT/deployments/kubernetes/production/datalake-deployment.yaml"
        "$PROJECT_ROOT/deployments/kubernetes/production/datalake-service.yaml"
    )
    
    for config_file in "${config_files[@]}"; do
        if [[ ! -f "$config_file" ]]; then
            log_error "Configuration file not found: $config_file"
            exit 1
        fi
        
        # Validate YAML syntax
        if ! yq eval '.' "$config_file" > /dev/null 2>&1; then
            log_error "Invalid YAML syntax in: $config_file"
            exit 1
        fi
    done
    
    log_success "Configuration files validated"
}

validate_secrets() {
    log_info "Validating secrets..."
    
    local required_secrets=(
        "gollmslake-database-secrets"
        "gollmslake-messaging-secrets"
        "gollmslake-storage-secrets"
        "gollmslake-auth-secrets"
        "gollmslake-tls-secret"
    )
    
    for secret in "${required_secrets[@]}"; do
        if ! kubectl get secret "$secret" -n "$NAMESPACE" &> /dev/null; then
            log_error "Required secret '$secret' not found in namespace '$NAMESPACE'"
            exit 1
        fi
    done
    
    log_success "Secrets validated"
}

# Deployment functions
create_configmaps() {
    log_info "Creating/updating ConfigMaps..."
    
    # Create main configuration ConfigMap
    kubectl create configmap gollmslake-config \
        --from-file="$PROJECT_ROOT/configs/production/datalake-production.yaml" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # Create security configuration ConfigMap
    kubectl create configmap gollmslake-security-config \
        --from-file="$PROJECT_ROOT/configs/security/security-config.yaml" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "ConfigMaps created/updated"
}

deploy_application() {
    log_info "Deploying application..."
    
    # Apply deployment
    kubectl apply -f "$PROJECT_ROOT/deployments/kubernetes/production/datalake-deployment.yaml"
    
    # Apply service and ingress
    kubectl apply -f "$PROJECT_ROOT/deployments/kubernetes/production/datalake-service.yaml"
    
    # Apply monitoring configuration
    kubectl apply -f "$PROJECT_ROOT/deployments/kubernetes/monitoring/prometheus-rules.yaml"
    
    log_success "Application manifests applied"
}

wait_for_deployment() {
    log_info "Waiting for deployment to be ready..."
    
    # Wait for deployment rollout
    if ! kubectl rollout status deployment/"$APP_NAME" -n "$NAMESPACE" --timeout="$DEPLOYMENT_TIMEOUT"; then
        log_error "Deployment rollout failed"
        return 1
    fi
    
    # Wait for pods to be ready
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        local ready_pods
        ready_pods=$(kubectl get pods -n "$NAMESPACE" -l app=gollmslake,component=datalake -o jsonpath='{.items[*].status.conditions[?(@.type=="Ready")].status}' | grep -o "True" | wc -l)
        local total_pods
        total_pods=$(kubectl get pods -n "$NAMESPACE" -l app=gollmslake,component=datalake --no-headers | wc -l)
        
        if [ "$ready_pods" -eq "$total_pods" ] && [ "$total_pods" -gt 0 ]; then
            log_success "All pods are ready ($ready_pods/$total_pods)"
            return 0
        fi
        
        log_info "Waiting for pods to be ready ($ready_pods/$total_pods)..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Timeout waiting for pods to be ready"
    return 1
}

health_check() {
    log_info "Performing health checks..."
    
    # Get service endpoint
    local service_ip
    service_ip=$(kubectl get service "$APP_NAME-service" -n "$NAMESPACE" -o jsonpath='{.spec.clusterIP}')
    
    if [[ -z "$service_ip" ]]; then
        log_error "Could not get service IP"
        return 1
    fi
    
    # Health check with retry
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if kubectl run health-check-pod --rm -i --restart=Never --image=curlimages/curl:latest -- \
           curl -k -f "https://$service_ip:8081/health" --max-time 10 &> /dev/null; then
            log_success "Health check passed"
            return 0
        fi
        
        log_info "Health check attempt $((attempt + 1))/$max_attempts failed, retrying..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Health check failed after $max_attempts attempts"
    return 1
}

smoke_tests() {
    log_info "Running smoke tests..."
    
    # Basic connectivity test
    local service_ip
    service_ip=$(kubectl get service "$APP_NAME-service" -n "$NAMESPACE" -o jsonpath='{.spec.clusterIP}')
    
    # Test metrics endpoint
    if kubectl run metrics-test-pod --rm -i --restart=Never --image=curlimages/curl:latest -- \
       curl -k -f "https://$service_ip:9090/metrics" --max-time 10 | grep -q "gollmslake"; then
        log_success "Metrics endpoint test passed"
    else
        log_error "Metrics endpoint test failed"
        return 1
    fi
    
    # Test readiness endpoint
    if kubectl run readiness-test-pod --rm -i --restart=Never --image=curlimages/curl:latest -- \
       curl -k -f "https://$service_ip:8081/ready" --max-time 10; then
        log_success "Readiness endpoint test passed"
    else
        log_error "Readiness endpoint test failed"
        return 1
    fi
    
    log_success "Smoke tests completed"
}

rollback() {
    log_warning "Initiating rollback..."
    
    # Get previous revision
    local previous_revision
    previous_revision=$(kubectl rollout history deployment/"$APP_NAME" -n "$NAMESPACE" --revision=0 | tail -2 | head -1 | awk '{print $1}')
    
    if [[ -n "$previous_revision" ]]; then
        log_info "Rolling back to revision $previous_revision"
        kubectl rollout undo deployment/"$APP_NAME" -n "$NAMESPACE" --to-revision="$previous_revision"
        
        if kubectl rollout status deployment/"$APP_NAME" -n "$NAMESPACE" --timeout="$DEPLOYMENT_TIMEOUT"; then
            log_success "Rollback completed successfully"
        else
            log_error "Rollback failed"
            return 1
        fi
    else
        log_error "No previous revision found for rollback"
        return 1
    fi
}

# Main deployment function
main() {
    log_info "Starting production deployment of Gollmslake Data Lake Integration Service"
    log_info "Namespace: $NAMESPACE"
    log_info "Application: $APP_NAME"
    
    # Validation phase
    validate_prerequisites
    validate_configuration
    validate_secrets
    
    # Deployment phase
    create_configmaps
    deploy_application
    
    # Verification phase
    if ! wait_for_deployment; then
        log_error "Deployment verification failed"
        rollback
        exit 1
    fi
    
    if ! health_check; then
        log_error "Health check failed"
        rollback
        exit 1
    fi
    
    if ! smoke_tests; then
        log_error "Smoke tests failed"
        rollback
        exit 1
    fi
    
    # Success
    log_success "Production deployment completed successfully!"
    log_info "Application is now available at:"
    log_info "  - HTTPS: https://datalake.company.com"
    log_info "  - API: https://api.datalake.company.com"
    log_info "  - Metrics: https://datalake.company.com/metrics"
    
    # Display deployment info
    log_info "Deployment information:"
    kubectl get deployment "$APP_NAME" -n "$NAMESPACE" -o wide
    kubectl get pods -n "$NAMESPACE" -l app=gollmslake,component=datalake
    kubectl get service "$APP_NAME-service" -n "$NAMESPACE"
}

# Parse command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        rollback
        ;;
    "health-check")
        health_check
        ;;
    "smoke-tests")
        smoke_tests
        ;;
    *)
        echo "Usage: $0 [deploy|rollback|health-check|smoke-tests]"
        exit 1
        ;;
esac

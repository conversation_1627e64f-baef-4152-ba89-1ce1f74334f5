#!/bin/bash

# Data Lake Integration Test Script
# This script sets up the test environment and runs comprehensive integration tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.test.yml"
TEST_TIMEOUT="300s"
HEALTH_CHECK_RETRIES=30
HEALTH_CHECK_DELAY=10

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a service is healthy
check_service_health() {
    local service=$1
    local url=$2
    local retries=$3
    
    print_status "Checking health of $service..."
    
    for i in $(seq 1 $retries); do
        if curl -f -s "$url" > /dev/null 2>&1; then
            print_success "$service is healthy"
            return 0
        fi
        
        print_status "Waiting for $service to be ready... (attempt $i/$retries)"
        sleep $HEALTH_CHECK_DELAY
    done
    
    print_error "$service failed to become healthy after $retries attempts"
    return 1
}

# Function to cleanup
cleanup() {
    print_status "Cleaning up test environment..."
    docker-compose -f $COMPOSE_FILE down -v --remove-orphans
    docker system prune -f
    print_success "Cleanup completed"
}

# Function to setup test environment
setup_environment() {
    print_status "Setting up data lake test environment..."
    
    # Stop any existing containers
    docker-compose -f $COMPOSE_FILE down -v --remove-orphans 2>/dev/null || true
    
    # Start services
    print_status "Starting MinIO and Iceberg REST catalog..."
    docker-compose -f $COMPOSE_FILE up -d
    
    # Wait for services to be healthy
    print_status "Waiting for services to be ready..."
    sleep 20
    
    # Check MinIO health
    if ! check_service_health "MinIO" "http://localhost:9000/minio/health/live" $HEALTH_CHECK_RETRIES; then
        print_error "MinIO failed to start"
        cleanup
        exit 1
    fi
    
    # Check Iceberg REST catalog health
    if ! check_service_health "Iceberg REST" "http://localhost:8181/v1/config" $HEALTH_CHECK_RETRIES; then
        print_error "Iceberg REST catalog failed to start"
        cleanup
        exit 1
    fi
    
    print_success "Test environment is ready"
}

# Function to run integration tests
run_integration_tests() {
    print_status "Running data lake integration tests..."
    
    # Set environment variables for tests
    export DATALAKE_TEST_MINIO_ENDPOINT="http://localhost:9000"
    export DATALAKE_TEST_ICEBERG_ENDPOINT="http://localhost:8181"
    export DATALAKE_TEST_S3_ACCESS_KEY="minioadmin"
    export DATALAKE_TEST_S3_SECRET_KEY="minioadmin"
    
    # Run tests with timeout
    if timeout $TEST_TIMEOUT go test -v -tags=integration ./internal/datalake/... -run TestDataLakeIntegration; then
        print_success "Integration tests passed"
    else
        print_error "Integration tests failed"
        return 1
    fi
}

# Function to run performance tests
run_performance_tests() {
    print_status "Running data lake performance tests..."
    
    if timeout $TEST_TIMEOUT go test -v -tags=integration ./internal/datalake/... -run TestDataLakePerformance; then
        print_success "Performance tests passed"
    else
        print_warning "Performance tests failed (this might be expected)"
        return 0  # Don't fail the script for performance test failures
    fi
}

# Function to run chaos tests
run_chaos_tests() {
    print_status "Running chaos tests..."
    
    # Test with MinIO temporarily down
    print_status "Testing resilience to MinIO outage..."
    docker-compose -f $COMPOSE_FILE stop minio
    sleep 5
    
    # Run a subset of tests that should handle failures gracefully
    if go test -v -tags=integration ./internal/datalake/... -run TestDataLakeIntegration/health_check -timeout 30s; then
        print_status "Chaos test completed (some failures expected)"
    fi
    
    # Restart MinIO
    docker-compose -f $COMPOSE_FILE start minio
    sleep 10
    check_service_health "MinIO" "http://localhost:9000/minio/health/live" 10
}

# Function to generate test report
generate_test_report() {
    print_status "Generating test coverage report..."
    
    # Run tests with coverage
    go test -v -tags=integration -coverprofile=coverage.out ./internal/datalake/...
    go tool cover -html=coverage.out -o coverage.html
    
    print_success "Test coverage report generated: coverage.html"
}

# Main execution
main() {
    print_status "Starting Data Lake Integration Test Suite"
    
    # Parse command line arguments
    RUN_SETUP=true
    RUN_INTEGRATION=true
    RUN_PERFORMANCE=false
    RUN_CHAOS=false
    RUN_COVERAGE=false
    CLEANUP_AFTER=true
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --no-setup)
                RUN_SETUP=false
                shift
                ;;
            --performance)
                RUN_PERFORMANCE=true
                shift
                ;;
            --chaos)
                RUN_CHAOS=true
                shift
                ;;
            --coverage)
                RUN_COVERAGE=true
                shift
                ;;
            --no-cleanup)
                CLEANUP_AFTER=false
                shift
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo "Options:"
                echo "  --no-setup     Skip environment setup"
                echo "  --performance  Run performance tests"
                echo "  --chaos        Run chaos tests"
                echo "  --coverage     Generate coverage report"
                echo "  --no-cleanup   Skip cleanup after tests"
                echo "  --help         Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Trap cleanup on exit
    if [ "$CLEANUP_AFTER" = true ]; then
        trap cleanup EXIT
    fi
    
    # Setup environment
    if [ "$RUN_SETUP" = true ]; then
        setup_environment
    fi
    
    # Run tests
    if [ "$RUN_INTEGRATION" = true ]; then
        run_integration_tests
    fi
    
    if [ "$RUN_PERFORMANCE" = true ]; then
        run_performance_tests
    fi
    
    if [ "$RUN_CHAOS" = true ]; then
        run_chaos_tests
    fi
    
    if [ "$RUN_COVERAGE" = true ]; then
        generate_test_report
    fi
    
    print_success "Data Lake Integration Test Suite completed successfully"
}

# Run main function
main "$@"

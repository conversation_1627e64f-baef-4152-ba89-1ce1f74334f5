# Batch Processing Engine

The Batch Processing Engine provides a comprehensive alternative to Spark jobs for large-scale data processing, historical data analysis, and data compaction operations. Built in Go, it offers high performance, low resource usage, and seamless integration with the existing data processing pipeline.

## Features

### 🚀 High-Performance Processing
- **Worker Pool Architecture**: Configurable worker pools with automatic scaling
- **Priority-Based Scheduling**: Jobs are scheduled based on priority levels and resource availability
- **Concurrent Execution**: Multiple jobs can run simultaneously with resource management
- **Memory Efficient**: Optimized memory usage with configurable limits per job type

### 📊 Job Types
- **Compaction Jobs**: Alternative to Spark compaction with binpack and sort strategies
- **Aggregation Jobs**: Real-time and batch aggregation with windowing support
- **Enrichment Jobs**: Data enrichment with external sources and caching
- **Analysis Jobs**: Historical data analysis and trend detection

### 🔄 Reliability & Recovery
- **Failure Recovery**: Automatic retry mechanisms with exponential backoff
- **Checkpointing**: Job state persistence for recovery after failures
- **Timeout Handling**: Configurable timeouts with graceful job termination
- **Progress Tracking**: Real-time progress monitoring and ETA calculation

### 📈 Monitoring & Observability
- **Comprehensive Metrics**: Job execution metrics, throughput, and resource utilization
- **Health Checks**: Built-in health monitoring and status reporting
- **Alerting**: Configurable alerts for job failures and resource thresholds
- **Tracing**: Distributed tracing support for debugging and performance analysis

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Batch Processing Engine                      │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Job Manager   │  │    Scheduler    │  │    Executor     │  │
│  │                 │  │                 │  │                 │  │
│  │ • Job Storage   │  │ • Priority      │  │ • Worker Pool   │  │
│  │ • Lifecycle     │  │   Queue         │  │ • Resource      │  │
│  │ • Persistence   │  │ • Resource      │  │   Management    │  │
│  │ • Cleanup       │  │   Allocation    │  │ • Job Execution │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                        Job Processors                           │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Compaction    │  │   Aggregation   │  │   Enrichment    │  │
│  │   Processor     │  │   Processor     │  │   Processor     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                      Data Sources & Sinks                       │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │     Kafka       │  │    Iceberg      │  │   Cassandra     │  │
│  │   (Streaming)   │  │   (Data Lake)   │  │   (Database)    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Quick Start

### Installation

```bash
# Build the batch processing engine
go build -o bin/batch-engine cmd/batch-engine/main.go

# Make it executable
chmod +x bin/batch-engine
```

### Configuration

Create a configuration file based on the example:

```bash
cp configs/batch-engine.yaml configs/my-batch-engine.yaml
# Edit the configuration as needed
```

### Starting the Engine

```bash
# Start the batch processing engine
./bin/batch-engine start --config configs/my-batch-engine.yaml

# Start with verbose logging
./bin/batch-engine start --config configs/my-batch-engine.yaml --verbose
```

### Submitting Jobs

#### Using Command Line

```bash
# Submit a compaction job
./bin/batch-engine submit \
  --type compaction \
  --name "daily-compaction" \
  --priority 5

# Submit with job file
./bin/batch-engine submit --file jobs/compaction-job.json

# Submit and wait for completion
./bin/batch-engine submit --file jobs/aggregation-job.json --sync
```

#### Using Job Definition Files

Create a job definition file (`jobs/compaction-job.json`):

```json
{
  "name": "ocsf-table-compaction",
  "type": "compaction",
  "priority": 7,
  "config": {
    "parallelism": 4,
    "batch_size": 10000,
    "timeout": "2h",
    "parameters": {
      "target_file_size": "536870912",
      "min_file_size": "268435456",
      "strategy": "binpack"
    }
  },
  "input": {
    "type": "iceberg",
    "sources": ["ocsf.network_activity", "ocsf.process_activity"],
    "format": "parquet",
    "time_range": {
      "start": "2024-01-01T00:00:00Z",
      "end": "2024-01-02T00:00:00Z"
    }
  },
  "output": {
    "type": "iceberg",
    "target": "ocsf.compacted_activities",
    "format": "parquet",
    "partition": ["date", "source"]
  }
}
```

### Monitoring Jobs

```bash
# List all jobs
./bin/batch-engine jobs list

# List jobs by status
./bin/batch-engine jobs list --status running

# List jobs by type
./bin/batch-engine jobs list --type compaction

# Show job details
./bin/batch-engine jobs show <job-id>

# Show engine status
./bin/batch-engine status

# Show detailed metrics
./bin/batch-engine metrics
```

### Managing Jobs

```bash
# Cancel a running job
./bin/batch-engine cancel <job-id>
```

## Job Types

### Compaction Jobs

Compaction jobs provide an alternative to Spark compaction operations, optimizing data layout and reducing file count.

**Features:**
- Binpack and sort strategies
- Configurable file size targets
- Partial progress commits
- Time-based partitioning support

**Example Configuration:**
```yaml
job_types:
  compaction:
    default_parallelism: 4
    default_parameters:
      target_file_size: "536870912"  # 512MB
      strategy: "binpack"
      partial_progress_enabled: "true"
```

**Use Cases:**
- Daily/hourly table compaction
- Historical data optimization
- Storage cost reduction
- Query performance improvement

### Aggregation Jobs

Aggregation jobs perform real-time and batch aggregation operations with support for windowing and complex aggregation functions.

**Features:**
- Multiple aggregation functions (count, sum, avg, min, max)
- Time-based windowing
- Group-by operations
- Incremental aggregation

**Example Configuration:**
```yaml
job_types:
  aggregation:
    default_parallelism: 8
    default_parameters:
      aggregation_functions: ["count", "sum", "avg"]
      window_size: "1h"
      slide_interval: "15m"
```

**Use Cases:**
- Metrics calculation
- Report generation
- Real-time dashboards
- Data summarization

### Enrichment Jobs

Enrichment jobs enhance data with information from external sources, including threat intelligence, geolocation, and organizational data.

**Features:**
- Multiple enrichment sources
- Intelligent caching
- Batch processing optimization
- Rate limiting and retry logic

**Example Configuration:**
```yaml
job_types:
  enrichment:
    default_parallelism: 6
    default_parameters:
      enrichment_sources: ["bluepages", "threat_intel", "geo_ip"]
      cache_enabled: true
      cache_ttl: "1h"
```

**Use Cases:**
- Security event enrichment
- User information lookup
- Geolocation enhancement
- Threat intelligence correlation

### Analysis Jobs

Analysis jobs perform historical data analysis, trend detection, and complex analytical operations.

**Features:**
- Trend analysis
- Anomaly detection
- Statistical calculations
- Time series analysis

**Example Configuration:**
```yaml
job_types:
  analysis:
    default_parallelism: 4
    default_parameters:
      analysis_type: "trend"
      time_window: "7d"
      aggregation_level: "hourly"
```

**Use Cases:**
- Security trend analysis
- Performance monitoring
- Capacity planning
- Behavioral analysis

## Configuration

### Basic Configuration

```yaml
batch:
  enabled: true
  max_workers: 10
  queue_size: 100
  job_timeout: "1h"
  retry_attempts: 3
  retry_delay: "30s"
```

### Resource Limits

```yaml
resource_limits:
  max_concurrent_jobs:
    compaction: 3
    aggregation: 5
    enrichment: 8
  memory_limits:
    compaction: 4096  # MB
    aggregation: 2048
    enrichment: 1024
  cpu_limits:
    compaction: 4.0   # cores
    aggregation: 2.0
    enrichment: 1.0
```

### Scheduling

```yaml
scheduler:
  priority_levels:
    critical: 10
    high: 7
    medium: 5
    low: 2
  schedule_interval: "1s"
  max_queue_size: 1000
```

### Monitoring

```yaml
monitoring:
  health_check:
    enabled: true
    port: 8081
    interval: "30s"
  alerts:
    job_failure_rate_threshold: 0.1
    queue_size_threshold: 800
    worker_utilization_threshold: 0.9
```

## API Reference

### Programmatic Usage

```go
package main

import (
    "context"
    "time"
    
    "github.com/gollm/core-gollmslake-go/internal/batch"
    "github.com/gollm/core-gollmslake-go/pkg/config"
)

func main() {
    // Create engine
    cfg := &config.Config{
        Batch: config.BatchConfig{
            Enabled:    true,
            MaxWorkers: 10,
        },
    }
    
    engine, err := batch.NewEngine(cfg, logger)
    if err != nil {
        panic(err)
    }
    
    // Start engine
    if err := engine.Start(); err != nil {
        panic(err)
    }
    defer engine.Stop()
    
    // Submit job
    job := &batch.Job{
        Name: "example-compaction",
        Type: "compaction",
        Config: batch.JobConfig{
            Parallelism: 4,
            Timeout:     2 * time.Hour,
        },
        Input: batch.JobInput{
            Type:    "iceberg",
            Sources: []string{"ocsf.network_activity"},
        },
        Output: batch.JobOutput{
            Type:   "iceberg",
            Target: "ocsf.compacted_network_activity",
        },
    }
    
    result, err := engine.SubmitJob(context.Background(), job)
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("Job submitted: %s\n", result.JobID)
}
```

## Performance Tuning

### Worker Pool Optimization

```yaml
batch:
  max_workers: 20        # Increase for more concurrent jobs
  queue_size: 200        # Increase for higher job throughput
```

### Memory Management

```yaml
resource_limits:
  memory_limits:
    compaction: 8192     # Increase for large datasets
    aggregation: 4096    # Adjust based on aggregation complexity
```

### Parallelism Tuning

```yaml
job_types:
  compaction:
    default_parallelism: 8  # Increase for faster processing
  aggregation:
    default_parallelism: 16 # Higher parallelism for aggregations
```

## Troubleshooting

### Common Issues

1. **High Memory Usage**
   - Reduce batch sizes
   - Lower parallelism levels
   - Increase memory limits

2. **Job Timeouts**
   - Increase job timeout values
   - Optimize job parameters
   - Check resource availability

3. **Queue Backlog**
   - Increase worker count
   - Optimize job priorities
   - Scale horizontally

### Debugging

```bash
# Enable debug logging
./bin/batch-engine start --verbose

# Check job details
./bin/batch-engine jobs show <job-id>

# Monitor metrics
./bin/batch-engine metrics

# Check health status
curl http://localhost:8081/health
```

### Logs and Metrics

- **Application Logs**: Check for job execution details and errors
- **Metrics Endpoint**: `http://localhost:8080/metrics` for Prometheus metrics
- **Health Endpoint**: `http://localhost:8081/health` for health status

## Migration from Spark

### Compaction Jobs

**Spark:**
```sql
CALL iceberg.system.rewrite_data_files(
  table => 'ocsf.network_activity',
  strategy => 'binpack',
  options => map('target-file-size-bytes', '536870912')
)
```

**Batch Engine:**
```json
{
  "name": "network-activity-compaction",
  "type": "compaction",
  "input": {
    "type": "iceberg",
    "sources": ["ocsf.network_activity"]
  },
  "config": {
    "parameters": {
      "strategy": "binpack",
      "target_file_size": "536870912"
    }
  }
}
```

### Benefits of Migration

- **Lower Resource Usage**: 50-70% reduction in memory usage
- **Faster Startup**: Sub-second job initialization vs minutes for Spark
- **Better Integration**: Native Go integration with existing services
- **Simplified Operations**: Single binary deployment vs complex Spark clusters

## Contributing

When contributing to the batch processing engine:

1. Follow existing code patterns and architecture
2. Add comprehensive unit and integration tests
3. Update documentation for new features
4. Ensure backward compatibility
5. Test with realistic workloads

## Testing

### Unit Tests

```bash
# Run unit tests
go test ./internal/batch/...

# Run with coverage
go test -cover ./internal/batch/...
```

### Integration Tests

```bash
# Run integration tests
go test -tags=integration ./internal/batch/...
```

### Performance Tests

```bash
# Run benchmarks
go test -bench=. ./internal/batch/...
```

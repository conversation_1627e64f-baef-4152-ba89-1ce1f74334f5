# Enhanced Archival Service

The Enhanced Archival Service provides comprehensive raw log archival capabilities with retention policy management, compliance reporting, and data lifecycle management. This service builds upon the existing archival functionality to provide enterprise-grade data governance and compliance features.

## Features

### 🔄 Retention Policy Management
- **Dynamic Policy Engine**: Configurable retention policies based on data source, type, and classification
- **Policy Inheritance**: Hierarchical policy application with source-specific, type-specific, and default policies
- **Automated Enforcement**: Background policy enforcement with configurable check intervals
- **OpenSearch ISM Integration**: Seamless integration with existing OpenSearch Index State Management policies

### 📊 Compliance Reporting
- **Regulatory Compliance**: Built-in support for GDPR, SOX, HIPAA, PCI-DSS, and other regulations
- **Audit Trails**: Comprehensive logging of all archival operations and policy changes
- **Automated Reports**: Daily compliance reports with violation tracking and recommendations
- **Data Classification**: Support for public, internal, confidential, and restricted data classifications

### 🔄 Data Lifecycle Management
- **Storage Tier Management**: Automatic data movement between hot, warm, cold, and archive tiers
- **Compression and Encryption**: Configurable compression and encryption based on data sensitivity
- **Expiration Handling**: Automated deletion of expired data with compliance verification
- **Metadata Tracking**: Comprehensive metadata tracking for all archived data

### 🚨 Alerting and Monitoring
- **Violation Alerts**: Real-time alerts for compliance violations and policy failures
- **Metrics Collection**: Detailed metrics on archival operations, storage usage, and compliance status
- **Health Monitoring**: Built-in health checks for all service components
- **Performance Tracking**: Monitoring of throughput, latency, and resource utilization

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Enhanced Archival Service                    │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ Policy Manager  │  │Compliance Report│  │Lifecycle Manager│  │
│  │                 │  │                 │  │                 │  │
│  │ • Retention     │  │ • Audit Trails  │  │ • Tier Movement │  │
│  │   Policies      │  │ • Violation     │  │ • Expiration    │  │
│  │ • Rule Engine   │  │   Tracking      │  │ • Compression   │  │
│  │ • Policy        │  │ • Regulatory    │  │ • Metadata      │  │
│  │   Enforcement   │  │   Reports       │  │   Tracking      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                      Core Archival Engine                       │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ Object Storage  │  │   Compression   │  │   Encryption    │  │
│  │ (S3/MinIO/COS)  │  │   (gzip/zstd)   │  │   (AES-256)     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Configuration

### Basic Configuration

```yaml
archive:
  enabled: true
  buffer_size: 10000
  flush_interval: "5m"
  compression: true
  retention: "720h"  # 30 days default
  
  policy_manager:
    enabled: true
    policy_check_interval: "1h"
    default_retention: "720h"
    
  compliance_report:
    enabled: true
    report_interval: "24h"
    regulations: ["GDPR", "SOX", "HIPAA"]
    
  lifecycle_manager:
    enabled: true
    cleanup_interval: "1h"
    storage_tiers: ["hot", "warm", "cold", "archive"]
```

### Retention Policies

Retention policies are automatically loaded based on data source and type:

```yaml
# OCSF data with 7-day warm retention
ocsf:*:
  retention: "168h"  # 7 days
  storage_tier: "warm"
  compression: true

# Security audit logs with 7-year retention for SOX compliance
security:auditlog:
  retention: "61320h"  # 7 years
  storage_tier: "cold"
  immutable: true
  regulations: ["SOX", "PCI-DSS"]

# Office 365 logs with 1-year retention for GDPR compliance
office365:raw-logs:
  retention: "8760h"  # 1 year
  storage_tier: "cold"
  regulations: ["GDPR", "SOX"]
```

## Usage

### Starting the Service

```bash
# Start the archival service
./bin/archival-service start --config configs/archival-service.yaml

# Start with verbose logging
./bin/archival-service start --config configs/archival-service.yaml --verbose
```

### Managing Policies

```bash
# List all retention policies
./bin/archival-service policies list

# Show service status and metrics
./bin/archival-service status
```

### Compliance Reporting

```bash
# Generate compliance report
./bin/archival-service compliance report

# View compliance events
./bin/archival-service compliance events --since 24h
```

### Programmatic Usage

```go
package main

import (
    "context"
    "time"
    
    "github.com/gollm/core-gollmslake-go/internal/archival"
    "github.com/gollm/core-gollmslake-go/pkg/config"
)

func main() {
    // Load configuration
    cfg, err := config.Load()
    if err != nil {
        panic(err)
    }
    
    // Create archival service
    service, err := archival.NewService(cfg, logger)
    if err != nil {
        panic(err)
    }
    
    // Start service
    if err := service.Start(); err != nil {
        panic(err)
    }
    defer service.Stop()
    
    // Archive data
    request := &archival.ArchivalRequest{
        ID:        "example-001",
        Source:    "application",
        DataType:  "logs",
        Data:      []byte(`{"message": "example log"}`),
        Timestamp: time.Now(),
        Compliance: &archival.ComplianceRequirements{
            Regulations:    []string{"GDPR"},
            Classification: "internal",
            Encryption:     true,
            Audit:          true,
        },
    }
    
    result, err := service.Archive(context.Background(), request)
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("Archived to: %s\n", result.ArchivePath)
    fmt.Printf("Expires at: %s\n", result.ExpiresAt)
}
```

## Default Retention Policies

The service comes with pre-configured retention policies based on the existing OpenSearch ISM policies:

| Source | Data Type | Retention | Storage Tier | Compliance |
|--------|-----------|-----------|--------------|------------|
| ocsf | * | 7 days | warm | - |
| ocsf | guardium | 30 days | warm | - |
| ocsf | isv-general | 90 days | warm | - |
| security | auditlog | 180 days | cold | SOX, PCI-DSS |
| akamai | raw-logs | 30 days | standard | - |
| crowdstrike | raw-logs | 90 days | standard | SOX |
| office365 | raw-logs | 365 days | cold | GDPR, SOX |
| * | * | 30 days | standard | - (default) |

## Compliance Features

### Supported Regulations

- **GDPR**: General Data Protection Regulation
  - Right to erasure support
  - Data portability
  - Audit trail requirements
  
- **SOX**: Sarbanes-Oxley Act
  - 7-year minimum retention for financial data
  - Immutable storage requirements
  - Comprehensive audit trails
  
- **HIPAA**: Health Insurance Portability and Accountability Act
  - 6-year minimum retention
  - Encryption requirements
  - Access logging
  
- **PCI-DSS**: Payment Card Industry Data Security Standard
  - 1-3 year retention windows
  - Encryption requirements
  - Secure deletion

### Data Classifications

- **Public**: Publicly available information (30d-1y retention)
- **Internal**: Internal business information (90d-7y retention, encryption required)
- **Confidential**: Sensitive business information (1y-10y retention, encryption + audit required)
- **Restricted**: Highly sensitive information (7y-25y retention, encryption + audit + immutable)

## Monitoring and Metrics

### Key Metrics

- `archival_total_archived`: Total number of archived items
- `archival_total_deleted`: Total number of deleted items
- `archival_policy_violations`: Number of policy violations
- `archival_compliance_reports`: Number of compliance reports generated
- `archival_active_retention_rules`: Number of active retention rules
- `archival_storage_usage_bytes`: Total storage usage in bytes
- `archival_operation_duration_seconds`: Duration of archival operations

### Health Checks

The service provides health checks for:
- Object storage connectivity
- Policy manager status
- Compliance reporter status
- Lifecycle manager status

Access health checks at: `http://localhost:8080/health`

## Testing

### Unit Tests

```bash
# Run unit tests
go test ./internal/archival/...

# Run tests with coverage
go test -cover ./internal/archival/...
```

### Integration Tests

```bash
# Run integration tests (requires Docker)
go test -tags=integration ./internal/archival/...

# Run integration tests with TestContainers
go test -run TestArchivalServiceIntegration ./internal/archival/...
```

### Performance Tests

```bash
# Run performance tests
go test -run TestArchivalServiceConcurrency ./internal/archival/...

# Run benchmarks
go test -bench=. ./internal/archival/...
```

## Migration from Existing Archival

The enhanced archival service is designed to be backward compatible with the existing `internal/archive/writer.go`. To migrate:

1. **Update Configuration**: Add the new archival configuration sections
2. **Deploy Service**: Deploy the new archival service alongside existing components
3. **Gradual Migration**: Gradually migrate data sources to use the new service
4. **Policy Configuration**: Configure retention policies based on your compliance requirements
5. **Monitoring Setup**: Set up monitoring and alerting for the new service

## Troubleshooting

### Common Issues

1. **Storage Connection Failures**
   - Check object storage credentials and connectivity
   - Verify bucket permissions and existence
   
2. **Policy Violations**
   - Review retention policy configurations
   - Check data classification settings
   - Verify compliance requirements
   
3. **Performance Issues**
   - Adjust buffer sizes and flush intervals
   - Monitor concurrent operation limits
   - Check storage tier performance characteristics

### Logs and Debugging

Enable debug logging for detailed troubleshooting:

```bash
./bin/archival-service start --config configs/archival-service.yaml --verbose
```

Check service logs for:
- Policy enforcement activities
- Compliance violations
- Storage operations
- Lifecycle management actions

## Contributing

When contributing to the archival service:

1. Follow the existing code patterns and architecture
2. Add comprehensive unit and integration tests
3. Update documentation for new features
4. Ensure compliance with data governance requirements
5. Test with real data sources and storage backends

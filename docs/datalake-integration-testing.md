# Data Lake Integration Testing Guide

This document provides comprehensive guidance for testing the Data Lake Integration implementation in the core-gollmslake-go project.

## Overview

The data lake integration provides Apache Iceberg-based table management with S3-compatible storage backends. The testing strategy includes:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test with real MinIO and Iceberg REST catalog services using TestContainers
- **Performance Tests**: Measure throughput and latency under load
- **Chaos Tests**: Verify resilience under failure conditions

## Test Infrastructure

### TestContainers Setup

The integration tests use TestContainers to spin up real services:

- **MinIO**: S3-compatible object storage (minio/minio:latest)
- **Iceberg REST Catalog**: Apache Iceberg catalog service (tabulario/iceberg-rest:latest)
- **PostgreSQL**: Optional metadata store for advanced scenarios (postgres:15)

### Docker Compose Environment

For local development and manual testing, use the provided docker-compose configuration:

```bash
# Start the test environment
docker-compose -f docker-compose.test.yml up -d

# Check service health
curl http://localhost:9000/minio/health/live  # MinIO
curl http://localhost:8181/v1/config          # Iceberg REST

# Stop the environment
docker-compose -f docker-compose.test.yml down -v
```

## Running Tests

### Quick Start

```bash
# Run all data lake tests
make test-datalake-all

# Run only unit tests
make test-datalake

# Run only integration tests
make test-datalake-integration

# Run performance tests
make test-datalake-performance

# Run chaos tests
make test-datalake-chaos
```

### Manual Test Execution

```bash
# Unit tests
go test -v ./internal/datalake/...

# Integration tests (requires Docker)
go test -v -tags=integration ./internal/datalake/... -run TestDataLakeIntegration

# Performance tests
go test -v -tags=integration ./internal/datalake/... -run TestDataLakePerformance

# Concurrency tests
go test -v -tags=integration ./internal/datalake/... -run TestDataLakeConcurrency

# Chaos tests
go test -v -tags=integration ./internal/datalake/... -run TestDataLakeChaos
```

### Using the Test Script

The `scripts/test-datalake-integration.sh` script provides comprehensive test orchestration:

```bash
# Full test suite with environment setup
./scripts/test-datalake-integration.sh

# Skip environment setup (use existing containers)
./scripts/test-datalake-integration.sh --no-setup

# Run with performance and chaos tests
./scripts/test-datalake-integration.sh --performance --chaos

# Generate coverage report
./scripts/test-datalake-integration.sh --coverage

# Keep environment running after tests
./scripts/test-datalake-integration.sh --no-cleanup
```

## Test Scenarios

### Integration Tests (`TestDataLakeIntegration`)

1. **Namespace Operations**
   - Create, list, and drop namespaces
   - Verify namespace properties and metadata

2. **Table Operations**
   - Create tables with proper Iceberg schemas
   - List tables within namespaces
   - Get table metadata and properties
   - Drop tables and verify cleanup

3. **Data Operations**
   - Write data to Iceberg tables (simulated)
   - Read data from tables
   - Verify data integrity and schema compliance

4. **Query Operations**
   - Execute SQL queries against the catalog
   - Test SHOW NAMESPACES and SHOW TABLES
   - Verify query result formatting

5. **Analytics Operations**
   - Run analytics jobs (table stats, data quality)
   - Monitor job execution and status

### Concurrency Tests (`TestDataLakeConcurrency`)

1. **Concurrent Namespace Creation**
   - Multiple workers creating namespaces simultaneously
   - Verify thread safety and error handling

2. **Concurrent Table Operations**
   - Parallel table creation within namespaces
   - Test catalog consistency under concurrent access

### Chaos Tests (`TestDataLakeChaos`)

1. **Timeout Resilience**
   - Operations with short context timeouts
   - Verify graceful degradation and recovery

2. **Error Handling**
   - Invalid operations (non-existent resources)
   - Malformed requests and edge cases

3. **Client Recovery**
   - Recovery after multiple failed operations
   - Health check functionality after errors

### Performance Tests (`TestDataLakePerformance`)

1. **Concurrent Operations**
   - Measure throughput for namespace/table creation
   - Track latency under concurrent load

2. **Query Performance**
   - Measure query execution times
   - Verify reasonable response times

## Configuration

### Test Configuration File

The `configs/datalake-integration-test.yaml` file provides test-specific settings:

- Container connection endpoints
- Timeout configurations
- Performance test parameters
- Chaos test settings

### Environment Variables

Tests can be configured via environment variables:

```bash
export DATALAKE_TEST_MINIO_ENDPOINT="http://localhost:9000"
export DATALAKE_TEST_ICEBERG_ENDPOINT="http://localhost:8181"
export DATALAKE_TEST_S3_ACCESS_KEY="minioadmin"
export DATALAKE_TEST_S3_SECRET_KEY="minioadmin"
```

## Troubleshooting

### Common Issues

1. **Container Startup Failures**
   - Ensure Docker is running and has sufficient resources
   - Check port availability (9000, 8181, 5432)
   - Verify network connectivity

2. **Test Timeouts**
   - Increase timeout values in test configuration
   - Check container health status
   - Monitor system resource usage

3. **Connection Errors**
   - Verify service endpoints are accessible
   - Check firewall and network settings
   - Ensure proper credentials configuration

### Debug Mode

Enable debug logging for detailed test output:

```bash
export LOG_LEVEL=debug
go test -v -tags=integration ./internal/datalake/...
```

### Manual Service Verification

```bash
# Test MinIO connectivity
mc alias set testminio http://localhost:9000 minioadmin minioadmin
mc ls testminio/

# Test Iceberg REST catalog
curl -X GET http://localhost:8181/v1/config
curl -X GET http://localhost:8181/v1/namespaces
```

## Coverage and Quality Metrics

### Coverage Reports

```bash
# Generate coverage report
go test -v -tags=integration -coverprofile=coverage.out ./internal/datalake/...
go tool cover -html=coverage.out -o coverage.html
```

### Quality Gates

- **Unit Test Coverage**: Target 95%+ line coverage
- **Integration Test Coverage**: All major workflows tested
- **Performance Benchmarks**: Sub-second response times for basic operations
- **Chaos Test Resilience**: Graceful handling of all failure scenarios

## Continuous Integration

The tests are designed to run in CI/CD pipelines with:

- Automatic container lifecycle management
- Parallel test execution where safe
- Comprehensive error reporting
- Coverage and performance metrics collection

For CI environments, use the `--no-cleanup` flag cautiously and ensure proper resource cleanup in pipeline scripts.

# Production Deployment Guide

This guide provides comprehensive instructions for deploying the Data Lake Integration service to production environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Infrastructure Setup](#infrastructure-setup)
3. [Security Configuration](#security-configuration)
4. [Credential Management](#credential-management)
5. [Deployment Process](#deployment-process)
6. [Monitoring and Observability](#monitoring-and-observability)
7. [Backup and Recovery](#backup-and-recovery)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

- **Kubernetes Cluster**: v1.24+ with RBAC enabled
- **CPU**: Minimum 4 cores per pod, recommended 8 cores
- **Memory**: Minimum 8GB per pod, recommended 16GB
- **Storage**: 100GB+ for logs and checkpoints
- **Network**: High-bandwidth connection to data lake storage

### External Dependencies

- **Apache Iceberg REST Catalog**: v1.4.0+
- **Apache Kafka**: v3.0+ with SASL/SSL support
- **Apache Cassandra**: v4.0+ cluster
- **PostgreSQL**: v13+ with SSL support
- **AWS S3** or compatible object storage
- **Monitoring Stack**: Prometheus, Grafana, Jaeger

## Infrastructure Setup

### 1. Kubernetes Namespace

```bash
# Create production namespace
kubectl create namespace gollmslake-prod

# Apply resource quotas
kubectl apply -f - <<EOF
apiVersion: v1
kind: ResourceQuota
metadata:
  name: gollmslake-quota
  namespace: gollmslake-prod
spec:
  hard:
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    persistentvolumeclaims: "10"
EOF
```

### 2. Network Policies

```bash
# Apply network policies for security
kubectl apply -f - <<EOF
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: gollmslake-network-policy
  namespace: gollmslake-prod
spec:
  podSelector:
    matchLabels:
      app: gollmslake
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 8081
    - protocol: TCP
      port: 9090
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS
    - protocol: TCP
      port: 9042  # Cassandra
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 9092  # Kafka
EOF
```

### 3. Storage Classes

```bash
# Create storage class for high-performance storage
kubectl apply -f - <<EOF
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: gollmslake-ssd
provisioner: kubernetes.io/aws-ebs
parameters:
  type: gp3
  iops: "3000"
  throughput: "125"
  encrypted: "true"
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer
EOF
```

## Security Configuration

### 1. Service Account and RBAC

```bash
# Create service account
kubectl apply -f - <<EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: gollmslake-service-account
  namespace: gollmslake-prod
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT:role/GollmslakeServiceRole
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: gollmslake-role
  namespace: gollmslake-prod
rules:
- apiGroups: [""]
  resources: ["secrets", "configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: gollmslake-role-binding
  namespace: gollmslake-prod
subjects:
- kind: ServiceAccount
  name: gollmslake-service-account
  namespace: gollmslake-prod
roleRef:
  kind: Role
  name: gollmslake-role
  apiGroup: rbac.authorization.k8s.io
EOF
```

### 2. Pod Security Standards

```bash
# Apply pod security standards
kubectl label namespace gollmslake-prod \
  pod-security.kubernetes.io/enforce=restricted \
  pod-security.kubernetes.io/audit=restricted \
  pod-security.kubernetes.io/warn=restricted
```

## Credential Management

### 1. AWS Secrets Manager Setup

```bash
# Create database credentials
aws secretsmanager create-secret \
  --name "gollmslake/prod/database" \
  --description "Database credentials for production" \
  --secret-string '{
    "cassandra_username": "prod_cassandra_user",
    "cassandra_password": "STRONG_GENERATED_PASSWORD",
    "postgres_username": "prod_postgres_user",
    "postgres_password": "STRONG_GENERATED_PASSWORD"
  }' \
  --tags '[
    {"Key": "Environment", "Value": "production"},
    {"Key": "Application", "Value": "gollmslake"}
  ]'

# Create messaging credentials
aws secretsmanager create-secret \
  --name "gollmslake/prod/messaging" \
  --description "Messaging credentials for production" \
  --secret-string '{
    "kafka_username": "prod_kafka_user",
    "kafka_password": "STRONG_GENERATED_PASSWORD"
  }'

# Create authentication secrets
aws secretsmanager create-secret \
  --name "gollmslake/prod/auth" \
  --description "Authentication secrets for production" \
  --secret-string '{
    "jwt_secret": "256_BIT_GENERATED_SECRET_KEY",
    "oauth2_client_secret": "OAUTH2_CLIENT_SECRET"
  }'
```

### 2. Kubernetes Secrets

```bash
# Create TLS secret
kubectl create secret tls gollmslake-tls-secret \
  --cert=/path/to/tls.crt \
  --key=/path/to/tls.key \
  --namespace=gollmslake-prod

# Create generic secrets from AWS Secrets Manager
kubectl create secret generic gollmslake-database-secrets \
  --from-literal=cassandra-username="$(aws secretsmanager get-secret-value --secret-id gollmslake/prod/database --query SecretString --output text | jq -r .cassandra_username)" \
  --from-literal=cassandra-password="$(aws secretsmanager get-secret-value --secret-id gollmslake/prod/database --query SecretString --output text | jq -r .cassandra_password)" \
  --namespace=gollmslake-prod
```

## Deployment Process

### 1. Configuration Deployment

```bash
# Create ConfigMap from production config
kubectl create configmap gollmslake-config \
  --from-file=configs/production/datalake-production.yaml \
  --namespace=gollmslake-prod

# Create security ConfigMap
kubectl create configmap gollmslake-security-config \
  --from-file=configs/security/security-config.yaml \
  --namespace=gollmslake-prod
```

### 2. Application Deployment

```bash
# Deploy the application
kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gollmslake-datalake
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: gollmslake
      component: datalake
  template:
    metadata:
      labels:
        app: gollmslake
        component: datalake
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: gollmslake-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        fsGroup: 65534
      containers:
      - name: gollmslake-datalake
        image: gollmslake/datalake:v1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: grpc
        - containerPort: 8081
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: CONFIG_FILE
          value: "/etc/gollmslake/config.yaml"
        - name: ENVIRONMENT
          value: "production"
        # Database credentials from secrets
        - name: CASSANDRA_USERNAME
          valueFrom:
            secretKeyRef:
              name: gollmslake-database-secrets
              key: cassandra-username
        - name: CASSANDRA_PASSWORD
          valueFrom:
            secretKeyRef:
              name: gollmslake-database-secrets
              key: cassandra-password
        volumeMounts:
        - name: config-volume
          mountPath: /etc/gollmslake
          readOnly: true
        - name: tls-volume
          mountPath: /etc/ssl/certs
          readOnly: true
        - name: logs-volume
          mountPath: /var/log/gollmslake
        resources:
          requests:
            cpu: 4
            memory: 8Gi
          limits:
            cpu: 8
            memory: 16Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8081
            scheme: HTTPS
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8081
            scheme: HTTPS
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: config-volume
        configMap:
          name: gollmslake-config
      - name: tls-volume
        secret:
          secretName: gollmslake-tls-secret
      - name: logs-volume
        persistentVolumeClaim:
          claimName: gollmslake-logs-pvc
      nodeSelector:
        kubernetes.io/arch: amd64
        node-type: compute-optimized
      tolerations:
      - key: "dedicated"
        operator: "Equal"
        value: "gollmslake"
        effect: "NoSchedule"
EOF
```

### 3. Service and Ingress

```bash
# Create service
kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  name: gollmslake-datalake-service
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
spec:
  selector:
    app: gollmslake
    component: datalake
  ports:
  - name: grpc
    port: 8080
    targetPort: 8080
  - name: http
    port: 8081
    targetPort: 8081
  - name: metrics
    port: 9090
    targetPort: 9090
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gollmslake-datalake-ingress
  namespace: gollmslake-prod
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - datalake.company.com
    secretName: gollmslake-tls-cert
  rules:
  - host: datalake.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gollmslake-datalake-service
            port:
              number: 8081
EOF
```

## Monitoring and Observability

### 1. Prometheus ServiceMonitor

```bash
kubectl apply -f - <<EOF
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: gollmslake-datalake-monitor
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
spec:
  selector:
    matchLabels:
      app: gollmslake
      component: datalake
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    scheme: https
    tlsConfig:
      insecureSkipVerify: false
EOF
```

### 2. Grafana Dashboard

Import the provided Grafana dashboard from `configs/monitoring/grafana-dashboard.json`

### 3. Alerting Rules

```bash
kubectl apply -f - <<EOF
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: gollmslake-datalake-alerts
  namespace: gollmslake-prod
spec:
  groups:
  - name: gollmslake.datalake
    rules:
    - alert: GollmslakeDataLakeDown
      expr: up{job="gollmslake-datalake"} == 0
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "Gollmslake Data Lake service is down"
        description: "Gollmslake Data Lake service has been down for more than 1 minute"
    
    - alert: GollmslakeHighErrorRate
      expr: rate(http_requests_total{job="gollmslake-datalake",status=~"5.."}[5m]) > 0.1
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High error rate in Gollmslake Data Lake"
        description: "Error rate is {{ $value }} errors per second"
EOF
```

## Backup and Recovery

### 1. Configuration Backup

```bash
# Backup all configurations
kubectl get configmaps,secrets -n gollmslake-prod -o yaml > gollmslake-config-backup.yaml

# Backup to S3
aws s3 cp gollmslake-config-backup.yaml s3://company-backups/gollmslake/$(date +%Y-%m-%d)/
```

### 2. Data Backup

```bash
# Backup Iceberg table metadata
# This should be handled by your Iceberg catalog backup strategy

# Backup application logs
kubectl exec -n gollmslake-prod deployment/gollmslake-datalake -- \
  tar -czf /tmp/logs-backup.tar.gz /var/log/gollmslake/

# Copy backup to local
kubectl cp gollmslake-prod/gollmslake-datalake-pod:/tmp/logs-backup.tar.gz ./logs-backup.tar.gz
```

## Troubleshooting

### Common Issues

1. **Pod Startup Issues**
   ```bash
   # Check pod status
   kubectl get pods -n gollmslake-prod
   
   # Check pod logs
   kubectl logs -n gollmslake-prod deployment/gollmslake-datalake
   
   # Check events
   kubectl get events -n gollmslake-prod --sort-by='.lastTimestamp'
   ```

2. **Configuration Issues**
   ```bash
   # Validate configuration
   kubectl exec -n gollmslake-prod deployment/gollmslake-datalake -- \
     /app/gollmslake --config /etc/gollmslake/config.yaml --validate
   ```

3. **Network Connectivity**
   ```bash
   # Test Iceberg catalog connectivity
   kubectl exec -n gollmslake-prod deployment/gollmslake-datalake -- \
     curl -k https://iceberg-catalog:8181/v1/config
   
   # Test database connectivity
   kubectl exec -n gollmslake-prod deployment/gollmslake-datalake -- \
     nc -zv cassandra-host 9042
   ```

### Performance Tuning

1. **JVM Settings** (if applicable)
   ```yaml
   env:
   - name: JAVA_OPTS
     value: "-Xmx12g -Xms8g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
   ```

2. **Resource Limits**
   ```yaml
   resources:
     requests:
       cpu: 6
       memory: 12Gi
     limits:
       cpu: 12
       memory: 24Gi
   ```

3. **Horizontal Pod Autoscaler**
   ```bash
   kubectl autoscale deployment gollmslake-datalake \
     --cpu-percent=70 \
     --min=3 \
     --max=10 \
     -n gollmslake-prod
   ```

## Security Checklist

- [ ] All secrets stored in AWS Secrets Manager or Kubernetes secrets
- [ ] TLS enabled for all communications
- [ ] Network policies applied
- [ ] Pod security standards enforced
- [ ] RBAC configured with least privilege
- [ ] Container images scanned for vulnerabilities
- [ ] Audit logging enabled
- [ ] Monitoring and alerting configured
- [ ] Backup and recovery procedures tested
- [ ] Incident response plan documented

## Post-Deployment Validation

1. **Health Checks**
   ```bash
   # Check service health
   curl -k https://datalake.company.com/health
   
   # Check metrics endpoint
   curl -k https://datalake.company.com/metrics
   ```

2. **Functional Tests**
   ```bash
   # Run integration tests
   kubectl apply -f tests/integration/production-tests.yaml
   ```

3. **Performance Tests**
   ```bash
   # Run load tests
   kubectl apply -f tests/performance/load-tests.yaml
   ```

For additional support, contact the platform team or refer to the troubleshooting runbook.

## Related Documentation

- [Staging Deployment Guide](staging-deployment-guide.md)
- [Security Configuration Guide](../security/security-guide.md)
- [Monitoring and Alerting Guide](../monitoring/monitoring-guide.md)
- [Backup and Recovery Procedures](../operations/backup-recovery.md)
- [Troubleshooting Runbook](../operations/troubleshooting-runbook.md)

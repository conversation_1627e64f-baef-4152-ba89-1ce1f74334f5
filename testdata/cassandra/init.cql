-- Cassandra initialization script for testing
-- This script creates the necessary keyspace and tables for testing

-- Create test keyspace
CREATE KEYSPACE IF NOT EXISTS test_keyspace
WITH REPLICATION = {
    'class': 'SimpleStrategy',
    'replication_factor': 1
};

USE test_keyspace;

-- Create logs table for testing log storage
CREATE TABLE IF NOT EXISTS logs (
    id UUID PRIMARY KEY,
    timestamp TIMESTAMP,
    level TEXT,
    message TEXT,
    source TEXT,
    metadata MAP<TEXT, TEXT>,
    created_at TIMESTAMP
);

-- Create events table for testing event storage
CREATE TABLE IF NOT EXISTS events (
    id UUID PRIMARY KEY,
    event_type TEXT,
    timestamp TIMESTAMP,
    source_ip INET,
    destination_ip INET,
    user_id TEXT,
    session_id TEXT,
    data TEXT,
    processed BOOLEAN,
    created_at TIMESTAMP
);

-- Create network_activity table for OCSF events
CREATE TABLE IF NOT EXISTS network_activity (
    id UUID PRIMARY KEY,
    class_uid INT,
    activity_id INT,
    timestamp BIGINT,
    src_ip INET,
    src_port INT,
    dst_ip INET,
    dst_port INT,
    protocol TEXT,
    bytes_in BIGINT,
    bytes_out BIGINT,
    duration INT,
    status TEXT,
    metadata TEXT,
    created_at TIMESTAMP
);

-- Create rules table for rule engine testing
CREATE TABLE IF NOT EXISTS rules (
    id UUID PRIMARY KEY,
    name TEXT,
    description TEXT,
    conditions TEXT,
    actions TEXT,
    enabled BOOLEAN,
    priority INT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Create rule_executions table for tracking rule executions
CREATE TABLE IF NOT EXISTS rule_executions (
    id UUID PRIMARY KEY,
    rule_id UUID,
    event_id UUID,
    timestamp TIMESTAMP,
    result TEXT,
    execution_time_ms INT,
    error_message TEXT,
    created_at TIMESTAMP
);

-- Create transformations table for pipeline testing
CREATE TABLE IF NOT EXISTS transformations (
    id UUID PRIMARY KEY,
    name TEXT,
    description TEXT,
    template TEXT,
    input_schema TEXT,
    output_schema TEXT,
    enabled BOOLEAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Create pipeline_executions table for tracking pipeline runs
CREATE TABLE IF NOT EXISTS pipeline_executions (
    id UUID PRIMARY KEY,
    pipeline_name TEXT,
    timestamp TIMESTAMP,
    input_count INT,
    output_count INT,
    error_count INT,
    execution_time_ms INT,
    status TEXT,
    created_at TIMESTAMP
);

-- Create schema_versions table for schema management
CREATE TABLE IF NOT EXISTS schema_versions (
    version TEXT PRIMARY KEY,
    description TEXT,
    applied_at TIMESTAMP,
    checksum TEXT
);

-- Insert initial test data
INSERT INTO logs (id, timestamp, level, message, source, metadata, created_at)
VALUES (
    uuid(),
    toTimestamp(now()),
    'INFO',
    'Test log message',
    'test-source',
    {'component': 'test', 'version': '1.0.0'},
    toTimestamp(now())
);

INSERT INTO rules (id, name, description, conditions, actions, enabled, priority, created_at, updated_at)
VALUES (
    uuid(),
    'Test Rule',
    'A test rule for validation',
    '{"field": "event_type", "operator": "equals", "value": "login"}',
    '{"action": "alert", "severity": "medium"}',
    true,
    1,
    toTimestamp(now()),
    toTimestamp(now())
);

INSERT INTO transformations (id, name, description, template, input_schema, output_schema, enabled, created_at, updated_at)
VALUES (
    uuid(),
    'Test Transformation',
    'A test transformation for validation',
    '{"class_uid": 4001, "activity_id": 1, "timestamp": .timestamp}',
    '{"type": "object", "properties": {"timestamp": {"type": "integer"}}}',
    '{"type": "object", "properties": {"class_uid": {"type": "integer"}}}',
    true,
    toTimestamp(now()),
    toTimestamp(now())
);

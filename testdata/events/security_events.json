[{"timestamp": 1699123456000, "event_type": "authentication", "source_ip": "************", "user_id": "john.doe", "result": "success", "method": "password", "service": "ssh", "destination": "server-01", "session_id": "sess_auth_001"}, {"timestamp": 1699123460000, "event_type": "authentication", "source_ip": "************", "user_id": "jane.smith", "result": "failure", "reason": "invalid_credentials", "attempts": 3, "service": "ssh", "destination": "server-01", "session_id": "sess_auth_002"}, {"timestamp": 1699123470000, "event_type": "authorization", "source_ip": "************", "user_id": "john.doe", "result": "success", "resource": "/admin/users", "action": "read", "service": "web-app", "session_id": "sess_auth_001"}, {"timestamp": 1699123480000, "event_type": "authorization", "source_ip": "*************", "user_id": "bob.wilson", "result": "failure", "reason": "insufficient_privileges", "resource": "/admin/config", "action": "write", "service": "web-app", "session_id": "sess_auth_003"}, {"timestamp": 1699123490000, "event_type": "file_access", "source_ip": "************", "user_id": "john.doe", "result": "success", "file_path": "/var/log/application.log", "action": "read", "file_size": 1048576, "service": "file-server", "session_id": "sess_auth_001"}, {"timestamp": 1699123500000, "event_type": "file_access", "source_ip": "*************", "user_id": "alice.brown", "result": "failure", "reason": "file_not_found", "file_path": "/etc/sensitive/config.xml", "action": "read", "service": "file-server", "session_id": "sess_auth_004"}, {"timestamp": 1699123510000, "event_type": "privilege_escalation", "source_ip": "************", "user_id": "john.doe", "result": "success", "from_role": "user", "to_role": "admin", "service": "system", "session_id": "sess_auth_001"}, {"timestamp": 1699123520000, "event_type": "privilege_escalation", "source_ip": "*************", "user_id": "charlie.davis", "result": "failure", "reason": "unauthorized_attempt", "from_role": "guest", "to_role": "admin", "service": "system", "session_id": "sess_auth_005"}]
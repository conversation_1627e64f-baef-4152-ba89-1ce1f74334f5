-- PostgreSQL initialization script for testing
-- This script creates the necessary tables for testing

-- Create logs table for testing log storage
CREATE TABLE IF NOT EXISTS logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    source VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create events table for testing event storage
CREATE TABLE IF NOT EXISTS events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(100) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    source_ip INET,
    destination_ip INET,
    user_id VARCHAR(255),
    session_id VARCHAR(255),
    data JSONB,
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create network_activity table for OCSF events
CREATE TABLE IF NOT EXISTS network_activity (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    class_uid INTEGER NOT NULL,
    activity_id INTEGER NOT NULL,
    timestamp BIGINT NOT NULL,
    src_ip INET,
    src_port INTEGER,
    dst_ip INET,
    dst_port INTEGER,
    protocol VARCHAR(20),
    bytes_in BIGINT,
    bytes_out BIGINT,
    duration INTEGER,
    status VARCHAR(50),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create rules table for rule engine testing
CREATE TABLE IF NOT EXISTS rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    conditions JSONB NOT NULL,
    actions JSONB NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create rule_executions table for tracking rule executions
CREATE TABLE IF NOT EXISTS rule_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rule_id UUID NOT NULL REFERENCES rules(id),
    event_id UUID,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    result VARCHAR(50) NOT NULL,
    execution_time_ms INTEGER,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create transformations table for pipeline testing
CREATE TABLE IF NOT EXISTS transformations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    template TEXT NOT NULL,
    input_schema JSONB,
    output_schema JSONB,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create pipeline_executions table for tracking pipeline runs
CREATE TABLE IF NOT EXISTS pipeline_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pipeline_name VARCHAR(255) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    input_count INTEGER DEFAULT 0,
    output_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    execution_time_ms INTEGER,
    status VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create schema_versions table for schema management
CREATE TABLE IF NOT EXISTS schema_versions (
    version VARCHAR(50) PRIMARY KEY,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    checksum VARCHAR(64)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_logs_level ON logs(level);
CREATE INDEX IF NOT EXISTS idx_events_timestamp ON events(timestamp);
CREATE INDEX IF NOT EXISTS idx_events_type ON events(event_type);
CREATE INDEX IF NOT EXISTS idx_events_processed ON events(processed);
CREATE INDEX IF NOT EXISTS idx_network_activity_timestamp ON network_activity(timestamp);
CREATE INDEX IF NOT EXISTS idx_network_activity_src_ip ON network_activity(src_ip);
CREATE INDEX IF NOT EXISTS idx_network_activity_dst_ip ON network_activity(dst_ip);
CREATE INDEX IF NOT EXISTS idx_rules_enabled ON rules(enabled);
CREATE INDEX IF NOT EXISTS idx_rules_priority ON rules(priority);
CREATE INDEX IF NOT EXISTS idx_rule_executions_rule_id ON rule_executions(rule_id);
CREATE INDEX IF NOT EXISTS idx_rule_executions_timestamp ON rule_executions(timestamp);
CREATE INDEX IF NOT EXISTS idx_transformations_enabled ON transformations(enabled);
CREATE INDEX IF NOT EXISTS idx_pipeline_executions_name ON pipeline_executions(pipeline_name);
CREATE INDEX IF NOT EXISTS idx_pipeline_executions_timestamp ON pipeline_executions(timestamp);

-- Insert initial test data
INSERT INTO logs (timestamp, level, message, source, metadata) VALUES
(NOW(), 'INFO', 'Test log message', 'test-source', '{"component": "test", "version": "1.0.0"}'),
(NOW() - INTERVAL '1 hour', 'ERROR', 'Test error message', 'test-source', '{"component": "test", "error_code": "E001"}'),
(NOW() - INTERVAL '2 hours', 'DEBUG', 'Test debug message', 'test-source', '{"component": "test", "debug_level": "verbose"}');

INSERT INTO events (event_type, timestamp, source_ip, destination_ip, user_id, session_id, data) VALUES
('login', NOW(), '*************', '*********', 'john.doe', 'sess_12345', '{"success": true, "method": "password"}'),
('logout', NOW() - INTERVAL '30 minutes', '*************', '*********', 'john.doe', 'sess_12345', '{"reason": "user_initiated"}'),
('failed_login', NOW() - INTERVAL '1 hour', '*************', '*********', 'jane.smith', 'sess_67890', '{"reason": "invalid_password", "attempts": 3}');

INSERT INTO rules (name, description, conditions, actions, priority) VALUES
('Login Alert Rule', 'Alert on successful logins', '{"field": "event_type", "operator": "equals", "value": "login"}', '{"action": "alert", "severity": "low"}', 1),
('Failed Login Rule', 'Alert on failed login attempts', '{"field": "event_type", "operator": "equals", "value": "failed_login"}', '{"action": "alert", "severity": "medium"}', 2),
('High Traffic Rule', 'Alert on high network traffic', '{"field": "bytes_total", "operator": "greater_than", "value": 1000000}', '{"action": "alert", "severity": "high"}', 3);

INSERT INTO transformations (name, description, template, input_schema, output_schema) VALUES
('Network to OCSF', 'Transform network events to OCSF format', '{"class_uid": 4001, "activity_id": 1, "timestamp": .timestamp, "src_endpoint": {"ip": .source_ip, "port": .source_port}, "dst_endpoint": {"ip": .destination_ip, "port": .destination_port}}', '{"type": "object", "properties": {"timestamp": {"type": "integer"}, "source_ip": {"type": "string"}, "source_port": {"type": "integer"}}}', '{"type": "object", "properties": {"class_uid": {"type": "integer"}, "activity_id": {"type": "integer"}}}'),
('Security to OCSF', 'Transform security events to OCSF format', '{"class_uid": 3001, "activity_id": 1, "timestamp": .timestamp, "user": {"name": .user_id}, "src_endpoint": {"ip": .source_ip}}', '{"type": "object", "properties": {"timestamp": {"type": "integer"}, "user_id": {"type": "string"}, "source_ip": {"type": "string"}}}', '{"type": "object", "properties": {"class_uid": {"type": "integer"}, "activity_id": {"type": "integer"}}}');

INSERT INTO schema_versions (version, description, checksum) VALUES
('1.0.0', 'Initial schema version', 'abc123def456'),
('1.1.0', 'Added network activity table', 'def456ghi789'),
('1.2.0', 'Added rule engine tables', 'ghi789jkl012');

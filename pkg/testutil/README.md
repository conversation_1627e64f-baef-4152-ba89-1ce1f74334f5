# Enhanced Testing Framework

This package provides a comprehensive testing framework for the Core GoLLMSlake project, offering utilities for unit testing, integration testing, performance testing, and mock-based testing.

## Features

### 🐳 Container Integration
- **TestContainers Support**: Automated container management for Kafka, Cassandra, PostgreSQL, and Redis
- **Container Suite**: Centralized container lifecycle management
- **Configuration Generation**: Automatic test configuration from container connections

### 🧪 Test Fixtures
- **Test Data Management**: Load and manage test data from files
- **Temporary File Creation**: Create temporary files and directories for testing
- **JSON Utilities**: JSON marshaling/unmarshaling helpers
- **Assertion Helpers**: Enhanced assertion utilities

### 🚀 Performance Testing
- **Throughput Testing**: Measure operations per second with configurable concurrency
- **Memory Testing**: Track memory allocation and usage patterns
- **Latency Testing**: Measure latency distribution with percentiles
- **Benchmark Integration**: Go benchmark test integration

### 🎭 Mock Utilities
- **Mock Services**: Pre-built mocks for Kafka, Cassandra, OCSF, and other services
- **Mock Data Generation**: Generate realistic test data for network events, security events, and OCSF events
- **Verification Helpers**: Track and verify mock interactions

### 🏗️ Test Suite Framework
- **Comprehensive Test Suite**: Unified testing framework with configuration management
- **Test Runner**: Organized test execution for unit, integration, and performance tests
- **Cleanup Management**: Automatic resource cleanup and teardown

## Quick Start

### Basic Unit Testing

```go
func TestMyFunction(t *testing.T) {
    suite := testutil.NewTestSuite(t, testutil.DefaultTestConfig())
    
    // Use test fixtures
    fixture := suite.Fixture()
    testData := fixture.LoadTestData("events/network_activity.json")
    
    // Use mock data
    mockData := testutil.NewMockData()
    event := mockData.GenerateNetworkEvent()
    
    // Your test logic here
    assert.NotNil(t, event)
}
```

### Integration Testing with Containers

```go
func TestIntegration(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping integration test in short mode")
    }
    
    config := testutil.DefaultTestConfig()
    config.UseContainers = true
    
    suite := testutil.NewTestSuite(t, config)
    ctx, err := suite.SetupIntegrationTest()
    require.NoError(t, err)
    
    // Use real containers
    assert.NotEmpty(t, ctx.KafkaBrokers)
    assert.NotNil(t, ctx.CassandraConn)
    assert.NotNil(t, ctx.PostgresConn)
}
```

### Performance Testing

```go
func BenchmarkMyFunction(b *testing.B) {
    logger := zap.NewNop()
    pt := testutil.NewPerformanceTest(b, logger)
    
    test := testutil.ThroughputTest{
        Name:        "MyFunction",
        Duration:    5 * time.Second,
        Concurrency: 10,
        Execute: func(ctx context.Context, data interface{}) error {
            // Your function under test
            return myFunction()
        },
    }
    
    result := pt.RunThroughputTest(test)
    b.Logf("Throughput: %.2f ops/sec", result.OpsPerSecond)
}
```

### Mock-Based Testing

```go
func TestWithMocks(t *testing.T) {
    // Create mock Kafka producer
    producer := testutil.NewMockKafkaProducer()
    producer.On("SendMessage", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
    
    // Use the mock
    err := producer.SendMessage(ctx, "topic", "key", []byte("value"), nil)
    assert.NoError(t, err)
    
    // Verify interactions
    messages := producer.GetMessages()
    assert.Len(t, messages, 1)
    producer.AssertExpectations(t)
}
```

## Available Containers

The framework supports the following containers out of the box:

- **Kafka**: `confluentinc/cp-kafka:7.4.0`
- **Cassandra**: `cassandra:4.1` with initialization scripts
- **PostgreSQL**: `postgres:15-alpine` with test schema
- **Redis**: `redis:7-alpine`

## Test Data

The framework includes comprehensive test data in the `testdata/` directory:

- `testdata/cassandra/init.cql`: Cassandra schema and test data
- `testdata/postgres/init.sql`: PostgreSQL schema and test data
- `testdata/events/`: Sample event data (network, security, OCSF)

## Mock Services

Pre-built mocks are available for:

- **MockKafkaProducer**: Kafka message production with verification
- **MockCassandraSession**: Cassandra query execution tracking
- **MockOCSFService**: OCSF event validation
- **MockTransformationEngine**: JSLT transformation testing
- **MockLogger**: Structured logging verification

## Performance Testing

The framework provides three types of performance tests:

1. **Throughput Tests**: Measure operations per second
2. **Memory Tests**: Track memory allocation patterns
3. **Latency Tests**: Measure response time distribution

## Running Tests

```bash
# Unit tests only
make test

# Integration tests (requires Docker)
make test-integration

# Performance tests and benchmarks
make test-performance

# All tests
make test-all

# Generate coverage report
make test-coverage
```

## Configuration

Test configuration can be customized:

```go
config := &testutil.TestConfig{
    UseContainers:     true,
    ContainerTimeout:  120 * time.Second,
    PerformanceTests:  true,
    IntegrationTests:  true,
    LogLevel:         "debug",
    Parallel:         false,
    Timeout:          300 * time.Second,
}

suite := testutil.NewTestSuite(t, config)
```

## Best Practices

1. **Use Short Mode**: Always check `testing.Short()` for integration tests
2. **Container Cleanup**: The framework handles cleanup automatically
3. **Mock Verification**: Always call `AssertExpectations()` on mocks
4. **Test Data**: Use the provided test data or create your own in `testdata/`
5. **Performance Baselines**: Establish performance baselines for critical paths
6. **Parallel Testing**: Be careful with parallel tests when using containers

## Examples

See `pkg/testutil/example_test.go` for comprehensive examples of all framework features.

## Architecture

The testing framework follows these design principles:

- **Composable**: Mix and match components as needed
- **Configurable**: Extensive configuration options
- **Cleanup-Safe**: Automatic resource management
- **Performance-Aware**: Built-in performance testing capabilities
- **Mock-Friendly**: Comprehensive mocking support
- **Container-Ready**: First-class TestContainers integration

This framework enables comprehensive testing strategies that match the Java test-utilities module capabilities while leveraging Go's testing ecosystem and TestContainers for infrastructure testing.

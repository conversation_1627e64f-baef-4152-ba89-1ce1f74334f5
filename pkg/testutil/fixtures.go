// Package testutil provides test fixtures and utilities
package testutil

import (
	"encoding/json"
	"io"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"go.uber.org/zap/zaptest"
)

// TestFixture provides common test utilities and fixtures
type TestFixture struct {
	t      *testing.T
	logger *zap.Logger
	tmpDir string
}

// NewTestFixture creates a new test fixture
func NewTestFixture(t *testing.T) *TestFixture {
	logger := zaptest.NewLogger(t, zaptest.Level(zap.DebugLevel))

	tmpDir, err := os.MkdirTemp("", "gollmslake-test-*")
	require.NoError(t, err)

	t.Cleanup(func() {
		os.RemoveAll(tmpDir)
	})

	return &TestFixture{
		t:      t,
		logger: logger,
		tmpDir: tmpDir,
	}
}

// Logger returns the test logger
func (tf *TestFixture) Logger() *zap.Logger {
	return tf.logger
}

// TempDir returns the temporary directory for this test
func (tf *TestFixture) TempDir() string {
	return tf.tmpDir
}

// CreateTempFile creates a temporary file with the given content
func (tf *TestFixture) CreateTempFile(name, content string) string {
	filePath := filepath.Join(tf.tmpDir, name)

	// Create directory if needed
	dir := filepath.Dir(filePath)
	err := os.MkdirAll(dir, 0755)
	require.NoError(tf.t, err)

	err = os.WriteFile(filePath, []byte(content), 0644)
	require.NoError(tf.t, err)

	return filePath
}

// CreateTempJSONFile creates a temporary JSON file with the given data
func (tf *TestFixture) CreateTempJSONFile(name string, data interface{}) string {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	require.NoError(tf.t, err)

	return tf.CreateTempFile(name, string(jsonData))
}

// LoadTestData loads test data from a file relative to the test directory
func (tf *TestFixture) LoadTestData(relativePath string) []byte {
	// Find the testdata directory by walking up from the current test file
	testFile := tf.getTestFile()
	testDir := filepath.Dir(testFile)

	for {
		testDataDir := filepath.Join(testDir, "testdata")
		if _, err := os.Stat(testDataDir); err == nil {
			filePath := filepath.Join(testDataDir, relativePath)
			data, err := os.ReadFile(filePath)
			require.NoError(tf.t, err, "failed to read test data file: %s", filePath)
			return data
		}

		parent := filepath.Dir(testDir)
		if parent == testDir {
			break
		}
		testDir = parent
	}

	tf.t.Fatalf("testdata directory not found for path: %s", relativePath)
	return nil
}

// LoadTestJSON loads and unmarshals JSON test data
func (tf *TestFixture) LoadTestJSON(relativePath string, v interface{}) {
	data := tf.LoadTestData(relativePath)
	err := json.Unmarshal(data, v)
	require.NoError(tf.t, err, "failed to unmarshal JSON test data: %s", relativePath)
}

// AssertJSONEqual compares two JSON objects for equality
func (tf *TestFixture) AssertJSONEqual(expected, actual interface{}, msgAndArgs ...interface{}) {
	expectedJSON, err := json.MarshalIndent(expected, "", "  ")
	require.NoError(tf.t, err)

	actualJSON, err := json.MarshalIndent(actual, "", "  ")
	require.NoError(tf.t, err)

	assert.JSONEq(tf.t, string(expectedJSON), string(actualJSON), msgAndArgs...)
}

// AssertEventuallyTrue waits for a condition to become true
func (tf *TestFixture) AssertEventuallyTrue(condition func() bool, timeout time.Duration, msgAndArgs ...interface{}) {
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	timeoutCh := time.After(timeout)

	for {
		select {
		case <-ticker.C:
			if condition() {
				return
			}
		case <-timeoutCh:
			tf.t.Fatalf("condition did not become true within %v: %v", timeout, msgAndArgs)
		}
	}
}

// CaptureOutput captures stdout/stderr during function execution
func (tf *TestFixture) CaptureOutput(fn func()) (stdout, stderr string) {
	// Create pipes for stdout and stderr
	oldStdout := os.Stdout
	oldStderr := os.Stderr

	stdoutR, stdoutW, err := os.Pipe()
	require.NoError(tf.t, err)

	stderrR, stderrW, err := os.Pipe()
	require.NoError(tf.t, err)

	os.Stdout = stdoutW
	os.Stderr = stderrW

	// Capture output in goroutines
	stdoutCh := make(chan string, 1)
	stderrCh := make(chan string, 1)

	go func() {
		defer close(stdoutCh)
		data, _ := io.ReadAll(stdoutR)
		stdoutCh <- string(data)
	}()

	go func() {
		defer close(stderrCh)
		data, _ := io.ReadAll(stderrR)
		stderrCh <- string(data)
	}()

	// Execute function
	fn()

	// Restore original stdout/stderr
	os.Stdout = oldStdout
	os.Stderr = oldStderr

	// Close write ends and read captured output
	stdoutW.Close()
	stderrW.Close()

	stdout = <-stdoutCh
	stderr = <-stderrCh

	stdoutR.Close()
	stderrR.Close()

	return stdout, stderr
}

// getTestFile returns the file path of the calling test
func (tf *TestFixture) getTestFile() string {
	// This is a simplified approach - in practice you might want to use
	// runtime.Caller to get the actual test file path
	wd, err := os.Getwd()
	require.NoError(tf.t, err)
	return wd
}

// MockData provides utilities for creating mock test data
type MockData struct{}

// NewMockData creates a new mock data generator
func NewMockData() *MockData {
	return &MockData{}
}

// GenerateNetworkEvent creates a mock network event for testing
func (md *MockData) GenerateNetworkEvent() map[string]interface{} {
	return map[string]interface{}{
		"timestamp":            time.Now().Unix() * 1000,
		"source_ip":            "*************",
		"source_port":          54321,
		"source_hostname":      "workstation-01",
		"destination_ip":       "*********",
		"destination_port":     443,
		"destination_hostname": "web-server-01",
		"protocol":             "TCP",
		"direction":            "Outbound",
		"status":               "Success",
		"bytes_transferred":    2048,
		"bytes_in":             1024,
		"bytes_out":            1024,
		"packet_count":         15,
		"duration":             1500,
		"start_time":           time.Now().Unix() * 1000,
		"end_time":             (time.Now().Unix() + 2) * 1000,
		"connection_id":        "conn_12345",
		"session_id":           "sess_67890",
	}
}

// GenerateOCSFNetworkEvent creates a mock OCSF network event
func (md *MockData) GenerateOCSFNetworkEvent() map[string]interface{} {
	return map[string]interface{}{
		"class_uid":     4001,
		"activity_id":   1,
		"activity_name": "Network Activity",
		"category_name": "Network Activity",
		"category_uid":  4,
		"class_name":    "Network Activity",
		"severity_id":   1,
		"severity":      "Informational",
		"type_name":     "Network Activity: Unknown",
		"type_uid":      400100,
		"time":          time.Now().Unix() * 1000,
		"timestamp":     time.Now().Unix() * 1000,
		"metadata": map[string]interface{}{
			"version": "1.3.0",
			"product": map[string]interface{}{
				"name":        "Core GoLLMSlake",
				"vendor_name": "IBM",
				"version":     "1.0.0",
			},
			"profiles": []string{"network"},
		},
		"src_endpoint": map[string]interface{}{
			"ip":       "*************",
			"port":     54321,
			"hostname": "workstation-01",
		},
		"dst_endpoint": map[string]interface{}{
			"ip":       "*********",
			"port":     443,
			"hostname": "web-server-01",
		},
		"traffic": map[string]interface{}{
			"bytes":     2048,
			"bytes_in":  1024,
			"bytes_out": 1024,
			"packets":   15,
		},
		"connection_info": map[string]interface{}{
			"protocol_name": "TCP",
			"protocol_num":  6,
			"direction":     "Outbound",
			"direction_id":  2,
		},
		"status":     "Success",
		"status_id":  1,
		"duration":   1500,
		"start_time": time.Now().Unix() * 1000,
		"end_time":   (time.Now().Unix() + 2) * 1000,
	}
}

// GenerateSecurityEvent creates a mock security event
func (md *MockData) GenerateSecurityEvent() map[string]interface{} {
	return map[string]interface{}{
		"timestamp":   time.Now().Unix() * 1000,
		"event_type":  "authentication",
		"source_ip":   "************",
		"user_id":     "john.doe",
		"result":      "failure",
		"reason":      "invalid_credentials",
		"attempts":    3,
		"service":     "ssh",
		"destination": "server-01",
	}
}

// StringContains checks if a string contains a substring (case-insensitive)
func StringContains(s, substr string) bool {
	return strings.Contains(strings.ToLower(s), strings.ToLower(substr))
}

// MapContainsKey checks if a map contains a specific key
func MapContainsKey(m map[string]interface{}, key string) bool {
	_, exists := m[key]
	return exists
}

// MapGetString safely gets a string value from a map
func MapGetString(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

// MapGetInt safely gets an int value from a map
func MapGetInt(m map[string]interface{}, key string) int {
	switch val := m[key].(type) {
	case int:
		return val
	case float64:
		return int(val)
	case int64:
		return int(val)
	default:
		return 0
	}
}

// TestHelper provides additional test utilities
type TestHelper struct {
	t *testing.T
}

// NewTestHelper creates a new test helper
func NewTestHelper(t *testing.T) *TestHelper {
	return &TestHelper{t: t}
}

// RequireNoError is a convenience wrapper for require.NoError
func (th *TestHelper) RequireNoError(err error, msgAndArgs ...interface{}) {
	require.NoError(th.t, err, msgAndArgs...)
}

// AssertEqual is a convenience wrapper for assert.Equal
func (th *TestHelper) AssertEqual(expected, actual interface{}, msgAndArgs ...interface{}) {
	assert.Equal(th.t, expected, actual, msgAndArgs...)
}

// AssertContains is a convenience wrapper for assert.Contains
func (th *TestHelper) AssertContains(s, contains interface{}, msgAndArgs ...interface{}) {
	assert.Contains(th.t, s, contains, msgAndArgs...)
}

// AssertNotEmpty is a convenience wrapper for assert.NotEmpty
func (th *TestHelper) AssertNotEmpty(object interface{}, msgAndArgs ...interface{}) {
	assert.NotEmpty(th.t, object, msgAndArgs...)
}

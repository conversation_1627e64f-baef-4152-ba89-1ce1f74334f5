//go:build integration
// +build integration

package testutil

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"
)

// TestContainerSuite_Integration tests the container suite with real containers
func TestContainerSuite_Integration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	ctx := context.Background()
	logger := zaptest.NewLogger(t)

	suite := NewContainerSuite(ctx, logger)
	defer func() {
		if err := suite.Cleanup(); err != nil {
			t.Logf("Cleanup error: %v", err)
		}
	}()

	t.Run("StartKafka", func(t *testing.T) {
		brokers, err := suite.StartKafka()
		require.NoError(t, err)
		assert.NotEmpty(t, brokers)
		assert.Len(t, brokers, 1)

		// Verify Kafka container is accessible
		container := suite.GetContainer("kafka")
		assert.NotNil(t, container)

		// Test that we can get the brokers again
		// Note: In a real implementation, you would cast to the appropriate container type
		// For now, just verify the container exists
		assert.NotNil(t, container)
	})

	t.Run("StartCassandra", func(t *testing.T) {
		conn, err := suite.StartCassandra()
		require.NoError(t, err)
		assert.NotNil(t, conn)
		assert.NotEmpty(t, conn.Host)
		assert.Greater(t, conn.Port, 0)

		// Verify connection details
		address := conn.Address()
		assert.Contains(t, address, ":")

		// Verify Cassandra container is accessible
		container := suite.GetContainer("cassandra")
		assert.NotNil(t, container)
	})

	t.Run("StartPostgreSQL", func(t *testing.T) {
		conn, err := suite.StartPostgreSQL()
		require.NoError(t, err)
		assert.NotNil(t, conn)
		assert.NotEmpty(t, conn.Host)
		assert.Greater(t, conn.Port, 0)
		assert.Equal(t, "testdb", conn.Database)
		assert.Equal(t, "testuser", conn.Username)
		assert.Equal(t, "testpass", conn.Password)

		// Verify DSN format
		dsn := conn.DSN()
		assert.Contains(t, dsn, "postgres://")
		assert.Contains(t, dsn, "testuser:testpass")
		assert.Contains(t, dsn, "testdb")

		// Verify PostgreSQL container is accessible
		container := suite.GetContainer("postgres")
		assert.NotNil(t, container)
	})

	t.Run("StartRedis", func(t *testing.T) {
		conn, err := suite.StartRedis()
		require.NoError(t, err)
		assert.NotNil(t, conn)
		assert.NotEmpty(t, conn.Host)
		assert.Greater(t, conn.Port, 0)

		// Verify connection details
		address := conn.Address()
		assert.Contains(t, address, ":")

		// Verify Redis container is accessible
		container := suite.GetContainer("redis")
		assert.NotNil(t, container)
	})
}

// TestBuildTestConfig_Integration tests building configuration with container connections
func TestBuildTestConfig_Integration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	ctx := context.Background()
	logger := zaptest.NewLogger(t)

	suite := NewContainerSuite(ctx, logger)
	defer func() {
		if err := suite.Cleanup(); err != nil {
			t.Logf("Cleanup error: %v", err)
		}
	}()

	// Start containers
	kafkaBrokers, err := suite.StartKafka()
	require.NoError(t, err)

	cassandraConn, err := suite.StartCassandra()
	require.NoError(t, err)

	postgresConn, err := suite.StartPostgreSQL()
	require.NoError(t, err)

	// Build test configuration
	cfg := BuildTestConfig(kafkaBrokers, cassandraConn, postgresConn)

	// Verify configuration
	assert.NotNil(t, cfg)
	assert.Equal(t, "test-app", cfg.Application.Name)
	assert.Equal(t, "test", cfg.Application.Environment)

	// Verify Kafka configuration
	assert.Equal(t, kafkaBrokers, cfg.Kafka.Brokers)
	assert.Equal(t, "PLAINTEXT", cfg.Kafka.SecurityProtocol)

	// Verify Cassandra configuration
	assert.Contains(t, cfg.Cassandra.Hosts, cassandraConn.Host)
	assert.Equal(t, cassandraConn.Port, cfg.Cassandra.Port)
	assert.Equal(t, "test_keyspace", cfg.Cassandra.Keyspace)

	// Verify PostgreSQL configuration
	assert.Equal(t, postgresConn.Host, cfg.Postgres.Host)
	assert.Equal(t, postgresConn.Port, cfg.Postgres.Port)
	assert.Equal(t, postgresConn.Database, cfg.Postgres.Database)
	assert.Equal(t, postgresConn.Username, cfg.Postgres.Username)
	assert.Equal(t, postgresConn.Password, cfg.Postgres.Password)
}

// TestEndToEndPipeline_Integration tests a complete pipeline with real containers
func TestEndToEndPipeline_Integration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	ctx := context.Background()
	logger := zaptest.NewLogger(t)

	suite := NewContainerSuite(ctx, logger)
	defer func() {
		if err := suite.Cleanup(); err != nil {
			t.Logf("Cleanup error: %v", err)
		}
	}()

	// Start all required containers
	kafkaBrokers, err := suite.StartKafka()
	require.NoError(t, err)

	cassandraConn, err := suite.StartCassandra()
	require.NoError(t, err)

	postgresConn, err := suite.StartPostgreSQL()
	require.NoError(t, err)

	// Build configuration
	cfg := BuildTestConfig(kafkaBrokers, cassandraConn, postgresConn)

	// Wait for containers to be fully ready
	time.Sleep(5 * time.Second)

	t.Run("ConfigurationValidation", func(t *testing.T) {
		// Test that configuration is valid
		assert.NotNil(t, cfg)
		assert.NotEmpty(t, cfg.Kafka.Brokers)
		assert.NotEmpty(t, cfg.Cassandra.Hosts)
		assert.NotEmpty(t, cfg.Postgres.Host)
	})

	t.Run("ContainerConnectivity", func(t *testing.T) {
		// Test that containers are accessible
		// This would typically involve creating actual connections
		// to verify the containers are working properly

		// For now, just verify the containers exist
		kafkaContainer := suite.GetContainer("kafka")
		assert.NotNil(t, kafkaContainer)

		cassandraContainer := suite.GetContainer("cassandra")
		assert.NotNil(t, cassandraContainer)

		postgresContainer := suite.GetContainer("postgres")
		assert.NotNil(t, postgresContainer)
	})
}

// TestContainerSuite_Cleanup tests proper cleanup of containers
func TestContainerSuite_Cleanup(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	ctx := context.Background()
	logger := zaptest.NewLogger(t)

	suite := NewContainerSuite(ctx, logger)

	// Start a container
	_, err := suite.StartKafka()
	require.NoError(t, err)

	// Verify container exists
	container := suite.GetContainer("kafka")
	assert.NotNil(t, container)

	// Test cleanup
	err = suite.Cleanup()
	assert.NoError(t, err)

	// After cleanup, the container should still exist in the map
	// but should be terminated
	container = suite.GetContainer("kafka")
	assert.NotNil(t, container)
}

// TestContainerSuite_MultipleContainers tests starting multiple containers
func TestContainerSuite_MultipleContainers(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	ctx := context.Background()
	logger := zaptest.NewLogger(t)

	suite := NewContainerSuite(ctx, logger)
	defer func() {
		if err := suite.Cleanup(); err != nil {
			t.Logf("Cleanup error: %v", err)
		}
	}()

	// Start multiple containers concurrently
	type result struct {
		name string
		err  error
	}

	results := make(chan result, 3)

	go func() {
		_, err := suite.StartKafka()
		results <- result{"kafka", err}
	}()

	go func() {
		_, err := suite.StartCassandra()
		results <- result{"cassandra", err}
	}()

	go func() {
		_, err := suite.StartPostgreSQL()
		results <- result{"postgres", err}
	}()

	// Collect results
	for i := 0; i < 3; i++ {
		select {
		case res := <-results:
			assert.NoError(t, res.err, "Failed to start %s container", res.name)
		case <-time.After(120 * time.Second):
			t.Fatal("Timeout waiting for containers to start")
		}
	}

	// Verify all containers are running
	assert.NotNil(t, suite.GetContainer("kafka"))
	assert.NotNil(t, suite.GetContainer("cassandra"))
	assert.NotNil(t, suite.GetContainer("postgres"))
}

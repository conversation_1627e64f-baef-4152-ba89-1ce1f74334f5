// Package testutil provides comprehensive test suite utilities
package testutil

import (
	"context"
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zaptest"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// TestSuite provides a comprehensive testing framework
type TestSuite struct {
	t           *testing.T
	logger      *zap.Logger
	fixture     *TestFixture
	containers  *ContainerSuite
	performance *PerformanceTest
	config      *TestConfig
	cleanup     []func() error
}

// TestConfig holds configuration for the test suite
type TestConfig struct {
	UseContainers    bool
	ContainerTimeout time.Duration
	PerformanceTests bool
	IntegrationTests bool
	TempDir          string
	TestDataDir      string
	LogLevel         string
	Parallel         bool
	Timeout          time.Duration
}

// DefaultTestConfig returns a default test configuration
func DefaultTestConfig() *TestConfig {
	return &TestConfig{
		UseContainers:    true,
		ContainerTimeout: 120 * time.Second,
		PerformanceTests: false,
		IntegrationTests: true,
		LogLevel:         "info",
		Parallel:         false,
		Timeout:          300 * time.Second,
	}
}

// NewTestSuite creates a new comprehensive test suite
func NewTestSuite(t *testing.T, config *TestConfig) *TestSuite {
	if config == nil {
		config = DefaultTestConfig()
	}

	// Create logger
	var logger *zap.Logger
	if config.LogLevel == "debug" {
		logger = zaptest.NewLogger(t, zaptest.Level(zap.DebugLevel))
	} else {
		logger = zaptest.NewLogger(t, zaptest.Level(zap.InfoLevel))
	}

	// Create fixture
	fixture := NewTestFixture(t)

	// Create container suite if needed
	var containers *ContainerSuite
	if config.UseContainers {
		ctx := context.Background()
		containers = NewContainerSuite(ctx, logger)
	}

	// Create performance test utility
	var performance *PerformanceTest
	if config.PerformanceTests {
		// Performance tests will be created when needed in benchmark functions
		// We can't create it here since we don't have a *testing.B
		performance = nil
	}

	suite := &TestSuite{
		t:           t,
		logger:      logger,
		fixture:     fixture,
		containers:  containers,
		performance: performance,
		config:      config,
		cleanup:     make([]func() error, 0),
	}

	// Setup cleanup
	t.Cleanup(func() {
		if err := suite.Cleanup(); err != nil {
			t.Logf("Test suite cleanup error: %v", err)
		}
	})

	return suite
}

// Logger returns the test logger
func (ts *TestSuite) Logger() *zap.Logger {
	return ts.logger
}

// Fixture returns the test fixture
func (ts *TestSuite) Fixture() *TestFixture {
	return ts.fixture
}

// Containers returns the container suite (may be nil)
func (ts *TestSuite) Containers() *ContainerSuite {
	return ts.containers
}

// Performance returns the performance test utility (may be nil)
func (ts *TestSuite) Performance() *PerformanceTest {
	return ts.performance
}

// Config returns the test configuration
func (ts *TestSuite) Config() *TestConfig {
	return ts.config
}

// SetupIntegrationTest sets up containers and infrastructure for integration testing
func (ts *TestSuite) SetupIntegrationTest() (*IntegrationTestContext, error) {
	if !ts.config.IntegrationTests {
		return nil, fmt.Errorf("integration tests are disabled")
	}

	if ts.containers == nil {
		return nil, fmt.Errorf("containers are not enabled")
	}

	ts.logger.Info("Setting up integration test environment")

	ctx := &IntegrationTestContext{
		suite:  ts,
		logger: ts.logger,
	}

	// Start containers based on test requirements
	if ts.needsKafka() {
		brokers, err := ts.containers.StartKafka()
		if err != nil {
			return nil, fmt.Errorf("failed to start Kafka: %w", err)
		}
		ctx.KafkaBrokers = brokers
	}

	if ts.needsCassandra() {
		conn, err := ts.containers.StartCassandra()
		if err != nil {
			return nil, fmt.Errorf("failed to start Cassandra: %w", err)
		}
		ctx.CassandraConn = conn
	}

	if ts.needsPostgreSQL() {
		conn, err := ts.containers.StartPostgreSQL()
		if err != nil {
			return nil, fmt.Errorf("failed to start PostgreSQL: %w", err)
		}
		ctx.PostgresConn = conn
	}

	if ts.needsRedis() {
		conn, err := ts.containers.StartRedis()
		if err != nil {
			return nil, fmt.Errorf("failed to start Redis: %w", err)
		}
		ctx.RedisConn = conn
	}

	// Build test configuration
	ctx.Config = BuildTestConfig(ctx.KafkaBrokers, ctx.CassandraConn, ctx.PostgresConn)

	ts.logger.Info("Integration test environment ready")
	return ctx, nil
}

// IntegrationTestContext provides context for integration tests
type IntegrationTestContext struct {
	suite         *TestSuite
	logger        *zap.Logger
	KafkaBrokers  []string
	CassandraConn *CassandraConnection
	PostgresConn  *PostgreSQLConnection
	RedisConn     *RedisConnection
	Config        *config.Config
}

// Logger returns the context logger
func (ctx *IntegrationTestContext) Logger() *zap.Logger {
	return ctx.logger
}

// RunPerformanceTest runs a performance test if performance testing is enabled
func (ts *TestSuite) RunPerformanceTest(test ThroughputTest) (*ThroughputResult, error) {
	if !ts.config.PerformanceTests || ts.performance == nil {
		return nil, fmt.Errorf("performance tests are disabled")
	}

	ts.logger.Info("Running performance test", zap.String("name", test.Name))
	result := ts.performance.RunThroughputTest(test)
	ts.logger.Info("Performance test completed",
		zap.String("name", result.Name),
		zap.Float64("ops_per_second", result.OpsPerSecond))

	return result, nil
}

// RunMemoryTest runs a memory test if performance testing is enabled
func (ts *TestSuite) RunMemoryTest(test MemoryTest) (*MemoryResult, error) {
	if !ts.config.PerformanceTests || ts.performance == nil {
		return nil, fmt.Errorf("performance tests are disabled")
	}

	ts.logger.Info("Running memory test", zap.String("name", test.Name))
	result := ts.performance.RunMemoryTest(test)
	ts.logger.Info("Memory test completed",
		zap.String("name", result.Name),
		zap.Uint64("total_allocated", result.TotalAllocated))

	return result, nil
}

// RunLatencyTest runs a latency test if performance testing is enabled
func (ts *TestSuite) RunLatencyTest(test LatencyTest) (*LatencyResult, error) {
	if !ts.config.PerformanceTests || ts.performance == nil {
		return nil, fmt.Errorf("performance tests are disabled")
	}

	ts.logger.Info("Running latency test", zap.String("name", test.Name))
	result := ts.performance.RunLatencyTest(test)
	ts.logger.Info("Latency test completed",
		zap.String("name", result.Name),
		zap.Duration("mean", result.Mean))

	return result, nil
}

// AddCleanup adds a cleanup function to be called when the test suite is cleaned up
func (ts *TestSuite) AddCleanup(fn func() error) {
	ts.cleanup = append(ts.cleanup, fn)
}

// Cleanup cleans up all resources used by the test suite
func (ts *TestSuite) Cleanup() error {
	ts.logger.Info("Cleaning up test suite")

	var errors []error

	// Run custom cleanup functions
	for i := len(ts.cleanup) - 1; i >= 0; i-- {
		if err := ts.cleanup[i](); err != nil {
			errors = append(errors, err)
		}
	}

	// Cleanup containers
	if ts.containers != nil {
		if err := ts.containers.Cleanup(); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("cleanup errors: %v", errors)
	}

	ts.logger.Info("Test suite cleanup completed")
	return nil
}

// Helper methods to determine what containers are needed
func (ts *TestSuite) needsKafka() bool {
	return ts.hasTestTag("kafka") || ts.hasTestTag("integration")
}

func (ts *TestSuite) needsCassandra() bool {
	return ts.hasTestTag("cassandra") || ts.hasTestTag("integration")
}

func (ts *TestSuite) needsPostgreSQL() bool {
	return ts.hasTestTag("postgres") || ts.hasTestTag("integration")
}

func (ts *TestSuite) needsRedis() bool {
	return ts.hasTestTag("redis") || ts.hasTestTag("integration")
}

func (ts *TestSuite) hasTestTag(tag string) bool {
	// Check if the test name or environment suggests we need this container
	testName := ts.t.Name()
	return strings.Contains(strings.ToLower(testName), tag) ||
		strings.Contains(strings.ToLower(os.Getenv("TEST_TAGS")), tag)
}

// TestRunner provides utilities for running different types of tests
type TestRunner struct {
	suite *TestSuite
}

// NewTestRunner creates a new test runner
func NewTestRunner(suite *TestSuite) *TestRunner {
	return &TestRunner{suite: suite}
}

// RunUnitTests runs unit tests with the test suite
func (tr *TestRunner) RunUnitTests(tests map[string]func(*TestSuite)) {
	for name, testFunc := range tests {
		tr.suite.t.Run(name, func(t *testing.T) {
			// Create a sub-suite for this test
			subConfig := *tr.suite.config
			subConfig.UseContainers = false
			subConfig.IntegrationTests = false

			subSuite := NewTestSuite(t, &subConfig)
			testFunc(subSuite)
		})
	}
}

// RunIntegrationTests runs integration tests with containers
func (tr *TestRunner) RunIntegrationTests(tests map[string]func(*TestSuite, *IntegrationTestContext)) {
	if testing.Short() {
		tr.suite.t.Skip("Skipping integration tests in short mode")
	}

	for name, testFunc := range tests {
		tr.suite.t.Run(name, func(t *testing.T) {
			// Create a sub-suite for this test
			subConfig := *tr.suite.config
			subConfig.UseContainers = true
			subConfig.IntegrationTests = true

			subSuite := NewTestSuite(t, &subConfig)
			ctx, err := subSuite.SetupIntegrationTest()
			if err != nil {
				t.Fatalf("Failed to setup integration test: %v", err)
			}

			testFunc(subSuite, ctx)
		})
	}
}

// RunPerformanceTests runs performance tests
func (tr *TestRunner) RunPerformanceTests(tests map[string]func(*TestSuite)) {
	if !tr.suite.config.PerformanceTests {
		tr.suite.t.Skip("Performance tests are disabled")
	}

	for name, testFunc := range tests {
		tr.suite.t.Run(name, func(t *testing.T) {
			// Create a sub-suite for this test
			subConfig := *tr.suite.config
			subConfig.PerformanceTests = true

			subSuite := NewTestSuite(t, &subConfig)
			testFunc(subSuite)
		})
	}
}

package testutil

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"
)

// BenchmarkThroughputTest benchmarks the throughput testing utility
func BenchmarkThroughputTest(b *testing.B) {
	logger := zap.NewNop()
	pt := NewPerformanceTest(b, logger)

	// Simple operation that increments a counter
	counter := 0

	test := ThroughputTest{
		Name:        "SimpleIncrement",
		Duration:    1 * time.Second,
		Concurrency: 10,
		Execute: func(ctx context.Context, data interface{}) error {
			counter++
			return nil
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result := pt.RunThroughputTest(test)
		if result.OpsPerSecond == 0 {
			b.Fatal("No operations performed")
		}
	}
}

// BenchmarkMemoryTest benchmarks the memory testing utility
func BenchmarkMemoryTest(b *testing.B) {
	logger := zap.NewNop()
	pt := NewPerformanceTest(b, logger)

	test := MemoryTest{
		Name: "SimpleAllocation",
		Runs: 1000,
		Execute: func(data interface{}) error {
			// Allocate some memory
			_ = make([]byte, 1024)
			return nil
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result := pt.RunMemoryTest(test)
		if result.TotalAllocated == 0 {
			b.Fatal("No memory allocated")
		}
	}
}

// BenchmarkLatencyTest benchmarks the latency testing utility
func BenchmarkLatencyTest(b *testing.B) {
	logger := zap.NewNop()
	pt := NewPerformanceTest(b, logger)

	test := LatencyTest{
		Name:    "SimpleOperation",
		Samples: 100,
		Warmup:  10,
		Execute: func(data interface{}) error {
			// Simulate some work
			time.Sleep(time.Microsecond)
			return nil
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result := pt.RunLatencyTest(test)
		if result.Mean == 0 {
			b.Fatal("No latency measured")
		}
	}
}

// BenchmarkJSONMarshaling benchmarks JSON marshaling performance
func BenchmarkJSONMarshaling(b *testing.B) {
	mockData := NewMockData()
	event := mockData.GenerateNetworkEvent()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_, err := json.Marshal(event)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkJSONUnmarshaling benchmarks JSON unmarshaling performance
func BenchmarkJSONUnmarshaling(b *testing.B) {
	mockData := NewMockData()
	event := mockData.GenerateNetworkEvent()

	data, err := json.Marshal(event)
	if err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		var result map[string]interface{}
		err := json.Unmarshal(data, &result)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkOCSFEventGeneration benchmarks OCSF event generation
func BenchmarkOCSFEventGeneration(b *testing.B) {
	mockData := NewMockData()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		event := mockData.GenerateOCSFNetworkEvent()
		if event == nil {
			b.Fatal("Failed to generate event")
		}
	}
}

// BenchmarkNetworkEventGeneration benchmarks network event generation
func BenchmarkNetworkEventGeneration(b *testing.B) {
	mockData := NewMockData()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		event := mockData.GenerateNetworkEvent()
		if event == nil {
			b.Fatal("Failed to generate event")
		}
	}
}

// BenchmarkSecurityEventGeneration benchmarks security event generation
func BenchmarkSecurityEventGeneration(b *testing.B) {
	mockData := NewMockData()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		event := mockData.GenerateSecurityEvent()
		if event == nil {
			b.Fatal("Failed to generate event")
		}
	}
}

// BenchmarkMapOperations benchmarks map operations used in testing
func BenchmarkMapOperations(b *testing.B) {
	mockData := NewMockData()
	event := mockData.GenerateNetworkEvent()

	b.Run("MapContainsKey", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = MapContainsKey(event, "timestamp")
		}
	})

	b.Run("MapGetString", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = MapGetString(event, "source_ip")
		}
	})

	b.Run("MapGetInt", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = MapGetInt(event, "source_port")
		}
	})
}

// BenchmarkStringOperations benchmarks string operations used in testing
func BenchmarkStringOperations(b *testing.B) {
	testString := "This is a test string for benchmarking purposes"
	searchTerm := "test"

	b.Run("StringContains", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = StringContains(testString, searchTerm)
		}
	})
}

// BenchmarkMockProducer benchmarks the mock Kafka producer
func BenchmarkMockProducer(b *testing.B) {
	producer := NewMockKafkaProducer()
	producer.On("SendMessage", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

	ctx := context.Background()
	topic := "test-topic"
	key := "test-key"
	value := []byte("test-value")
	headers := map[string]string{"header1": "value1"}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		err := producer.SendMessage(ctx, topic, key, value, headers)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkMockCassandraSession benchmarks the mock Cassandra session
func BenchmarkMockCassandraSession(b *testing.B) {
	session := NewMockCassandraSession()
	session.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return(nil)

	ctx := context.Background()
	statement := "INSERT INTO test_table (id, data) VALUES (?, ?)"
	values := []interface{}{"test-id", "test-data"}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		err := session.Execute(ctx, statement, values...)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkMockOCSFService benchmarks the mock OCSF service
func BenchmarkMockOCSFService(b *testing.B) {
	service := NewMockOCSFService()
	service.On("ValidateEvent", mock.Anything, mock.Anything).Return(true, []string{}, nil)

	ctx := context.Background()
	mockData := NewMockData()
	event := mockData.GenerateOCSFNetworkEvent()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		valid, errors, err := service.ValidateEvent(ctx, event)
		if err != nil {
			b.Fatal(err)
		}
		if !valid {
			b.Fatal("Event should be valid")
		}
		if len(errors) > 0 {
			b.Fatal("Should have no errors")
		}
	}
}

// BenchmarkLargeEventProcessing benchmarks processing of large events
func BenchmarkLargeEventProcessing(b *testing.B) {
	// Generate a large event with many fields
	event := make(map[string]interface{})
	for i := 0; i < 1000; i++ {
		event[fmt.Sprintf("field_%d", i)] = fmt.Sprintf("value_%d", rand.Intn(10000))
	}

	b.Run("JSONMarshal", func(b *testing.B) {
		b.ResetTimer()
		b.ReportAllocs()
		for i := 0; i < b.N; i++ {
			_, err := json.Marshal(event)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("MapOperations", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			for j := 0; j < 100; j++ {
				key := fmt.Sprintf("field_%d", j)
				_ = MapContainsKey(event, key)
			}
		}
	})
}

// BenchmarkConcurrentMockOperations benchmarks concurrent operations on mocks
func BenchmarkConcurrentMockOperations(b *testing.B) {
	producer := NewMockKafkaProducer()
	producer.On("SendMessage", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			topic := fmt.Sprintf("topic-%d", rand.Intn(10))
			key := fmt.Sprintf("key-%d", rand.Intn(1000))
			value := []byte(fmt.Sprintf("value-%d", rand.Intn(10000)))
			headers := map[string]string{
				"header1": fmt.Sprintf("value-%d", rand.Intn(100)),
			}

			err := producer.SendMessage(ctx, topic, key, value, headers)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// Package testutil provides enhanced testing utilities with TestContainers integration
package testutil

import (
	"context"
	"fmt"
	"net"
	"strconv"
	"time"

	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/cassandra"
	"github.com/testcontainers/testcontainers-go/modules/kafka"
	"github.com/testcontainers/testcontainers-go/modules/postgres"
	"github.com/testcontainers/testcontainers-go/modules/redis"
	"github.com/testcontainers/testcontainers-go/wait"
	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
)

// ContainerSuite manages a collection of test containers
type ContainerSuite struct {
	ctx        context.Context
	logger     *zap.Logger
	containers map[string]testcontainers.Container
	cleanup    []func() error
}

// NewContainerSuite creates a new container suite for testing
func NewContainerSuite(ctx context.Context, logger *zap.Logger) *ContainerSuite {
	if logger == nil {
		logger = zap.NewNop()
	}
	
	return &ContainerSuite{
		ctx:        ctx,
		logger:     logger,
		containers: make(map[string]testcontainers.Container),
		cleanup:    make([]func() error, 0),
	}
}

// StartKafka starts a Kafka container and returns connection details
func (cs *ContainerSuite) StartKafka() ([]string, error) {
	cs.logger.Info("Starting Kafka container")
	
	kafkaContainer, err := kafka.Run(cs.ctx,
		"confluentinc/cp-kafka:7.4.0",
		kafka.WithClusterID("test-cluster"),
		testcontainers.WithWaitStrategy(
			wait.ForLog("started (kafka.server.KafkaServer)").
				WithStartupTimeout(60*time.Second),
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to start Kafka container: %w", err)
	}

	cs.containers["kafka"] = kafkaContainer
	cs.cleanup = append(cs.cleanup, func() error {
		return kafkaContainer.Terminate(cs.ctx)
	})

	brokers, err := kafkaContainer.Brokers(cs.ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get Kafka brokers: %w", err)
	}

	cs.logger.Info("Kafka container started", zap.Strings("brokers", brokers))
	return brokers, nil
}

// StartCassandra starts a Cassandra container and returns connection details
func (cs *ContainerSuite) StartCassandra() (*CassandraConnection, error) {
	cs.logger.Info("Starting Cassandra container")
	
	cassandraContainer, err := cassandra.Run(cs.ctx,
		"cassandra:4.1",
		cassandra.WithInitScripts("testdata/cassandra/init.cql"),
		testcontainers.WithWaitStrategy(
			wait.ForLog("Startup complete").
				WithStartupTimeout(120*time.Second),
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to start Cassandra container: %w", err)
	}

	cs.containers["cassandra"] = cassandraContainer
	cs.cleanup = append(cs.cleanup, func() error {
		return cassandraContainer.Terminate(cs.ctx)
	})

	host, err := cassandraContainer.Host(cs.ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get Cassandra host: %w", err)
	}

	port, err := cassandraContainer.MappedPort(cs.ctx, "9042")
	if err != nil {
		return nil, fmt.Errorf("failed to get Cassandra port: %w", err)
	}

	conn := &CassandraConnection{
		Host: host,
		Port: port.Int(),
	}

	cs.logger.Info("Cassandra container started", 
		zap.String("host", conn.Host), 
		zap.Int("port", conn.Port))
	
	return conn, nil
}

// StartPostgreSQL starts a PostgreSQL container and returns connection details
func (cs *ContainerSuite) StartPostgreSQL() (*PostgreSQLConnection, error) {
	cs.logger.Info("Starting PostgreSQL container")
	
	postgresContainer, err := postgres.Run(cs.ctx,
		"postgres:15-alpine",
		postgres.WithDatabase("testdb"),
		postgres.WithUsername("testuser"),
		postgres.WithPassword("testpass"),
		postgres.WithInitScripts("testdata/postgres/init.sql"),
		testcontainers.WithWaitStrategy(
			wait.ForLog("database system is ready to accept connections").
				WithOccurrence(2).
				WithStartupTimeout(60*time.Second),
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to start PostgreSQL container: %w", err)
	}

	cs.containers["postgres"] = postgresContainer
	cs.cleanup = append(cs.cleanup, func() error {
		return postgresContainer.Terminate(cs.ctx)
	})

	host, err := postgresContainer.Host(cs.ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get PostgreSQL host: %w", err)
	}

	port, err := postgresContainer.MappedPort(cs.ctx, "5432")
	if err != nil {
		return nil, fmt.Errorf("failed to get PostgreSQL port: %w", err)
	}

	conn := &PostgreSQLConnection{
		Host:     host,
		Port:     port.Int(),
		Database: "testdb",
		Username: "testuser",
		Password: "testpass",
	}

	cs.logger.Info("PostgreSQL container started", 
		zap.String("host", conn.Host), 
		zap.Int("port", conn.Port))
	
	return conn, nil
}

// StartRedis starts a Redis container and returns connection details
func (cs *ContainerSuite) StartRedis() (*RedisConnection, error) {
	cs.logger.Info("Starting Redis container")
	
	redisContainer, err := redis.Run(cs.ctx,
		"redis:7-alpine",
		redis.WithSnapshotting(10, 1),
		redis.WithLogLevel(redis.LogLevelVerbose),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to start Redis container: %w", err)
	}

	cs.containers["redis"] = redisContainer
	cs.cleanup = append(cs.cleanup, func() error {
		return redisContainer.Terminate(cs.ctx)
	})

	host, err := redisContainer.Host(cs.ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get Redis host: %w", err)
	}

	port, err := redisContainer.MappedPort(cs.ctx, "6379")
	if err != nil {
		return nil, fmt.Errorf("failed to get Redis port: %w", err)
	}

	conn := &RedisConnection{
		Host: host,
		Port: port.Int(),
	}

	cs.logger.Info("Redis container started", 
		zap.String("host", conn.Host), 
		zap.Int("port", conn.Port))
	
	return conn, nil
}

// GetContainer returns a specific container by name
func (cs *ContainerSuite) GetContainer(name string) testcontainers.Container {
	return cs.containers[name]
}

// Cleanup terminates all containers and cleans up resources
func (cs *ContainerSuite) Cleanup() error {
	cs.logger.Info("Cleaning up test containers")
	
	var errors []error
	for i := len(cs.cleanup) - 1; i >= 0; i-- {
		if err := cs.cleanup[i](); err != nil {
			errors = append(errors, err)
		}
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("cleanup errors: %v", errors)
	}
	
	return nil
}

// Connection types for different databases
type CassandraConnection struct {
	Host string
	Port int
}

func (c *CassandraConnection) Address() string {
	return net.JoinHostPort(c.Host, strconv.Itoa(c.Port))
}

type PostgreSQLConnection struct {
	Host     string
	Port     int
	Database string
	Username string
	Password string
}

func (c *PostgreSQLConnection) DSN() string {
	return fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable",
		c.Username, c.Password, c.Host, c.Port, c.Database)
}

type RedisConnection struct {
	Host string
	Port int
}

func (c *RedisConnection) Address() string {
	return net.JoinHostPort(c.Host, strconv.Itoa(c.Port))
}

// BuildTestConfig creates a test configuration using container connections
func BuildTestConfig(kafka []string, cassandra *CassandraConnection, postgres *PostgreSQLConnection) *config.Config {
	cfg := &config.Config{
		Application: config.ApplicationConfig{
			Name:        "test-app",
			Version:     "test",
			Environment: "test",
		},
		Kafka: config.KafkaConfig{
			Brokers:          kafka,
			SecurityProtocol: "PLAINTEXT",
			Producer: config.KafkaProducerConfig{
				Acks:           "all",
				BatchSize:      16384,
				BufferMemory:   33554432,
				Compression:    "snappy",
				MaxMessageSize: 1048576,
			},
			Consumer: config.KafkaConsumerConfig{
				GroupID:           "test-group",
				AutoOffsetReset:   "earliest",
				SessionTimeout:    "30s",
				HeartbeatInterval: "3s",
				MaxPollRecords:    500,
				FetchMinBytes:     1,
			},
		},
		ObjectStorage: config.ObjectStorageConfig{
			Provider:        "mock",
			Bucket:          "test-bucket",
			Region:          "us-east-1",
			AccessKeyID:     "test-key",
			SecretAccessKey: "test-secret",
		},
		Logging: config.LoggingConfig{
			Level:  "debug",
			Format: "json",
			Output: "stderr",
		},
		Metrics: config.MetricsConfig{
			Enabled: false,
			Address: "localhost:8080",
			Path:    "/metrics",
		},
	}

	if cassandra != nil {
		cfg.Cassandra = config.CassandraConfig{
			Hosts:       []string{cassandra.Host},
			Port:        cassandra.Port,
			Username:    "cassandra",
			Password:    "cassandra",
			Keyspace:    "test_keyspace",
			Consistency: "LOCAL_QUORUM",
			Timeout:     "30s",
		}
	}

	if postgres != nil {
		cfg.Postgres = config.PostgresConfig{
			Host:         postgres.Host,
			Port:         postgres.Port,
			Database:     postgres.Database,
			Username:     postgres.Username,
			Password:     postgres.Password,
			SSLMode:      "disable",
			MaxConns:     10,
			MaxIdleConns: 5,
			ConnTimeout:  30 * time.Second,
		}
	}

	return cfg
}

package testutil

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
)

// ExampleTestSuite demonstrates how to use the enhanced testing framework
func ExampleTestSuite() {
	// This example shows how to use the test suite in a real test
	// Note: This is just for documentation, not a real test
}

// TestExampleUnitTest demonstrates unit testing with the framework
func TestExampleUnitTest(t *testing.T) {
	// Create test suite with unit test configuration
	config := DefaultTestConfig()
	config.UseContainers = false
	config.IntegrationTests = false

	suite := NewTestSuite(t, config)

	t.Run("MockKafkaProducer", func(t *testing.T) {
		// Create mock producer
		producer := NewMockKafkaProducer()
		producer.On("SendMessage", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

		// Test sending a message
		ctx := context.Background()
		err := producer.SendMessage(ctx, "test-topic", "test-key", []byte("test-value"), map[string]string{"header": "value"})
		require.NoError(t, err)

		// Verify the message was stored
		messages := producer.GetMessages()
		assert.Len(t, messages, 1)
		assert.Equal(t, "test-topic", messages[0].Topic)
		assert.Equal(t, "test-key", messages[0].Key)
		assert.Equal(t, []byte("test-value"), messages[0].Value)

		producer.AssertExpectations(t)
	})

	t.Run("TestFixtures", func(t *testing.T) {
		fixture := suite.Fixture()

		// Test creating temporary files
		content := `{"test": "data"}`
		filePath := fixture.CreateTempFile("test.json", content)
		assert.FileExists(t, filePath)

		// Test loading test data
		mockData := NewMockData()
		event := mockData.GenerateNetworkEvent()
		_ = fixture.CreateTempJSONFile("event.json", event)

		var loadedEvent map[string]interface{}
		fixture.LoadTestJSON("event.json", &loadedEvent)
		fixture.AssertJSONEqual(event, loadedEvent)
	})

	t.Run("MockData", func(t *testing.T) {
		mockData := NewMockData()

		// Test network event generation
		networkEvent := mockData.GenerateNetworkEvent()
		assert.NotNil(t, networkEvent)
		assert.Contains(t, networkEvent, "source_ip")
		assert.Contains(t, networkEvent, "destination_ip")
		assert.Contains(t, networkEvent, "timestamp")

		// Test OCSF event generation
		ocsfEvent := mockData.GenerateOCSFNetworkEvent()
		assert.NotNil(t, ocsfEvent)
		assert.Equal(t, 4001, MapGetInt(ocsfEvent, "class_uid"))
		assert.Equal(t, 1, MapGetInt(ocsfEvent, "activity_id"))

		// Test security event generation
		securityEvent := mockData.GenerateSecurityEvent()
		assert.NotNil(t, securityEvent)
		assert.Contains(t, securityEvent, "event_type")
		assert.Contains(t, securityEvent, "user_id")
	})
}

// TestExampleIntegrationTest demonstrates integration testing with containers
func TestExampleIntegrationTest(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Create test suite with integration test configuration
	config := DefaultTestConfig()
	config.UseContainers = true
	config.IntegrationTests = true
	config.ContainerTimeout = 120 * time.Second

	suite := NewTestSuite(t, config)

	// Setup integration test environment
	ctx, err := suite.SetupIntegrationTest()
	require.NoError(t, err)
	require.NotNil(t, ctx)

	t.Run("ContainerConnectivity", func(t *testing.T) {
		// Verify Kafka is available
		if len(ctx.KafkaBrokers) > 0 {
			assert.NotEmpty(t, ctx.KafkaBrokers)
			suite.Logger().Info("Kafka brokers available",
				zap.Strings("brokers", ctx.KafkaBrokers))
		}

		// Verify Cassandra is available
		if ctx.CassandraConn != nil {
			assert.NotEmpty(t, ctx.CassandraConn.Host)
			assert.Greater(t, ctx.CassandraConn.Port, 0)
			suite.Logger().Info("Cassandra available",
				zap.String("address", ctx.CassandraConn.Address()))
		}

		// Verify PostgreSQL is available
		if ctx.PostgresConn != nil {
			assert.NotEmpty(t, ctx.PostgresConn.Host)
			assert.Greater(t, ctx.PostgresConn.Port, 0)
			suite.Logger().Info("PostgreSQL available",
				zap.String("dsn", ctx.PostgresConn.DSN()))
		}
	})

	t.Run("ConfigurationGeneration", func(t *testing.T) {
		// Test that configuration is properly generated
		cfg := ctx.Config
		require.NotNil(t, cfg)

		assert.Equal(t, "test-app", cfg.Application.Name)
		assert.Equal(t, "test", cfg.Application.Environment)

		if len(ctx.KafkaBrokers) > 0 {
			assert.Equal(t, ctx.KafkaBrokers, cfg.Kafka.Brokers)
		}

		if ctx.CassandraConn != nil {
			assert.Contains(t, cfg.Cassandra.Hosts, ctx.CassandraConn.Host)
			assert.Equal(t, ctx.CassandraConn.Port, cfg.Cassandra.Port)
		}
	})
}

// BenchmarkExamplePerformanceTest demonstrates performance testing
func BenchmarkExamplePerformanceTest(b *testing.B) {
	// For benchmarks, we create the performance test utility directly
	logger := zap.NewNop()
	pt := NewPerformanceTest(b, logger)

	b.Run("ThroughputTest", func(b *testing.B) {
		// Create a simple throughput test
		counter := 0
		test := ThroughputTest{
			Name:        "SimpleCounter",
			Duration:    1 * time.Second,
			Concurrency: 4,
			Execute: func(ctx context.Context, data interface{}) error {
				counter++
				return nil
			},
		}

		b.ResetTimer()
		result := pt.RunThroughputTest(test)

		b.Logf("Throughput: %.2f ops/sec", result.OpsPerSecond)
		if result.OpsPerSecond == 0 {
			b.Fatal("No operations performed")
		}
	})

	b.Run("MemoryTest", func(b *testing.B) {
		test := MemoryTest{
			Name: "SimpleAllocation",
			Runs: 1000,
			Execute: func(data interface{}) error {
				// Allocate some memory
				_ = make([]byte, 1024)
				return nil
			},
		}

		pt := NewPerformanceTest(b, logger)

		b.ResetTimer()
		result := pt.RunMemoryTest(test)

		b.Logf("Memory allocated: %d bytes", result.TotalAllocated)
		if result.TotalAllocated == 0 {
			b.Fatal("No memory allocated")
		}
	})

	b.Run("LatencyTest", func(b *testing.B) {
		test := LatencyTest{
			Name:    "SimpleOperation",
			Samples: 100,
			Warmup:  10,
			Execute: func(data interface{}) error {
				// Simulate some work
				time.Sleep(time.Microsecond)
				return nil
			},
		}

		pt := NewPerformanceTest(b, logger)

		b.ResetTimer()
		result := pt.RunLatencyTest(test)

		b.Logf("Latency - Mean: %v, P95: %v, P99: %v", result.Mean, result.P95, result.P99)
		if result.Mean == 0 {
			b.Fatal("No latency measured")
		}
	})
}

// TestExampleTestRunner demonstrates using the test runner
func TestExampleTestRunner(t *testing.T) {
	config := DefaultTestConfig()
	suite := NewTestSuite(t, config)
	runner := NewTestRunner(suite)

	// Define unit tests
	unitTests := map[string]func(*TestSuite){
		"MockOperations": func(ts *TestSuite) {
			producer := NewMockKafkaProducer()
			producer.On("SendMessage", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

			ctx := context.Background()
			err := producer.SendMessage(ctx, "test", "key", []byte("value"), nil)
			assert.NoError(ts.t, err)

			messages := producer.GetMessages()
			assert.Len(ts.t, messages, 1)
		},
		"DataGeneration": func(ts *TestSuite) {
			mockData := NewMockData()
			event := mockData.GenerateNetworkEvent()
			assert.NotNil(ts.t, event)

			// Test JSON marshaling
			data, err := json.Marshal(event)
			assert.NoError(ts.t, err)
			assert.NotEmpty(ts.t, data)
		},
	}

	// Run unit tests
	runner.RunUnitTests(unitTests)
}

// TestExampleHelperFunctions demonstrates utility functions
func TestExampleHelperFunctions(t *testing.T) {
	suite := NewTestSuite(t, DefaultTestConfig())
	helper := NewTestHelper(t)

	t.Run("MapOperations", func(t *testing.T) {
		testMap := map[string]interface{}{
			"string_field": "test_value",
			"int_field":    42,
			"float_field":  3.14,
		}

		// Test map operations
		assert.True(t, MapContainsKey(testMap, "string_field"))
		assert.False(t, MapContainsKey(testMap, "missing_field"))

		assert.Equal(t, "test_value", MapGetString(testMap, "string_field"))
		assert.Equal(t, "", MapGetString(testMap, "missing_field"))

		assert.Equal(t, 42, MapGetInt(testMap, "int_field"))
		assert.Equal(t, 0, MapGetInt(testMap, "missing_field"))

		helper.AssertEqual("test_value", MapGetString(testMap, "string_field"))
		helper.AssertEqual(42, MapGetInt(testMap, "int_field"))
	})

	t.Run("StringOperations", func(t *testing.T) {
		testString := "This is a Test String"

		assert.True(t, StringContains(testString, "test"))
		assert.True(t, StringContains(testString, "TEST"))
		assert.False(t, StringContains(testString, "missing"))

		helper.AssertContains(testString, "Test")
	})

	t.Run("EventuallyTrue", func(t *testing.T) {
		fixture := suite.Fixture()

		counter := 0
		condition := func() bool {
			counter++
			return counter >= 3
		}

		// This should succeed after a few iterations
		fixture.AssertEventuallyTrue(condition, 1*time.Second)
		assert.GreaterOrEqual(t, counter, 3)
	})
}

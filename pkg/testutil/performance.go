// Package testutil provides performance testing utilities
package testutil

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"go.uber.org/zap"
)

// PerformanceTest provides utilities for performance testing
type PerformanceTest struct {
	t      *testing.B
	logger *zap.Logger
}

// NewPerformanceTest creates a new performance test utility
func NewPerformanceTest(t *testing.B, logger *zap.Logger) *PerformanceTest {
	if logger == nil {
		logger = zap.NewNop()
	}
	
	return &PerformanceTest{
		t:      t,
		logger: logger,
	}
}

// ThroughputTest measures throughput of a function
type ThroughputTest struct {
	Name        string
	Duration    time.Duration
	Concurrency int
	Setup       func() interface{}
	Execute     func(ctx context.Context, data interface{}) error
	Teardown    func(data interface{})
}

// RunThroughputTest executes a throughput test and reports results
func (pt *PerformanceTest) RunThroughputTest(test ThroughputTest) *ThroughputResult {
	pt.logger.Info("Starting throughput test", 
		zap.String("name", test.Name),
		zap.Duration("duration", test.Duration),
		zap.Int("concurrency", test.Concurrency))
	
	ctx, cancel := context.WithTimeout(context.Background(), test.Duration)
	defer cancel()
	
	var (
		totalOps    int64
		totalErrors int64
		wg          sync.WaitGroup
	)
	
	startTime := time.Now()
	
	// Start concurrent workers
	for i := 0; i < test.Concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			
			// Setup data for this worker
			var data interface{}
			if test.Setup != nil {
				data = test.Setup()
				if test.Teardown != nil {
					defer test.Teardown(data)
				}
			}
			
			for {
				select {
				case <-ctx.Done():
					return
				default:
					if err := test.Execute(ctx, data); err != nil {
						atomic.AddInt64(&totalErrors, 1)
						pt.logger.Debug("Operation failed", 
							zap.Int("worker", workerID),
							zap.Error(err))
					}
					atomic.AddInt64(&totalOps, 1)
				}
			}
		}(i)
	}
	
	wg.Wait()
	actualDuration := time.Since(startTime)
	
	result := &ThroughputResult{
		Name:         test.Name,
		Duration:     actualDuration,
		Concurrency:  test.Concurrency,
		TotalOps:     totalOps,
		TotalErrors:  totalErrors,
		OpsPerSecond: float64(totalOps) / actualDuration.Seconds(),
		ErrorRate:    float64(totalErrors) / float64(totalOps),
	}
	
	pt.logger.Info("Throughput test completed",
		zap.String("name", result.Name),
		zap.Int64("total_ops", result.TotalOps),
		zap.Int64("total_errors", result.TotalErrors),
		zap.Float64("ops_per_second", result.OpsPerSecond),
		zap.Float64("error_rate", result.ErrorRate))
	
	return result
}

// ThroughputResult contains the results of a throughput test
type ThroughputResult struct {
	Name         string
	Duration     time.Duration
	Concurrency  int
	TotalOps     int64
	TotalErrors  int64
	OpsPerSecond float64
	ErrorRate    float64
}

// String returns a string representation of the throughput result
func (tr *ThroughputResult) String() string {
	return fmt.Sprintf("ThroughputResult{Name: %s, OpsPerSecond: %.2f, ErrorRate: %.4f, Duration: %v, Concurrency: %d}",
		tr.Name, tr.OpsPerSecond, tr.ErrorRate, tr.Duration, tr.Concurrency)
}

// MemoryTest measures memory usage of a function
type MemoryTest struct {
	Name     string
	Runs     int
	Setup    func() interface{}
	Execute  func(data interface{}) error
	Teardown func(data interface{})
}

// RunMemoryTest executes a memory test and reports results
func (pt *PerformanceTest) RunMemoryTest(test MemoryTest) *MemoryResult {
	pt.logger.Info("Starting memory test", 
		zap.String("name", test.Name),
		zap.Int("runs", test.Runs))
	
	// Force garbage collection before starting
	runtime.GC()
	runtime.GC() // Call twice to ensure cleanup
	
	var memBefore runtime.MemStats
	runtime.ReadMemStats(&memBefore)
	
	// Run the test
	for i := 0; i < test.Runs; i++ {
		var data interface{}
		if test.Setup != nil {
			data = test.Setup()
		}
		
		if err := test.Execute(data); err != nil {
			pt.logger.Error("Memory test execution failed", 
				zap.String("name", test.Name),
				zap.Int("run", i),
				zap.Error(err))
		}
		
		if test.Teardown != nil {
			test.Teardown(data)
		}
	}
	
	// Force garbage collection after test
	runtime.GC()
	runtime.GC()
	
	var memAfter runtime.MemStats
	runtime.ReadMemStats(&memAfter)
	
	result := &MemoryResult{
		Name:           test.Name,
		Runs:           test.Runs,
		AllocsBefore:   memBefore.Alloc,
		AllocsAfter:    memAfter.Alloc,
		TotalAllocated: memAfter.TotalAlloc - memBefore.TotalAlloc,
		AllocsPerRun:   (memAfter.TotalAlloc - memBefore.TotalAlloc) / uint64(test.Runs),
	}
	
	pt.logger.Info("Memory test completed",
		zap.String("name", result.Name),
		zap.Uint64("total_allocated", result.TotalAllocated),
		zap.Uint64("allocs_per_run", result.AllocsPerRun))
	
	return result
}

// MemoryResult contains the results of a memory test
type MemoryResult struct {
	Name           string
	Runs           int
	AllocsBefore   uint64
	AllocsAfter    uint64
	TotalAllocated uint64
	AllocsPerRun   uint64
}

// String returns a string representation of the memory result
func (mr *MemoryResult) String() string {
	return fmt.Sprintf("MemoryResult{Name: %s, TotalAllocated: %d bytes, AllocsPerRun: %d bytes, Runs: %d}",
		mr.Name, mr.TotalAllocated, mr.AllocsPerRun, mr.Runs)
}

// LatencyTest measures latency distribution of a function
type LatencyTest struct {
	Name        string
	Samples     int
	Warmup      int
	Setup       func() interface{}
	Execute     func(data interface{}) error
	Teardown    func(data interface{})
}

// RunLatencyTest executes a latency test and reports results
func (pt *PerformanceTest) RunLatencyTest(test LatencyTest) *LatencyResult {
	pt.logger.Info("Starting latency test", 
		zap.String("name", test.Name),
		zap.Int("samples", test.Samples),
		zap.Int("warmup", test.Warmup))
	
	latencies := make([]time.Duration, 0, test.Samples)
	
	// Setup data
	var data interface{}
	if test.Setup != nil {
		data = test.Setup()
		if test.Teardown != nil {
			defer test.Teardown(data)
		}
	}
	
	// Warmup runs
	for i := 0; i < test.Warmup; i++ {
		test.Execute(data)
	}
	
	// Actual measurement runs
	for i := 0; i < test.Samples; i++ {
		start := time.Now()
		if err := test.Execute(data); err != nil {
			pt.logger.Debug("Latency test execution failed", 
				zap.String("name", test.Name),
				zap.Int("sample", i),
				zap.Error(err))
			continue
		}
		latency := time.Since(start)
		latencies = append(latencies, latency)
	}
	
	result := calculateLatencyStats(test.Name, latencies)
	
	pt.logger.Info("Latency test completed",
		zap.String("name", result.Name),
		zap.Duration("mean", result.Mean),
		zap.Duration("p50", result.P50),
		zap.Duration("p95", result.P95),
		zap.Duration("p99", result.P99))
	
	return result
}

// LatencyResult contains the results of a latency test
type LatencyResult struct {
	Name    string
	Samples int
	Mean    time.Duration
	Min     time.Duration
	Max     time.Duration
	P50     time.Duration
	P90     time.Duration
	P95     time.Duration
	P99     time.Duration
}

// String returns a string representation of the latency result
func (lr *LatencyResult) String() string {
	return fmt.Sprintf("LatencyResult{Name: %s, Mean: %v, P50: %v, P95: %v, P99: %v, Samples: %d}",
		lr.Name, lr.Mean, lr.P50, lr.P95, lr.P99, lr.Samples)
}

// calculateLatencyStats calculates latency statistics from a slice of durations
func calculateLatencyStats(name string, latencies []time.Duration) *LatencyResult {
	if len(latencies) == 0 {
		return &LatencyResult{Name: name}
	}
	
	// Sort latencies for percentile calculations
	for i := 0; i < len(latencies); i++ {
		for j := i + 1; j < len(latencies); j++ {
			if latencies[i] > latencies[j] {
				latencies[i], latencies[j] = latencies[j], latencies[i]
			}
		}
	}
	
	// Calculate statistics
	var total time.Duration
	for _, lat := range latencies {
		total += lat
	}
	
	result := &LatencyResult{
		Name:    name,
		Samples: len(latencies),
		Mean:    total / time.Duration(len(latencies)),
		Min:     latencies[0],
		Max:     latencies[len(latencies)-1],
		P50:     latencies[len(latencies)*50/100],
		P90:     latencies[len(latencies)*90/100],
		P95:     latencies[len(latencies)*95/100],
		P99:     latencies[len(latencies)*99/100],
	}
	
	return result
}

// BenchmarkHelper provides utilities for Go benchmark tests
type BenchmarkHelper struct {
	b *testing.B
}

// NewBenchmarkHelper creates a new benchmark helper
func NewBenchmarkHelper(b *testing.B) *BenchmarkHelper {
	return &BenchmarkHelper{b: b}
}

// ResetTimer resets the benchmark timer
func (bh *BenchmarkHelper) ResetTimer() {
	bh.b.ResetTimer()
}

// StopTimer stops the benchmark timer
func (bh *BenchmarkHelper) StopTimer() {
	bh.b.StopTimer()
}

// StartTimer starts the benchmark timer
func (bh *BenchmarkHelper) StartTimer() {
	bh.b.StartTimer()
}

// ReportAllocs enables allocation reporting
func (bh *BenchmarkHelper) ReportAllocs() {
	bh.b.ReportAllocs()
}

// SetBytes sets the number of bytes processed per operation
func (bh *BenchmarkHelper) SetBytes(n int64) {
	bh.b.SetBytes(n)
}

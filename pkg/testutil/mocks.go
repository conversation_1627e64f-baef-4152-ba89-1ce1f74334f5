// Package testutil provides mock utilities for testing
package testutil

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/models"
)

// MockKafkaProducer provides a mock Kafka producer for testing
type MockKafkaProducer struct {
	mock.Mock
	messages []MockMessage
	mu       sync.RWMutex
}

// MockMessage represents a message sent to the mock producer
type MockMessage struct {
	Topic     string
	Key       string
	Value     []byte
	Headers   map[string]string
	Timestamp time.Time
}

// NewMockKafkaProducer creates a new mock Kafka producer
func NewMockKafkaProducer() *MockKafkaProducer {
	return &MockKafkaProducer{
		messages: make([]MockMessage, 0),
	}
}

// SendMessage mocks sending a message to <PERSON>f<PERSON>
func (m *MockKafkaProducer) SendMessage(ctx context.Context, topic, key string, value []byte, headers map[string]string) error {
	args := m.Called(ctx, topic, key, value, headers)
	
	// Store the message for verification
	m.mu.Lock()
	m.messages = append(m.messages, MockMessage{
		Topic:     topic,
		Key:       key,
		Value:     value,
		Headers:   headers,
		Timestamp: time.Now(),
	})
	m.mu.Unlock()
	
	return args.Error(0)
}

// GetMessages returns all messages sent to this producer
func (m *MockKafkaProducer) GetMessages() []MockMessage {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	result := make([]MockMessage, len(m.messages))
	copy(result, m.messages)
	return result
}

// GetMessagesForTopic returns messages sent to a specific topic
func (m *MockKafkaProducer) GetMessagesForTopic(topic string) []MockMessage {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	var result []MockMessage
	for _, msg := range m.messages {
		if msg.Topic == topic {
			result = append(result, msg)
		}
	}
	return result
}

// Clear clears all stored messages
func (m *MockKafkaProducer) Clear() {
	m.mu.Lock()
	m.messages = m.messages[:0]
	m.mu.Unlock()
}

// Close mocks closing the producer
func (m *MockKafkaProducer) Close() error {
	args := m.Called()
	return args.Error(0)
}

// MockCassandraSession provides a mock Cassandra session for testing
type MockCassandraSession struct {
	mock.Mock
	queries []MockQuery
	mu      sync.RWMutex
}

// MockQuery represents a query executed on the mock session
type MockQuery struct {
	Statement string
	Values    []interface{}
	Timestamp time.Time
}

// NewMockCassandraSession creates a new mock Cassandra session
func NewMockCassandraSession() *MockCassandraSession {
	return &MockCassandraSession{
		queries: make([]MockQuery, 0),
	}
}

// Execute mocks executing a query
func (m *MockCassandraSession) Execute(ctx context.Context, statement string, values ...interface{}) error {
	args := m.Called(ctx, statement, values)
	
	// Store the query for verification
	m.mu.Lock()
	m.queries = append(m.queries, MockQuery{
		Statement: statement,
		Values:    values,
		Timestamp: time.Now(),
	})
	m.mu.Unlock()
	
	return args.Error(0)
}

// Query mocks querying data
func (m *MockCassandraSession) Query(ctx context.Context, statement string, values ...interface{}) ([]map[string]interface{}, error) {
	args := m.Called(ctx, statement, values)
	
	// Store the query for verification
	m.mu.Lock()
	m.queries = append(m.queries, MockQuery{
		Statement: statement,
		Values:    values,
		Timestamp: time.Now(),
	})
	m.mu.Unlock()
	
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]map[string]interface{}), args.Error(1)
}

// GetQueries returns all queries executed on this session
func (m *MockCassandraSession) GetQueries() []MockQuery {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	result := make([]MockQuery, len(m.queries))
	copy(result, m.queries)
	return result
}

// Clear clears all stored queries
func (m *MockCassandraSession) Clear() {
	m.mu.Lock()
	m.queries = m.queries[:0]
	m.mu.Unlock()
}

// Close mocks closing the session
func (m *MockCassandraSession) Close() error {
	args := m.Called()
	return args.Error(0)
}

// MockOCSFService provides a mock OCSF service for testing
type MockOCSFService struct {
	mock.Mock
	validations []MockValidation
	mu          sync.RWMutex
}

// MockValidation represents a validation performed by the mock service
type MockValidation struct {
	Event     map[string]interface{}
	Result    bool
	Errors    []string
	Timestamp time.Time
}

// NewMockOCSFService creates a new mock OCSF service
func NewMockOCSFService() *MockOCSFService {
	return &MockOCSFService{
		validations: make([]MockValidation, 0),
	}
}

// ValidateEvent mocks validating an OCSF event
func (m *MockOCSFService) ValidateEvent(ctx context.Context, event map[string]interface{}) (bool, []string, error) {
	args := m.Called(ctx, event)
	
	// Store the validation for verification
	m.mu.Lock()
	m.validations = append(m.validations, MockValidation{
		Event:     event,
		Result:    args.Bool(0),
		Errors:    args.Get(1).([]string),
		Timestamp: time.Now(),
	})
	m.mu.Unlock()
	
	return args.Bool(0), args.Get(1).([]string), args.Error(2)
}

// GetValidations returns all validations performed by this service
func (m *MockOCSFService) GetValidations() []MockValidation {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	result := make([]MockValidation, len(m.validations))
	copy(result, m.validations)
	return result
}

// Clear clears all stored validations
func (m *MockOCSFService) Clear() {
	m.mu.Lock()
	m.validations = m.validations[:0]
	m.mu.Unlock()
}

// MockTransformationEngine provides a mock transformation engine for testing
type MockTransformationEngine struct {
	mock.Mock
	transformations []MockTransformation
	mu              sync.RWMutex
}

// MockTransformation represents a transformation performed by the mock engine
type MockTransformation struct {
	Template  string
	Input     map[string]interface{}
	Output    map[string]interface{}
	Timestamp time.Time
}

// NewMockTransformationEngine creates a new mock transformation engine
func NewMockTransformationEngine() *MockTransformationEngine {
	return &MockTransformationEngine{
		transformations: make([]MockTransformation, 0),
	}
}

// Transform mocks transforming data using a JSLT template
func (m *MockTransformationEngine) Transform(ctx context.Context, template string, input map[string]interface{}) (map[string]interface{}, error) {
	args := m.Called(ctx, template, input)
	
	// Store the transformation for verification
	m.mu.Lock()
	m.transformations = append(m.transformations, MockTransformation{
		Template:  template,
		Input:     input,
		Output:    args.Get(0).(map[string]interface{}),
		Timestamp: time.Now(),
	})
	m.mu.Unlock()
	
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

// GetTransformations returns all transformations performed by this engine
func (m *MockTransformationEngine) GetTransformations() []MockTransformation {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	result := make([]MockTransformation, len(m.transformations))
	copy(result, m.transformations)
	return result
}

// Clear clears all stored transformations
func (m *MockTransformationEngine) Clear() {
	m.mu.Lock()
	m.transformations = m.transformations[:0]
	m.mu.Unlock()
}

// MockLogger provides a mock logger for testing
type MockLogger struct {
	mock.Mock
	logs []MockLogEntry
	mu   sync.RWMutex
}

// MockLogEntry represents a log entry
type MockLogEntry struct {
	Level     string
	Message   string
	Fields    map[string]interface{}
	Timestamp time.Time
}

// NewMockLogger creates a new mock logger
func NewMockLogger() *MockLogger {
	return &MockLogger{
		logs: make([]MockLogEntry, 0),
	}
}

// Info mocks logging an info message
func (m *MockLogger) Info(msg string, fields ...zap.Field) {
	m.Called(msg, fields)
	
	fieldMap := make(map[string]interface{})
	for _, field := range fields {
		fieldMap[field.Key] = field.Interface
	}
	
	m.mu.Lock()
	m.logs = append(m.logs, MockLogEntry{
		Level:     "info",
		Message:   msg,
		Fields:    fieldMap,
		Timestamp: time.Now(),
	})
	m.mu.Unlock()
}

// Error mocks logging an error message
func (m *MockLogger) Error(msg string, fields ...zap.Field) {
	m.Called(msg, fields)
	
	fieldMap := make(map[string]interface{})
	for _, field := range fields {
		fieldMap[field.Key] = field.Interface
	}
	
	m.mu.Lock()
	m.logs = append(m.logs, MockLogEntry{
		Level:     "error",
		Message:   msg,
		Fields:    fieldMap,
		Timestamp: time.Now(),
	})
	m.mu.Unlock()
}

// Debug mocks logging a debug message
func (m *MockLogger) Debug(msg string, fields ...zap.Field) {
	m.Called(msg, fields)
	
	fieldMap := make(map[string]interface{})
	for _, field := range fields {
		fieldMap[field.Key] = field.Interface
	}
	
	m.mu.Lock()
	m.logs = append(m.logs, MockLogEntry{
		Level:     "debug",
		Message:   msg,
		Fields:    fieldMap,
		Timestamp: time.Now(),
	})
	m.mu.Unlock()
}

// GetLogs returns all log entries
func (m *MockLogger) GetLogs() []MockLogEntry {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	result := make([]MockLogEntry, len(m.logs))
	copy(result, m.logs)
	return result
}

// GetLogsByLevel returns log entries for a specific level
func (m *MockLogger) GetLogsByLevel(level string) []MockLogEntry {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	var result []MockLogEntry
	for _, log := range m.logs {
		if log.Level == level {
			result = append(result, log)
		}
	}
	return result
}

// Clear clears all stored log entries
func (m *MockLogger) Clear() {
	m.mu.Lock()
	m.logs = m.logs[:0]
	m.mu.Unlock()
}

// MockConfigProvider provides mock configuration for testing
type MockConfigProvider struct {
	config *config.Config
}

// NewMockConfigProvider creates a new mock config provider
func NewMockConfigProvider() *MockConfigProvider {
	return &MockConfigProvider{
		config: &config.Config{
			Application: config.ApplicationConfig{
				Name:        "test-app",
				Version:     "test",
				Environment: "test",
			},
			Logging: config.LoggingConfig{
				Level:  "debug",
				Format: "json",
				Output: "stderr",
			},
			Metrics: config.MetricsConfig{
				Enabled: false,
				Address: "localhost:8080",
				Path:    "/metrics",
			},
		},
	}
}

// GetConfig returns the mock configuration
func (m *MockConfigProvider) GetConfig() *config.Config {
	return m.config
}

// SetConfig sets the mock configuration
func (m *MockConfigProvider) SetConfig(cfg *config.Config) {
	m.config = cfg
}

// WithKafka adds Kafka configuration to the mock config
func (m *MockConfigProvider) WithKafka(brokers []string) *MockConfigProvider {
	m.config.Kafka = config.KafkaConfig{
		Brokers:          brokers,
		SecurityProtocol: "PLAINTEXT",
	}
	return m
}

// WithCassandra adds Cassandra configuration to the mock config
func (m *MockConfigProvider) WithCassandra(hosts []string, port int) *MockConfigProvider {
	m.config.Cassandra = config.CassandraConfig{
		Hosts:       hosts,
		Port:        port,
		Username:    "cassandra",
		Password:    "cassandra",
		Keyspace:    "test_keyspace",
		Consistency: "LOCAL_QUORUM",
		Timeout:     "30s",
	}
	return m
}

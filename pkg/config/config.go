package config

import (
	"fmt"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/spf13/viper"
)

// Config represents the main application configuration
type Config struct {
	// Application metadata
	Application ApplicationConfig `mapstructure:"application" validate:"required"`

	// Logging configuration
	Logging LoggingConfig `mapstructure:"logging" validate:"required"`

	// Database configurations
	Cassandra CassandraConfig `mapstructure:"cassandra" validate:"required"`
	Postgres  PostgresConfig  `mapstructure:"postgres" validate:"required"`

	// Messaging configurations
	Kafka KafkaConfig `mapstructure:"kafka" validate:"required"`

	// Storage configurations
	ObjectStorage ObjectStorageConfig `mapstructure:"object_storage" validate:"required"`

	// API configurations
	ExternalAPI ExternalAPIConfig `mapstructure:"external_api" validate:"required"`

	// Processing configurations
	Omnibus    OmnibusConfig    `mapstructure:"omnibus" validate:"required"`
	Enrichment EnrichmentConfig `mapstructure:"enrichment" validate:"required"`
	Rules      RulesConfig      `mapstructure:"rules" validate:"required"`

	// Observability
	Metrics MetricsConfig `mapstructure:"metrics" validate:"required"`
	Tracing TracingConfig `mapstructure:"tracing" validate:"required"`

	// Archive and Storage
	Archive     ArchiveConfig     `mapstructure:"archive"`
	Storage     StorageConfig     `mapstructure:"storage"`
	Observation ObservationConfig `mapstructure:"observation"`

	// Schema Management
	SchemaReconciler SchemaReconcilerConfig `mapstructure:"schema_reconciler"`

	// OCSF Schema Integration
	OCSFSchemas OCSFSchemasConfig `mapstructure:"ocsf_schemas"`

	// Batch Processing Engine
	Batch BatchConfig `mapstructure:"batch"`

	// Complex Event Processing
	CEP CEPConfig `mapstructure:"cep"`

	// Data Lake Integration
	DataLake DataLakeConfig `mapstructure:"data_lake"`
}

type ApplicationConfig struct {
	Name        string `mapstructure:"name" validate:"required"`
	Version     string `mapstructure:"version" validate:"required"`
	Environment string `mapstructure:"environment" validate:"required,oneof=dev staging prod"`
}

type LoggingConfig struct {
	Level      string `mapstructure:"level" validate:"required,oneof=debug info warn error"`
	Format     string `mapstructure:"format" validate:"required,oneof=json console"`
	OutputPath string `mapstructure:"output_path"`
}

type CassandraConfig struct {
	Hosts       []string      `mapstructure:"hosts" validate:"required,min=1"`
	Port        int           `mapstructure:"port" validate:"required,min=1,max=65535"`
	Username    string        `mapstructure:"username" validate:"required"`
	Password    string        `mapstructure:"password" validate:"required"`
	Keyspace    string        `mapstructure:"keyspace" validate:"required"`
	Consistency string        `mapstructure:"consistency" validate:"required"`
	Timeout     time.Duration `mapstructure:"timeout" validate:"required"`
	Retries     int           `mapstructure:"retries" validate:"min=0"`
}

type PostgresConfig struct {
	Host         string        `mapstructure:"host" validate:"required"`
	Port         int           `mapstructure:"port" validate:"required,min=1,max=65535"`
	Database     string        `mapstructure:"database" validate:"required"`
	Username     string        `mapstructure:"username" validate:"required"`
	Password     string        `mapstructure:"password" validate:"required"`
	SSLMode      string        `mapstructure:"ssl_mode" validate:"required,oneof=disable require verify-ca verify-full"`
	MaxConns     int           `mapstructure:"max_conns" validate:"min=1"`
	MaxIdleConns int           `mapstructure:"max_idle_conns" validate:"min=0"`
	ConnTimeout  time.Duration `mapstructure:"conn_timeout" validate:"required"`
}

type KafkaConfig struct {
	Brokers          []string       `mapstructure:"brokers" validate:"required,min=1"`
	SecurityProtocol string         `mapstructure:"security_protocol" validate:"required,oneof=PLAINTEXT SASL_PLAINTEXT SASL_SSL SSL"`
	SASLMechanism    string         `mapstructure:"sasl_mechanism"`
	SASLUsername     string         `mapstructure:"sasl_username"`
	SASLPassword     string         `mapstructure:"sasl_password"`
	TLS              TLSConfig      `mapstructure:"tls"`
	Producer         ProducerConfig `mapstructure:"producer"`
	Consumer         ConsumerConfig `mapstructure:"consumer"`
}

type TLSConfig struct {
	Enabled            bool   `mapstructure:"enabled"`
	CertFile           string `mapstructure:"cert_file"`
	KeyFile            string `mapstructure:"key_file"`
	CAFile             string `mapstructure:"ca_file"`
	InsecureSkipVerify bool   `mapstructure:"insecure_skip_verify"`
}

type ProducerConfig struct {
	Acks           string        `mapstructure:"acks" validate:"required,oneof=0 1 all"`
	Retries        int           `mapstructure:"retries" validate:"min=0"`
	BatchSize      int           `mapstructure:"batch_size" validate:"min=1"`
	Linger         time.Duration `mapstructure:"linger_ms"`
	BufferMemory   int           `mapstructure:"buffer_memory" validate:"min=1"`
	Compression    string        `mapstructure:"compression" validate:"oneof=none gzip snappy lz4 zstd"`
	MaxMessageSize int           `mapstructure:"max_message_size" validate:"min=1"`
}

type ConsumerConfig struct {
	GroupID            string        `mapstructure:"group_id" validate:"required"`
	AutoOffsetReset    string        `mapstructure:"auto_offset_reset" validate:"required,oneof=earliest latest none"`
	EnableAutoCommit   bool          `mapstructure:"enable_auto_commit"`
	AutoCommitInterval time.Duration `mapstructure:"auto_commit_interval"`
	SessionTimeout     time.Duration `mapstructure:"session_timeout" validate:"required"`
	HeartbeatInterval  time.Duration `mapstructure:"heartbeat_interval" validate:"required"`
	MaxPollRecords     int           `mapstructure:"max_poll_records" validate:"min=1"`
	FetchMinBytes      int           `mapstructure:"fetch_min_bytes" validate:"min=1"`
	FetchMaxWait       time.Duration `mapstructure:"fetch_max_wait"`
}

type ObjectStorageConfig struct {
	Provider        string `mapstructure:"provider" validate:"required,oneof=s3 minio cos"`
	Endpoint        string `mapstructure:"endpoint"`
	Region          string `mapstructure:"region"`
	AccessKeyID     string `mapstructure:"access_key_id" validate:"required"`
	SecretAccessKey string `mapstructure:"secret_access_key" validate:"required"`
	Bucket          string `mapstructure:"bucket" validate:"required"`
	UseSSL          bool   `mapstructure:"use_ssl"`
}

type ExternalAPIConfig struct {
	Address          string          `mapstructure:"address" validate:"required"`
	GRPCPort         int             `mapstructure:"grpc_port" validate:"required,min=1,max=65535"`
	HTTPPort         int             `mapstructure:"http_port" validate:"required,min=1,max=65535"`
	EnableReflection bool            `mapstructure:"enable_reflection"`
	TLS              TLSConfig       `mapstructure:"tls"`
	RateLimit        RateLimitConfig `mapstructure:"rate_limit"`
	Auth             AuthConfig      `mapstructure:"auth"`
}

type RateLimitConfig struct {
	Enabled bool    `mapstructure:"enabled"`
	RPS     float64 `mapstructure:"rps" validate:"min=0"`
	Burst   int     `mapstructure:"burst" validate:"min=0"`
}

type AuthConfig struct {
	Enabled         bool          `mapstructure:"enabled"`
	JWTSecret       string        `mapstructure:"jwt_secret"`
	JWKSUrl         string        `mapstructure:"jwks_url"`
	Issuer          string        `mapstructure:"issuer"`
	Audience        string        `mapstructure:"audience"`
	AccessTokenTTL  time.Duration `mapstructure:"access_token_ttl"`
	RefreshTokenTTL time.Duration `mapstructure:"refresh_token_ttl"`
	PublicKeyPath   string        `mapstructure:"public_key_path"`
	PrivateKeyPath  string        `mapstructure:"private_key_path"`
}

// Additional configuration types for processing modules
type OmnibusConfig struct {
	Enabled            bool                      `mapstructure:"enabled"`
	InputTopics        []string                  `mapstructure:"input_topics" validate:"required,min=1"`
	ActiveStages       []string                  `mapstructure:"active_stages" validate:"required,min=1"`
	Stages             map[string]StageConfig    `mapstructure:"stages" validate:"required"`
	TransformationsDir string                    `mapstructure:"transformations_dir" validate:"required"`
	Resources          map[string]ResourceConfig `mapstructure:"resources"`
	Transformations    []TransformationConfig    `mapstructure:"transformations"`
	Batching           BatchingConfig            `mapstructure:"batching"`
}

type StageConfig struct {
	Type            string                 `mapstructure:"@type" validate:"required"`
	ScriptsDir      string                 `mapstructure:"scripts_dir"`
	SplitChain      bool                   `mapstructure:"split_chain"`
	Transformations []string               `mapstructure:"transformations"`
	Config          map[string]interface{} `mapstructure:",remain"`
}

type ResourceConfig struct {
	Type string `mapstructure:"@type" validate:"required,oneof=path url"`
	Path string `mapstructure:"path"`
	URL  string `mapstructure:"url"`
}

type TransformationConfig struct {
	Name    string                 `mapstructure:"name" validate:"required"`
	Type    string                 `mapstructure:"type" validate:"required"`
	Enabled bool                   `mapstructure:"enabled"`
	Config  map[string]interface{} `mapstructure:"config"`
}

type EnrichmentConfig struct {
	Enabled       bool                              `mapstructure:"enabled"`
	BatchSize     int                               `mapstructure:"batch_size" validate:"min=1"`
	Workers       int                               `mapstructure:"workers" validate:"min=1"`
	Timeout       time.Duration                     `mapstructure:"timeout" validate:"required"`
	RetryAttempts int                               `mapstructure:"retry_attempts" validate:"min=0"`
	Sources       map[string]EnrichmentSourceConfig `mapstructure:"sources"`
}

type EnrichmentSourceConfig struct {
	Enabled   bool              `mapstructure:"enabled"`
	URL       string            `mapstructure:"url"`
	Timeout   time.Duration     `mapstructure:"timeout"`
	RateLimit int               `mapstructure:"rate_limit"`
	APIKey    string            `mapstructure:"api_key"`
	Headers   map[string]string `mapstructure:"headers"`
}

// Specific enrichment source configurations
type BluepagesConfig struct {
	Enabled bool          `mapstructure:"enabled"`
	URL     string        `mapstructure:"url"`
	Timeout time.Duration `mapstructure:"timeout"`
}

type ShodanConfig struct {
	Enabled bool          `mapstructure:"enabled"`
	APIKey  string        `mapstructure:"api_key"`
	URL     string        `mapstructure:"url"`
	Timeout time.Duration `mapstructure:"timeout"`
}

type CrowdstrikeConfig struct {
	Enabled      bool          `mapstructure:"enabled"`
	ClientID     string        `mapstructure:"client_id"`
	ClientSecret string        `mapstructure:"client_secret"`
	BaseURL      string        `mapstructure:"base_url"`
	Timeout      time.Duration `mapstructure:"timeout"`
}

type RulesConfig struct {
	Enabled         bool                     `mapstructure:"enabled"`
	RulesDir        string                   `mapstructure:"rules_dir" validate:"required"`
	BufferSize      int                      `mapstructure:"buffer_size" validate:"min=1"`
	Workers         int                      `mapstructure:"workers" validate:"min=1"`
	ProcessingDelay time.Duration            `mapstructure:"processing_delay"`
	MLModels        map[string]MLModelConfig `mapstructure:"ml_models"`
}

type MLModelConfig struct {
	Enabled   bool    `mapstructure:"enabled"`
	ModelPath string  `mapstructure:"model_path"`
	Threshold float64 `mapstructure:"threshold" validate:"min=0,max=1"`
}

type MetricsConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Address string `mapstructure:"address" validate:"required_if=Enabled true"`
	Path    string `mapstructure:"path"`
}

type TracingConfig struct {
	Enabled     bool    `mapstructure:"enabled"`
	ServiceName string  `mapstructure:"service_name"`
	Endpoint    string  `mapstructure:"endpoint"`
	SampleRate  float64 `mapstructure:"sample_rate" validate:"min=0,max=1"`
}

type ArchiveConfig struct {
	Enabled       bool          `mapstructure:"enabled"`
	BufferSize    int           `mapstructure:"buffer_size" validate:"min=1"`
	FlushInterval time.Duration `mapstructure:"flush_interval" validate:"required"`
	Compression   bool          `mapstructure:"compression"`
	Retention     time.Duration `mapstructure:"retention" validate:"required"`

	// Enhanced archival configuration
	PolicyManager    PolicyManagerConfig    `mapstructure:"policy_manager"`
	ComplianceReport ComplianceReportConfig `mapstructure:"compliance_report"`
	LifecycleManager LifecycleManagerConfig `mapstructure:"lifecycle_manager"`
}

// PolicyManagerConfig holds policy manager configuration
type PolicyManagerConfig struct {
	Enabled             bool          `mapstructure:"enabled"`
	PolicyCheckInterval time.Duration `mapstructure:"policy_check_interval"`
	DefaultRetention    time.Duration `mapstructure:"default_retention"`
	PoliciesPath        string        `mapstructure:"policies_path"`
}

// ComplianceReportConfig holds compliance reporting configuration
type ComplianceReportConfig struct {
	Enabled        bool          `mapstructure:"enabled"`
	ReportInterval time.Duration `mapstructure:"report_interval"`
	ReportsPath    string        `mapstructure:"reports_path"`
	Regulations    []string      `mapstructure:"regulations"`
	AlertThreshold int           `mapstructure:"alert_threshold"`
}

// LifecycleManagerConfig holds lifecycle manager configuration
type LifecycleManagerConfig struct {
	Enabled            bool          `mapstructure:"enabled"`
	CleanupInterval    time.Duration `mapstructure:"cleanup_interval"`
	MetadataPath       string        `mapstructure:"metadata_path"`
	StorageTiers       []string      `mapstructure:"storage_tiers"`
	CompressionEnabled bool          `mapstructure:"compression_enabled"`
}

type StorageConfig struct {
	Type   string `mapstructure:"type" validate:"required,oneof=s3 minio cos"`
	Bucket string `mapstructure:"bucket" validate:"required"`
	Region string `mapstructure:"region"`
}

type ObservationConfig struct {
	Workers    int `mapstructure:"workers" validate:"min=1"`
	BufferSize int `mapstructure:"buffer_size" validate:"min=1"`
	BatchSize  int `mapstructure:"batch_size" validate:"min=1"`
}

// BatchingConfig configures the log batching system for Cassandra writes
type BatchingConfig struct {
	Enabled        bool          `mapstructure:"enabled"`
	BatchSize      int           `mapstructure:"batch_size" validate:"min=1"`
	FlushInterval  time.Duration `mapstructure:"flush_interval" validate:"required"`
	MaxMemoryBytes int64         `mapstructure:"max_memory_bytes" validate:"min=1"`
	Workers        int           `mapstructure:"workers" validate:"min=1"`
	QueueDepth     int           `mapstructure:"queue_depth" validate:"min=1"`
	RetryAttempts  int           `mapstructure:"retry_attempts" validate:"min=0"`
	RetryDelay     time.Duration `mapstructure:"retry_delay"`
	EnableDLQ      bool          `mapstructure:"enable_dlq"`
	DLQTopic       string        `mapstructure:"dlq_topic"`
}

// LoadConfig loads configuration from file and environment variables
func LoadConfig(configFile string) (*Config, error) {
	v := viper.New()

	// Set config file
	if configFile != "" {
		v.SetConfigFile(configFile)
	} else {
		v.SetConfigName("config")
		v.SetConfigType("yaml")
		v.AddConfigPath(".")
		v.AddConfigPath("./configs")
		v.AddConfigPath("/etc/gollmslake")
	}

	// Enable environment variable support
	v.AutomaticEnv()
	v.SetEnvPrefix("GOLLMSLAKE")

	// Read config file
	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// Unmarshal config
	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Set defaults for batching if enabled
	config.setBatchingDefaults()

	// Validate config
	validate := validator.New()
	if err := validate.Struct(&config); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return &config, nil
}

// setBatchingDefaults sets default values for batching configuration
func (c *Config) setBatchingDefaults() {
	if !c.Omnibus.Batching.Enabled {
		return
	}

	b := &c.Omnibus.Batching

	if b.BatchSize == 0 {
		b.BatchSize = 100
	}
	if b.FlushInterval == 0 {
		b.FlushInterval = 1 * time.Second
	}
	if b.MaxMemoryBytes == 0 {
		b.MaxMemoryBytes = 10 * 1024 * 1024 // 10MB
	}
	if b.Workers == 0 {
		b.Workers = 2
	}
	if b.QueueDepth == 0 {
		b.QueueDepth = 100
	}
	if b.RetryAttempts == 0 {
		b.RetryAttempts = 3
	}
	if b.RetryDelay == 0 {
		b.RetryDelay = 100 * time.Millisecond
	}
	if b.EnableDLQ && b.DLQTopic == "" {
		b.DLQTopic = "log-storage-dlq"
	}
}

// SchemaReconcilerConfig configures the schema management service
type SchemaReconcilerConfig struct {
	Enabled           bool                             `mapstructure:"enabled"`
	ReconcileInterval time.Duration                    `mapstructure:"reconcile_interval" validate:"required"`
	Kubernetes        KubernetesConfig                 `mapstructure:"kubernetes"`
	Cassandra         SchemaReconcilerCassandraConfig  `mapstructure:"cassandra"`
	OpenSearch        SchemaReconcilerOpenSearchConfig `mapstructure:"opensearch"`
	DryRun            bool                             `mapstructure:"dry_run"`
	BackupEnabled     bool                             `mapstructure:"backup_enabled"`
}

type KubernetesConfig struct {
	InCluster     bool   `mapstructure:"in_cluster"`
	ConfigPath    string `mapstructure:"config_path"`
	Namespace     string `mapstructure:"namespace" validate:"required"`
	LabelSelector string `mapstructure:"label_selector"`
}

type SchemaReconcilerCassandraConfig struct {
	SchemaPath        string        `mapstructure:"schema_path" validate:"required"`
	MigrationTimeout  time.Duration `mapstructure:"migration_timeout" validate:"required"`
	BackupKeyspace    string        `mapstructure:"backup_keyspace"`
	ReplicationFactor int           `mapstructure:"replication_factor" validate:"min=1"`
}

type SchemaReconcilerOpenSearchConfig struct {
	IndexTemplatesPath string        `mapstructure:"index_templates_path" validate:"required"`
	MigrationTimeout   time.Duration `mapstructure:"migration_timeout" validate:"required"`
	BackupIndices      bool          `mapstructure:"backup_indices"`
}

// OCSFSchemasConfig configures OCSF schema integration
type OCSFSchemasConfig struct {
	Enabled         bool          `mapstructure:"enabled"`
	SchemasPath     string        `mapstructure:"schemas_path" validate:"required"`
	Version         string        `mapstructure:"version" validate:"required"`
	CacheEnabled    bool          `mapstructure:"cache_enabled"`
	CacheTTL        time.Duration `mapstructure:"cache_ttl"`
	ValidationMode  string        `mapstructure:"validation_mode" validate:"oneof=strict lenient disabled"`
	CustomSchemas   []string      `mapstructure:"custom_schemas"`
	RefreshInterval time.Duration `mapstructure:"refresh_interval"`
}

// CEPConfig configures Complex Event Processing
type CEPConfig struct {
	Enabled                    bool          `mapstructure:"enabled"`
	BufferSize                 int           `mapstructure:"buffer_size" validate:"min=1"`
	PatternsDir                string        `mapstructure:"patterns_dir"`
	CorrelationEnabled         bool          `mapstructure:"correlation_enabled"`
	CorrelationWindow          time.Duration `mapstructure:"correlation_window"`
	MaxCorrelationGroups       int           `mapstructure:"max_correlation_groups" validate:"min=1"`
	CorrelationThreshold       float64       `mapstructure:"correlation_threshold" validate:"min=0,max=1"`
	CorrelationKeys            []string      `mapstructure:"correlation_keys"`
	ThreatDetectionEnabled     bool          `mapstructure:"threat_detection_enabled"`
	DDoSDetectionEnabled       bool          `mapstructure:"ddos_detection_enabled"`
	BruteForceDetectionEnabled bool          `mapstructure:"brute_force_detection_enabled"`
	AnomalyDetectionEnabled    bool          `mapstructure:"anomaly_detection_enabled"`
	ThreatUpdateInterval       time.Duration `mapstructure:"threat_update_interval"`
}

// BatchConfig configures the batch processing engine
type BatchConfig struct {
	Enabled           bool          `mapstructure:"enabled"`
	MaxWorkers        int           `mapstructure:"max_workers" validate:"min=1"`
	QueueSize         int           `mapstructure:"queue_size" validate:"min=1"`
	JobTimeout        time.Duration `mapstructure:"job_timeout"`
	RetryAttempts     int           `mapstructure:"retry_attempts" validate:"min=0"`
	RetryDelay        time.Duration `mapstructure:"retry_delay"`
	CleanupInterval   time.Duration `mapstructure:"cleanup_interval"`
	MetricsInterval   time.Duration `mapstructure:"metrics_interval"`
	CheckpointEnabled bool          `mapstructure:"checkpoint_enabled"`
	CheckpointDir     string        `mapstructure:"checkpoint_dir"`
}

// DataLakeConfig configures the data lake integration
type DataLakeConfig struct {
	Enabled bool          `mapstructure:"enabled"`
	Catalog CatalogConfig `mapstructure:"catalog" validate:"required"`
}

// CatalogConfig configures the Iceberg catalog
type CatalogConfig struct {
	Type       string            `mapstructure:"type" validate:"required,oneof=rest glue sql"`
	URI        string            `mapstructure:"uri"`
	Properties map[string]string `mapstructure:"properties"`
}

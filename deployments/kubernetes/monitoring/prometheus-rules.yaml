# Prometheus Monitoring Rules for Data Lake Integration Service
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: gollmslake-datalake-monitor
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
    monitoring: prometheus
spec:
  selector:
    matchLabels:
      app: gollmslake
      component: datalake
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    scheme: https
    tlsConfig:
      insecureSkipVerify: false
      serverName: gollmslake-datalake-service.gollmslake-prod.svc.cluster.local
    honorLabels: true
    scrapeTimeout: 10s
  namespaceSelector:
    matchNames:
    - gollmslake-prod
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: gollmslake-datalake-alerts
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
    monitoring: prometheus
spec:
  groups:
  - name: gollmslake.datalake.availability
    interval: 30s
    rules:
    - alert: GollmslakeDataLakeDown
      expr: up{job="gollmslake-datalake"} == 0
      for: 1m
      labels:
        severity: critical
        service: gollmslake-datalake
        team: platform
      annotations:
        summary: "Gollmslake Data Lake service is down"
        description: "Gollmslake Data Lake service {{ $labels.instance }} has been down for more than 1 minute"
        runbook_url: "https://wiki.company.com/runbooks/gollmslake-datalake-down"
        dashboard_url: "https://grafana.company.com/d/gollmslake-datalake"
    
    - alert: GollmslakeDataLakeHighErrorRate
      expr: |
        (
          rate(http_requests_total{job="gollmslake-datalake",status=~"5.."}[5m]) /
          rate(http_requests_total{job="gollmslake-datalake"}[5m])
        ) * 100 > 5
      for: 5m
      labels:
        severity: warning
        service: gollmslake-datalake
        team: platform
      annotations:
        summary: "High error rate in Gollmslake Data Lake"
        description: "Error rate is {{ $value | humanizePercentage }} for instance {{ $labels.instance }}"
        runbook_url: "https://wiki.company.com/runbooks/gollmslake-high-error-rate"
    
    - alert: GollmslakeDataLakeCriticalErrorRate
      expr: |
        (
          rate(http_requests_total{job="gollmslake-datalake",status=~"5.."}[5m]) /
          rate(http_requests_total{job="gollmslake-datalake"}[5m])
        ) * 100 > 15
      for: 2m
      labels:
        severity: critical
        service: gollmslake-datalake
        team: platform
      annotations:
        summary: "Critical error rate in Gollmslake Data Lake"
        description: "Error rate is {{ $value | humanizePercentage }} for instance {{ $labels.instance }}"
        runbook_url: "https://wiki.company.com/runbooks/gollmslake-critical-error-rate"

  - name: gollmslake.datalake.performance
    interval: 30s
    rules:
    - alert: GollmslakeDataLakeHighLatency
      expr: |
        histogram_quantile(0.95, 
          rate(http_request_duration_seconds_bucket{job="gollmslake-datalake"}[5m])
        ) > 2
      for: 5m
      labels:
        severity: warning
        service: gollmslake-datalake
        team: platform
      annotations:
        summary: "High latency in Gollmslake Data Lake"
        description: "95th percentile latency is {{ $value }}s for instance {{ $labels.instance }}"
        runbook_url: "https://wiki.company.com/runbooks/gollmslake-high-latency"
    
    - alert: GollmslakeDataLakeHighCPU
      expr: |
        (
          rate(container_cpu_usage_seconds_total{pod=~"gollmslake-datalake-.*"}[5m]) * 100
        ) > 80
      for: 10m
      labels:
        severity: warning
        service: gollmslake-datalake
        team: platform
      annotations:
        summary: "High CPU usage in Gollmslake Data Lake"
        description: "CPU usage is {{ $value | humanizePercentage }} for pod {{ $labels.pod }}"
        runbook_url: "https://wiki.company.com/runbooks/gollmslake-high-cpu"
    
    - alert: GollmslakeDataLakeHighMemory
      expr: |
        (
          container_memory_usage_bytes{pod=~"gollmslake-datalake-.*"} /
          container_spec_memory_limit_bytes{pod=~"gollmslake-datalake-.*"}
        ) * 100 > 85
      for: 10m
      labels:
        severity: warning
        service: gollmslake-datalake
        team: platform
      annotations:
        summary: "High memory usage in Gollmslake Data Lake"
        description: "Memory usage is {{ $value | humanizePercentage }} for pod {{ $labels.pod }}"
        runbook_url: "https://wiki.company.com/runbooks/gollmslake-high-memory"

  - name: gollmslake.datalake.business
    interval: 60s
    rules:
    - alert: GollmslakeDataLakeWriteFailures
      expr: |
        rate(datalake_write_operations_total{status="failed"}[10m]) > 0.1
      for: 5m
      labels:
        severity: warning
        service: gollmslake-datalake
        team: platform
      annotations:
        summary: "High write failure rate in Data Lake"
        description: "Write failure rate is {{ $value }} failures/sec for instance {{ $labels.instance }}"
        runbook_url: "https://wiki.company.com/runbooks/datalake-write-failures"
    
    - alert: GollmslakeDataLakeReadFailures
      expr: |
        rate(datalake_read_operations_total{status="failed"}[10m]) > 0.1
      for: 5m
      labels:
        severity: warning
        service: gollmslake-datalake
        team: platform
      annotations:
        summary: "High read failure rate in Data Lake"
        description: "Read failure rate is {{ $value }} failures/sec for instance {{ $labels.instance }}"
        runbook_url: "https://wiki.company.com/runbooks/datalake-read-failures"
    
    - alert: GollmslakeDataLakeTableCreationFailures
      expr: |
        rate(datalake_table_operations_total{operation="create",status="failed"}[10m]) > 0.01
      for: 5m
      labels:
        severity: critical
        service: gollmslake-datalake
        team: platform
      annotations:
        summary: "Table creation failures in Data Lake"
        description: "Table creation failure rate is {{ $value }} failures/sec for instance {{ $labels.instance }}"
        runbook_url: "https://wiki.company.com/runbooks/datalake-table-failures"

  - name: gollmslake.datalake.dependencies
    interval: 60s
    rules:
    - alert: GollmslakeDataLakeCassandraConnectionFailure
      expr: |
        cassandra_connection_errors_total > 0
      for: 2m
      labels:
        severity: critical
        service: gollmslake-datalake
        team: platform
      annotations:
        summary: "Cassandra connection failures"
        description: "Cassandra connection errors detected for instance {{ $labels.instance }}"
        runbook_url: "https://wiki.company.com/runbooks/cassandra-connection-failure"
    
    - alert: GollmslakeDataLakeKafkaConnectionFailure
      expr: |
        kafka_connection_errors_total > 0
      for: 2m
      labels:
        severity: critical
        service: gollmslake-datalake
        team: platform
      annotations:
        summary: "Kafka connection failures"
        description: "Kafka connection errors detected for instance {{ $labels.instance }}"
        runbook_url: "https://wiki.company.com/runbooks/kafka-connection-failure"
    
    - alert: GollmslakeDataLakeS3ConnectionFailure
      expr: |
        s3_connection_errors_total > 0
      for: 2m
      labels:
        severity: critical
        service: gollmslake-datalake
        team: platform
      annotations:
        summary: "S3 connection failures"
        description: "S3 connection errors detected for instance {{ $labels.instance }}"
        runbook_url: "https://wiki.company.com/runbooks/s3-connection-failure"

  - name: gollmslake.datalake.security
    interval: 60s
    rules:
    - alert: GollmslakeDataLakeAuthenticationFailures
      expr: |
        rate(http_requests_total{job="gollmslake-datalake",status="401"}[5m]) > 0.1
      for: 5m
      labels:
        severity: warning
        service: gollmslake-datalake
        team: security
      annotations:
        summary: "High authentication failure rate"
        description: "Authentication failure rate is {{ $value }} failures/sec for instance {{ $labels.instance }}"
        runbook_url: "https://wiki.company.com/runbooks/authentication-failures"
    
    - alert: GollmslakeDataLakeAuthorizationFailures
      expr: |
        rate(http_requests_total{job="gollmslake-datalake",status="403"}[5m]) > 0.05
      for: 5m
      labels:
        severity: warning
        service: gollmslake-datalake
        team: security
      annotations:
        summary: "High authorization failure rate"
        description: "Authorization failure rate is {{ $value }} failures/sec for instance {{ $labels.instance }}"
        runbook_url: "https://wiki.company.com/runbooks/authorization-failures"
    
    - alert: GollmslakeDataLakeSuspiciousActivity
      expr: |
        rate(http_requests_total{job="gollmslake-datalake",status=~"4.."}[5m]) > 1
      for: 10m
      labels:
        severity: warning
        service: gollmslake-datalake
        team: security
      annotations:
        summary: "Suspicious activity detected"
        description: "High rate of 4xx errors: {{ $value }} errors/sec for instance {{ $labels.instance }}"
        runbook_url: "https://wiki.company.com/runbooks/suspicious-activity"

  - name: gollmslake.datalake.sla
    interval: 300s
    rules:
    - alert: GollmslakeDataLakeSLABreach
      expr: |
        (
          (
            rate(http_requests_total{job="gollmslake-datalake",status=~"2.."}[5m]) +
            rate(http_requests_total{job="gollmslake-datalake",status=~"3.."}[5m])
          ) /
          rate(http_requests_total{job="gollmslake-datalake"}[5m])
        ) * 100 < 99.9
      for: 15m
      labels:
        severity: critical
        service: gollmslake-datalake
        team: platform
        sla: "99.9"
      annotations:
        summary: "SLA breach detected"
        description: "Service availability is {{ $value | humanizePercentage }}, below 99.9% SLA"
        runbook_url: "https://wiki.company.com/runbooks/sla-breach"
        dashboard_url: "https://grafana.company.com/d/gollmslake-sla"

# Production Kubernetes Deployment for Data Lake Integration Service
apiVersion: v1
kind: Namespace
metadata:
  name: gollmslake-prod
  labels:
    name: gollmslake-prod
    environment: production
    app: gollmslake
  annotations:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: gollmslake-quota
  namespace: gollmslake-prod
spec:
  hard:
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: gollmslake-service-account
  namespace: gollmslake-prod
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT_ID:role/GollmslakeServiceRole
  labels:
    app: gollmslake
    component: datalake
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: gollmslake-role
  namespace: gollmslake-prod
rules:
- apiGroups: [""]
  resources: ["secrets", "configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: gollmslake-role-binding
  namespace: gollmslake-prod
subjects:
- kind: ServiceAccount
  name: gollmslake-service-account
  namespace: gollmslake-prod
roleRef:
  kind: Role
  name: gollmslake-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gollmslake-logs-pvc
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: gollmslake-ssd
  resources:
    requests:
      storage: 100Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gollmslake-checkpoints-pvc
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: gollmslake-ssd
  resources:
    requests:
      storage: 50Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gollmslake-datalake
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: gollmslake
      component: datalake
  template:
    metadata:
      labels:
        app: gollmslake
        component: datalake
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
        config.checksum: "REPLACE_WITH_CONFIG_CHECKSUM"
    spec:
      serviceAccountName: gollmslake-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: gollmslake-datalake
        image: gollmslake/datalake:v1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: grpc
          protocol: TCP
        - containerPort: 8081
          name: http
          protocol: TCP
        - containerPort: 9090
          name: metrics
          protocol: TCP
        env:
        - name: CONFIG_FILE
          value: "/etc/gollmslake/datalake-production.yaml"
        - name: ENVIRONMENT
          value: "production"
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        # Database credentials from secrets
        - name: CASSANDRA_USERNAME
          valueFrom:
            secretKeyRef:
              name: gollmslake-database-secrets
              key: cassandra-username
        - name: CASSANDRA_PASSWORD
          valueFrom:
            secretKeyRef:
              name: gollmslake-database-secrets
              key: cassandra-password
        - name: POSTGRES_USERNAME
          valueFrom:
            secretKeyRef:
              name: gollmslake-database-secrets
              key: postgres-username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: gollmslake-database-secrets
              key: postgres-password
        # Messaging credentials
        - name: KAFKA_USERNAME
          valueFrom:
            secretKeyRef:
              name: gollmslake-messaging-secrets
              key: kafka-username
        - name: KAFKA_PASSWORD
          valueFrom:
            secretKeyRef:
              name: gollmslake-messaging-secrets
              key: kafka-password
        # Storage credentials
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: gollmslake-storage-secrets
              key: aws-access-key-id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: gollmslake-storage-secrets
              key: aws-secret-access-key
        # Authentication secrets
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: gollmslake-auth-secrets
              key: jwt-secret
        volumeMounts:
        - name: config-volume
          mountPath: /etc/gollmslake
          readOnly: true
        - name: security-config-volume
          mountPath: /etc/gollmslake/security
          readOnly: true
        - name: tls-volume
          mountPath: /etc/ssl/certs
          readOnly: true
        - name: logs-volume
          mountPath: /var/log/gollmslake
        - name: checkpoints-volume
          mountPath: /var/lib/gollmslake/checkpoints
        - name: tmp-volume
          mountPath: /tmp
        resources:
          requests:
            cpu: 4
            memory: 8Gi
            ephemeral-storage: 2Gi
          limits:
            cpu: 8
            memory: 16Gi
            ephemeral-storage: 4Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8081
            scheme: HTTPS
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: /ready
            port: 8081
            scheme: HTTPS
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        startupProbe:
          httpGet:
            path: /health
            port: 8081
            scheme: HTTPS
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
          successThreshold: 1
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 65534
          runAsGroup: 65534
          capabilities:
            drop:
            - ALL
          seccompProfile:
            type: RuntimeDefault
      volumes:
      - name: config-volume
        configMap:
          name: gollmslake-config
          defaultMode: 0444
      - name: security-config-volume
        configMap:
          name: gollmslake-security-config
          defaultMode: 0444
      - name: tls-volume
        secret:
          secretName: gollmslake-tls-secret
          defaultMode: 0444
      - name: logs-volume
        persistentVolumeClaim:
          claimName: gollmslake-logs-pvc
      - name: checkpoints-volume
        persistentVolumeClaim:
          claimName: gollmslake-checkpoints-pvc
      - name: tmp-volume
        emptyDir:
          sizeLimit: 1Gi
      nodeSelector:
        kubernetes.io/arch: amd64
        node-type: compute-optimized
      tolerations:
      - key: "dedicated"
        operator: "Equal"
        value: "gollmslake"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - gollmslake
                - key: component
                  operator: In
                  values:
                  - datalake
              topologyKey: kubernetes.io/hostname
      terminationGracePeriodSeconds: 60
      dnsPolicy: ClusterFirst
      restartPolicy: Always

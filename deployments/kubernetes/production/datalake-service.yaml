# Production Kubernetes Service and Ingress for Data Lake Integration Service
apiVersion: v1
kind: Service
metadata:
  name: gollmslake-datalake-service
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/metrics"
spec:
  selector:
    app: gollmslake
    component: datalake
  ports:
  - name: grpc
    port: 8080
    targetPort: 8080
    protocol: TCP
  - name: http
    port: 8081
    targetPort: 8081
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: 9090
    protocol: TCP
  type: ClusterIP
  sessionAffinity: None
---
apiVersion: v1
kind: Service
metadata:
  name: gollmslake-datalake-headless
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
  annotations:
    service.alpha.kubernetes.io/tolerate-unready-endpoints: "true"
spec:
  selector:
    app: gollmslake
    component: datalake
  ports:
  - name: grpc
    port: 8080
    targetPort: 8080
    protocol: TCP
  - name: http
    port: 8081
    targetPort: 8081
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: 9090
    protocol: TCP
  type: ClusterIP
  clusterIP: None
  publishNotReadyAddresses: true
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gollmslake-datalake-ingress
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
  annotations:
    # Ingress class and SSL
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    
    # Certificate management
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    cert-manager.io/acme-challenge-type: "http01"
    
    # Security headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
      add_header X-Frame-Options "DENY" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Referrer-Policy "strict-origin-when-cross-origin" always;
      add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'" always;
    
    # Rate limiting
    nginx.ingress.kubernetes.io/rate-limit: "1000"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/rate-limit-connections: "100"
    
    # Timeouts and buffering
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/client-body-buffer-size: "1m"
    
    # Load balancing
    nginx.ingress.kubernetes.io/upstream-hash-by: "$remote_addr"
    nginx.ingress.kubernetes.io/load-balance: "round_robin"
    
    # Monitoring
    nginx.ingress.kubernetes.io/enable-access-log: "true"
    nginx.ingress.kubernetes.io/enable-rewrite-log: "true"
spec:
  tls:
  - hosts:
    - datalake.company.com
    - api.datalake.company.com
    secretName: gollmslake-tls-cert
  rules:
  - host: datalake.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gollmslake-datalake-service
            port:
              number: 8081
  - host: api.datalake.company.com
    http:
      paths:
      - path: /api/v1
        pathType: Prefix
        backend:
          service:
            name: gollmslake-datalake-service
            port:
              number: 8081
      - path: /grpc
        pathType: Prefix
        backend:
          service:
            name: gollmslake-datalake-service
            port:
              number: 8080
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: gollmslake-datalake-network-policy
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
spec:
  podSelector:
    matchLabels:
      app: gollmslake
      component: datalake
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from nginx ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 8081
  # Allow ingress from monitoring namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
  # Allow ingress from same namespace (for service discovery)
  - from:
    - namespaceSelector:
        matchLabels:
          name: gollmslake-prod
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 8081
    - protocol: TCP
      port: 9090
  egress:
  # Allow egress to DNS
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow egress to HTTPS (for external APIs, S3, etc.)
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow egress to Cassandra
  - to: []
    ports:
    - protocol: TCP
      port: 9042
  # Allow egress to PostgreSQL
  - to: []
    ports:
    - protocol: TCP
      port: 5432
  # Allow egress to Kafka
  - to: []
    ports:
    - protocol: TCP
      port: 9092
    - protocol: TCP
      port: 9093
    - protocol: TCP
      port: 9094
  # Allow egress to Iceberg REST catalog
  - to: []
    ports:
    - protocol: TCP
      port: 8181
  # Allow egress to Jaeger (tracing)
  - to: []
    ports:
    - protocol: TCP
      port: 14268
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: gollmslake-datalake-pdb
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: gollmslake
      component: datalake
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: gollmslake-datalake-hpa
  namespace: gollmslake-prod
  labels:
    app: gollmslake
    component: datalake
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: gollmslake-datalake
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "1000"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      - type: Pods
        value: 1
        periodSeconds: 60
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max

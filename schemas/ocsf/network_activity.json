{"name": "network_activity", "version": "1.3.0", "description": "Network Activity events describe network connection and traffic activity.", "category": "network", "class_uid": 4001, "type": "object", "properties": {"activity_id": {"type": "integer", "description": "The normalized identifier of the activity that triggered the event.", "enum": [0, 1, 2, 3, 4, 5, 6, 99], "metadata": {"0": "Unknown", "1": "Allow", "2": "<PERSON><PERSON>", "3": "RDP", "4": "SMB", "5": "SSH", "6": "Other", "99": "Other"}}, "activity_name": {"type": "string", "description": "The event activity name, as defined by the activity_id."}, "category_name": {"type": "string", "description": "The event category name, as defined by category_uid value: Network Activity."}, "category_uid": {"type": "integer", "description": "The category unique identifier of the event.", "const": 4}, "class_name": {"type": "string", "description": "The event class name, as defined by class_uid value: Network Activity."}, "class_uid": {"type": "integer", "description": "The unique identifier of a class. A Class describes the attributes available in an event.", "const": 4001}, "connection_info": {"type": "object", "description": "The network connection information.", "properties": {"boundary": {"type": "string", "description": "The boundary of the connection, normalized to one of the values: Internal, External, Unknown."}, "boundary_id": {"type": "integer", "description": "The normalized boundary ID of the connection.", "enum": [0, 1, 2, 99]}, "direction": {"type": "string", "description": "The direction of the network traffic: Inbound, Outbound, Lateral, Unknown."}, "direction_id": {"type": "integer", "description": "The normalized direction ID of the network traffic.", "enum": [0, 1, 2, 3, 99]}, "protocol_name": {"type": "string", "description": "The network protocol name in lowercase."}, "protocol_num": {"type": "integer", "description": "The network protocol number."}, "protocol_ver": {"type": "string", "description": "The network protocol version."}, "tcp_flags": {"type": "integer", "description": "The network TCP header flags."}, "uid": {"type": "string", "description": "The unique identifier of the connection."}}}, "count": {"type": "integer", "description": "The number of times that events in the same logical group occurred during the event Start Time to End Time period."}, "dst_endpoint": {"type": "object", "description": "The destination endpoint information.", "properties": {"domain": {"type": "string", "description": "The network domain where the endpoint resides."}, "hostname": {"type": "string", "description": "The fully qualified name of the endpoint."}, "instance_uid": {"type": "string", "description": "The unique identifier of a VM instance."}, "interface_name": {"type": "string", "description": "The name of the network interface."}, "interface_uid": {"type": "string", "description": "The unique identifier of the network interface."}, "intermediate_ips": {"type": "array", "description": "The intermediate IP addresses.", "items": {"type": "string"}}, "ip": {"type": "string", "description": "The IP address of the endpoint, in either IPv4 or IPv6 format."}, "location": {"type": "object", "description": "The geographical location of the endpoint.", "properties": {"city": {"type": "string", "description": "The name of the city."}, "continent": {"type": "string", "description": "The name of the continent."}, "coordinates": {"type": "array", "description": "A two-element array, containing a longitude/latitude pair. The format is [longitude, latitude].", "items": {"type": "number"}, "minItems": 2, "maxItems": 2}, "country": {"type": "string", "description": "The ISO 3166-1 Alpha-2 country code."}, "desc": {"type": "string", "description": "The description of the geographical location."}, "isp": {"type": "string", "description": "The name of the Internet Service Provider (ISP)."}, "postal_code": {"type": "string", "description": "The postal code of the location."}, "provider": {"type": "string", "description": "The provider of the geographical location data."}, "region": {"type": "string", "description": "The alphanumeric code that identifies the principal subdivision (e.g. province or state) of the country."}}}, "mac": {"type": "string", "description": "The Media Access Control (MAC) address of the endpoint."}, "name": {"type": "string", "description": "The short name of the endpoint."}, "port": {"type": "integer", "description": "The port used for communication within the network connection."}, "subnet_uid": {"type": "string", "description": "The unique identifier of a virtual subnet."}, "svc_name": {"type": "string", "description": "The service name in service-to-service connections."}, "uid": {"type": "string", "description": "The unique identifier of the endpoint."}, "vlan_uid": {"type": "string", "description": "The Virtual LAN identifier."}, "vpc_uid": {"type": "string", "description": "The unique identifier of the Virtual Private Cloud (VPC)."}}}, "duration": {"type": "integer", "description": "The event duration or aggregate time, the amount of time the event covers from start_time to end_time in milliseconds."}, "end_time": {"type": "integer", "description": "The end time of a time period, or the time of the most recent event included in the aggregate event."}, "end_time_dt": {"type": "string", "format": "date-time", "description": "The end time of a time period, or the time of the most recent event included in the aggregate event."}, "message": {"type": "string", "description": "The description of the event, as defined by the event source."}, "metadata": {"type": "object", "description": "The metadata associated with the event.", "properties": {"correlation_uid": {"type": "string", "description": "The unique identifier used to correlate events."}, "event_code": {"type": "string", "description": "The Event ID or Code that the product uses to describe the event."}, "extension": {"type": "object", "description": "The schema extension for event attributes that are not present in the base schema."}, "labels": {"type": "array", "description": "The list of labels/tags associated to a event.", "items": {"type": "string"}}, "log_name": {"type": "string", "description": "The event log name."}, "log_provider": {"type": "string", "description": "The logging provider or logging service that logged the event."}, "log_version": {"type": "string", "description": "The event log schema version that specifies the format of the original event."}, "logged_time": {"type": "integer", "description": "The time when the logging system collected and logged the event. This attribute is distinct from the event time itself, to account for delays in transmission."}, "logged_time_dt": {"type": "string", "format": "date-time", "description": "The time when the logging system collected and logged the event. This attribute is distinct from the event time itself, to account for delays in transmission."}, "modified_time": {"type": "integer", "description": "The time when the event was last modified or enriched."}, "modified_time_dt": {"type": "string", "format": "date-time", "description": "The time when the event was last modified or enriched."}, "original_time": {"type": "string", "description": "The original event time as reported by the event source. For example, the time in the original format from system event log such as Syslog."}, "processed_time": {"type": "integer", "description": "The event processed time, such as an ETL operation."}, "processed_time_dt": {"type": "string", "format": "date-time", "description": "The event processed time, such as an ETL operation."}, "product": {"type": "object", "description": "The product that reported the event.", "properties": {"feature": {"type": "object", "description": "The feature information.", "properties": {"name": {"type": "string", "description": "The name of the feature."}, "uid": {"type": "string", "description": "The unique identifier of the feature."}, "version": {"type": "string", "description": "The version of the feature."}}}, "lang": {"type": "string", "description": "The two letter lower case language codes, as defined by ISO 639-1."}, "name": {"type": "string", "description": "The name of the product."}, "path": {"type": "string", "description": "The installation path of the product."}, "uid": {"type": "string", "description": "The unique identifier of the product."}, "url_string": {"type": "string", "description": "The URL pointing towards the product."}, "vendor_name": {"type": "string", "description": "The name of the vendor of the product."}, "version": {"type": "string", "description": "The version of the product, as defined by the event source."}}}, "profiles": {"type": "array", "description": "The list of profiles used to create the event.", "items": {"type": "string"}}, "sequence": {"type": "integer", "description": "Sequence number of the event. The sequence number is a value available in some events, to make the exact ordering of events unambiguous, regardless of the timestamp precision."}, "tenant_uid": {"type": "string", "description": "The unique identifier of the tenant (UUID)."}, "uid": {"type": "string", "description": "The logging system-assigned unique identifier of an event instance."}, "version": {"type": "string", "description": "The version of the OCSF schema, using Semantic Versioning Specification (SemVer)."}}}, "observables": {"type": "array", "description": "The observables associated with the event.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The full name of the observable attribute."}, "reputation": {"type": "object", "description": "The reputation information for the observable."}, "type": {"type": "string", "description": "The observable value type name."}, "type_id": {"type": "integer", "description": "The observable value type identifier."}, "value": {"type": "string", "description": "The value associated with the observable attribute."}}, "required": ["name", "type_id", "value"]}}, "raw_data": {"type": "string", "description": "The event data as received from the event source."}, "severity": {"type": "string", "description": "The event severity, normalized to the caption of the severity_id value. In the case of 'Other', it is defined by the event source."}, "severity_id": {"type": "integer", "description": "The normalized identifier of the event severity.", "enum": [0, 1, 2, 3, 4, 5, 6, 99]}, "src_endpoint": {"type": "object", "description": "The source endpoint information.", "properties": {"domain": {"type": "string", "description": "The network domain where the endpoint resides."}, "hostname": {"type": "string", "description": "The fully qualified name of the endpoint."}, "ip": {"type": "string", "description": "The IP address of the endpoint, in either IPv4 or IPv6 format."}, "port": {"type": "integer", "description": "The port used for communication within the network connection."}}}, "start_time": {"type": "integer", "description": "The start time of a time period, or the time of the least recent event included in the aggregate event."}, "start_time_dt": {"type": "string", "format": "date-time", "description": "The start time of a time period, or the time of the least recent event included in the aggregate event."}, "status": {"type": "string", "description": "The event status, normalized to the caption of the status_id value. In the case of 'Other', it is defined by the event source."}, "status_code": {"type": "string", "description": "The event status code, as reported by the event source."}, "status_detail": {"type": "string", "description": "The status details contains additional information about the event outcome."}, "status_id": {"type": "integer", "description": "The normalized identifier of the event status.", "enum": [0, 1, 2, 99]}, "time": {"type": "integer", "description": "The normalized event occurrence time."}, "time_dt": {"type": "string", "format": "date-time", "description": "The normalized event occurrence time."}, "timezone_offset": {"type": "integer", "description": "The number of minutes that the reported event time is ahead or behind UTC, in the range -1,080 to +1,080."}, "type_name": {"type": "string", "description": "The event type name, as defined by the type_uid."}, "type_uid": {"type": "integer", "description": "The event type ID. It identifies the event's semantics and structure. The value is calculated by the logging system as: class_uid * 100 + activity_id."}, "unmapped": {"type": "object", "description": "The attributes that are not mapped to the event schema. The names and values of those attributes are specific to the event source."}}, "required": ["activity_id", "category_uid", "class_uid", "severity_id", "time", "type_uid"], "metadata": {"profiles": ["network"], "version": "1.3.0"}}
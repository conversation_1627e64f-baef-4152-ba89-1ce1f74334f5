package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/internal/cep"
	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/logger"
)

var (
	cfgFile    string
	logLevel   string
	outputFormat string
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "cep-manager",
		Short: "Complex Event Processing Manager",
		Long:  "A CLI tool for managing Complex Event Processing patterns, correlations, and threat detection",
	}

	// Global flags
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is ./configs/config.yaml)")
	rootCmd.PersistentFlags().StringVar(&logLevel, "log-level", "info", "log level (debug, info, warn, error)")
	rootCmd.PersistentFlags().StringVar(&outputFormat, "output", "json", "output format (json, table, yaml)")

	// Add subcommands
	rootCmd.AddCommand(
		newRunCommand(),
		newValidateCommand(),
		newStatsCommand(),
		newPatternsCommand(),
		newTestCommand(),
	)

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func newRunCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "run",
		Short: "Run the CEP engine",
		Long:  "Start the CEP engine and process events",
		RunE:  runCEPEngine,
	}

	cmd.Flags().Duration("duration", 0, "run for specified duration (0 = run indefinitely)")
	cmd.Flags().Bool("dry-run", false, "validate configuration without starting the engine")

	return cmd
}

func newValidateCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "validate",
		Short: "Validate CEP configuration",
		Long:  "Validate the CEP configuration file and patterns",
		RunE:  validateConfiguration,
	}
}

func newStatsCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "stats",
		Short: "Show CEP statistics",
		Long:  "Display CEP engine statistics and performance metrics",
		RunE:  showStats,
	}

	cmd.Flags().Bool("reset", false, "reset statistics after displaying")
	cmd.Flags().Duration("interval", 0, "refresh interval for continuous monitoring")

	return cmd
}

func newPatternsCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "patterns",
		Short: "Manage CEP patterns",
		Long:  "List, validate, and manage CEP patterns",
	}

	cmd.AddCommand(
		&cobra.Command{
			Use:   "list",
			Short: "List all patterns",
			RunE:  listPatterns,
		},
		&cobra.Command{
			Use:   "validate",
			Short: "Validate patterns",
			RunE:  validatePatterns,
		},
	)

	return cmd
}

func newTestCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "test",
		Short: "Test CEP with sample events",
		Long:  "Send test events to the CEP engine for validation",
		RunE:  testCEP,
	}

	cmd.Flags().String("event-file", "", "JSON file containing test events")
	cmd.Flags().Int("count", 10, "number of test events to generate")
	cmd.Flags().Duration("interval", time.Second, "interval between test events")

	return cmd
}

func initConfig() (*config.Config, *zap.Logger, error) {
	// Initialize Viper
	if cfgFile != "" {
		viper.SetConfigFile(cfgFile)
	} else {
		viper.SetConfigName("config")
		viper.SetConfigType("yaml")
		viper.AddConfigPath("./configs")
		viper.AddConfigPath(".")
	}

	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		return nil, nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var cfg config.Config
	if err := viper.Unmarshal(&cfg); err != nil {
		return nil, nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Initialize logger
	loggerConfig := logger.Config{
		Level:      logLevel,
		Format:     "json",
		OutputPath: "stdout",
	}

	log, err := logger.New(loggerConfig)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to initialize logger: %w", err)
	}

	return &cfg, log, nil
}

func runCEPEngine(cmd *cobra.Command, args []string) error {
	cfg, log, err := initConfig()
	if err != nil {
		return err
	}

	duration, _ := cmd.Flags().GetDuration("duration")
	dryRun, _ := cmd.Flags().GetBool("dry-run")

	// Create CEP service
	service, err := cep.NewService(cfg.CEP, log)
	if err != nil {
		return fmt.Errorf("failed to create CEP service: %w", err)
	}

	// Validate configuration
	if err := service.ValidateConfiguration(); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	if dryRun {
		log.Info("Configuration validation successful (dry-run mode)")
		return nil
	}

	// Create context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle duration limit
	if duration > 0 {
		ctx, cancel = context.WithTimeout(ctx, duration)
		defer cancel()
	}

	// Handle shutdown signals
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigCh
		log.Info("Shutdown signal received")
		cancel()
	}()

	// Start CEP service
	if err := service.Start(ctx); err != nil {
		return fmt.Errorf("failed to start CEP service: %w", err)
	}

	log.Info("CEP engine started", zap.Duration("duration", duration))

	// Wait for context cancellation
	<-ctx.Done()

	// Stop service
	service.Stop()
	log.Info("CEP engine stopped")

	return nil
}

func validateConfiguration(cmd *cobra.Command, args []string) error {
	cfg, log, err := initConfig()
	if err != nil {
		return err
	}

	service, err := cep.NewService(cfg.CEP, log)
	if err != nil {
		return fmt.Errorf("failed to create CEP service: %w", err)
	}

	if err := service.ValidateConfiguration(); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	fmt.Println("✓ Configuration validation successful")
	return nil
}

func showStats(cmd *cobra.Command, args []string) error {
	cfg, log, err := initConfig()
	if err != nil {
		return err
	}

	service, err := cep.NewService(cfg.CEP, log)
	if err != nil {
		return fmt.Errorf("failed to create CEP service: %w", err)
	}

	reset, _ := cmd.Flags().GetBool("reset")
	interval, _ := cmd.Flags().GetDuration("interval")

	if interval > 0 {
		// Continuous monitoring
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		for {
			stats := service.GetStats()
			if err := outputData(stats); err != nil {
				return err
			}

			select {
			case <-ticker.C:
				continue
			case <-context.Background().Done():
				return nil
			}
		}
	} else {
		// Single stats display
		stats := service.GetStats()
		if err := outputData(stats); err != nil {
			return err
		}

		if reset {
			service.ResetStats()
			fmt.Println("Statistics reset")
		}
	}

	return nil
}

func listPatterns(cmd *cobra.Command, args []string) error {
	cfg, log, err := initConfig()
	if err != nil {
		return err
	}

	service, err := cep.NewService(cfg.CEP, log)
	if err != nil {
		return fmt.Errorf("failed to create CEP service: %w", err)
	}

	patterns := service.GetPatterns()
	return outputData(patterns)
}

func validatePatterns(cmd *cobra.Command, args []string) error {
	cfg, log, err := initConfig()
	if err != nil {
		return err
	}

	service, err := cep.NewService(cfg.CEP, log)
	if err != nil {
		return fmt.Errorf("failed to create CEP service: %w", err)
	}

	patterns := service.GetPatterns()
	validPatterns := 0
	totalPatterns := len(patterns)

	for id, pattern := range patterns {
		if pattern.Enabled && len(pattern.Conditions) > 0 {
			validPatterns++
			fmt.Printf("✓ Pattern %s: %s\n", id, pattern.Name)
		} else {
			fmt.Printf("⚠ Pattern %s: %s (disabled or invalid)\n", id, pattern.Name)
		}
	}

	fmt.Printf("\nValidation Summary: %d/%d patterns valid\n", validPatterns, totalPatterns)
	return nil
}

func testCEP(cmd *cobra.Command, args []string) error {
	cfg, log, err := initConfig()
	if err != nil {
		return err
	}

	eventFile, _ := cmd.Flags().GetString("event-file")
	count, _ := cmd.Flags().GetInt("count")
	interval, _ := cmd.Flags().GetDuration("interval")

	service, err := cep.NewService(cfg.CEP, log)
	if err != nil {
		return fmt.Errorf("failed to create CEP service: %w", err)
	}

	ctx := context.Background()
	if err := service.Start(ctx); err != nil {
		return fmt.Errorf("failed to start CEP service: %w", err)
	}
	defer service.Stop()

	var events []map[string]interface{}

	if eventFile != "" {
		// Load events from file
		data, err := os.ReadFile(eventFile)
		if err != nil {
			return fmt.Errorf("failed to read event file: %w", err)
		}

		if err := json.Unmarshal(data, &events); err != nil {
			return fmt.Errorf("failed to parse event file: %w", err)
		}
	} else {
		// Generate test events
		events = generateTestEvents(count)
	}

	fmt.Printf("Processing %d test events...\n", len(events))

	for i, event := range events {
		result, err := service.ProcessEvent(ctx, event)
		if err != nil {
			fmt.Printf("Error processing event %d: %v\n", i+1, err)
			continue
		}

		fmt.Printf("Event %d: %d pattern matches\n", i+1, result.MatchCount)
		for _, match := range result.PatternMatches {
			fmt.Printf("  - Pattern: %s (confidence: %.2f)\n", match.PatternName, match.Confidence)
		}

		if i < len(events)-1 {
			time.Sleep(interval)
		}
	}

	// Show final stats
	stats := service.GetStats()
	fmt.Printf("\nFinal Statistics:\n")
	fmt.Printf("Events Processed: %d\n", stats.EngineStats.EventsProcessed)
	fmt.Printf("Patterns Matched: %d\n", stats.EngineStats.PatternsMatched)

	return nil
}

func generateTestEvents(count int) []map[string]interface{} {
	events := make([]map[string]interface{}, count)

	for i := 0; i < count; i++ {
		events[i] = map[string]interface{}{
			"class_uid":     4001,
			"activity_name": "Network Activity",
			"severity_id":   2,
			"timestamp":     time.Now().Unix(),
			"src_endpoint": map[string]interface{}{
				"ip": fmt.Sprintf("192.168.1.%d", i%254+1),
			},
			"dst_endpoint": map[string]interface{}{
				"ip": "********",
			},
			"traffic": map[string]interface{}{
				"bytes_out": float64(1024 * (i + 1)),
			},
		}
	}

	return events
}

func outputData(data interface{}) error {
	switch outputFormat {
	case "json":
		encoder := json.NewEncoder(os.Stdout)
		encoder.SetIndent("", "  ")
		return encoder.Encode(data)
	case "yaml":
		// For simplicity, output as JSON for now
		// In a real implementation, you'd use a YAML library
		return outputData(data)
	default:
		return fmt.Errorf("unsupported output format: %s", outputFormat)
	}
}

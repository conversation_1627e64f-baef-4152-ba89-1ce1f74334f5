package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/internal/schema"
	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/database"
	"github.com/gollm/core-gollmslake-go/pkg/logging"
)

var (
	configFile = flag.String("config", "configs/schema-reconciler.yaml", "Configuration file path")
	dryRun     = flag.Bool("dry-run", false, "Perform dry run without making changes")
	once       = flag.Bool("once", false, "Run reconciliation once and exit")
	validate   = flag.String("validate", "", "Validate schema file and exit")
	apply      = flag.String("apply", "", "Apply schema file and exit")
	schemaType = flag.String("type", "cassandra", "Schema type (cassandra, opensearch)")
)

func main() {
	flag.Parse()

	// Load configuration
	cfg, err := config.LoadConfig(*configFile)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Override dry run from command line
	if *dryRun {
		cfg.SchemaReconciler.DryRun = true
	}

	// Initialize logger
	logger, err := logging.NewLogger(cfg.Logging)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	logger.Info("Starting schema reconciler",
		zap.String("config_file", *configFile),
		zap.Bool("dry_run", cfg.SchemaReconciler.DryRun),
		zap.Bool("once", *once))

	// Initialize Cassandra client
	cassandraClient, err := database.NewCassandraClient(cfg.Cassandra, logger)
	if err != nil {
		logger.Fatal("Failed to initialize Cassandra client", zap.Error(err))
	}
	defer cassandraClient.Close()

	// Create schema service
	schemaService, err := schema.NewService(cfg.SchemaReconciler, cassandraClient, logger)
	if err != nil {
		logger.Fatal("Failed to create schema service", zap.Error(err))
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle validation mode
	if *validate != "" {
		if err := handleValidation(ctx, schemaService, *validate, *schemaType, logger); err != nil {
			logger.Fatal("Schema validation failed", zap.Error(err))
		}
		return
	}

	// Handle apply mode
	if *apply != "" {
		if err := handleApply(ctx, schemaService, *apply, *schemaType, cfg.SchemaReconciler.DryRun, logger); err != nil {
			logger.Fatal("Schema application failed", zap.Error(err))
		}
		return
	}

	// Handle once mode
	if *once {
		if err := handleOnce(ctx, schemaService, logger); err != nil {
			logger.Fatal("One-time reconciliation failed", zap.Error(err))
		}
		return
	}

	// Start continuous reconciliation
	go func() {
		if err := schemaService.Start(ctx); err != nil && err != context.Canceled {
			logger.Error("Schema service failed", zap.Error(err))
			cancel()
		}
	}()

	// Wait for shutdown signal
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	select {
	case sig := <-sigCh:
		logger.Info("Received shutdown signal", zap.String("signal", sig.String()))
	case <-ctx.Done():
		logger.Info("Context cancelled")
	}

	// Graceful shutdown
	logger.Info("Shutting down schema reconciler")
	schemaService.Stop()

	// Give some time for graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer shutdownCancel()

	<-shutdownCtx.Done()
	logger.Info("Schema reconciler stopped")
}

// handleValidation handles schema validation mode
func handleValidation(ctx context.Context, service *schema.Service, filePath, schemaType string, logger *zap.Logger) error {
	logger.Info("Validating schema file",
		zap.String("file", filePath),
		zap.String("type", schemaType))

	content, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read schema file: %w", err)
	}

	if err := service.ValidateSchema(ctx, string(content), schemaType); err != nil {
		logger.Error("Schema validation failed", zap.Error(err))
		return err
	}

	logger.Info("Schema validation successful")
	return nil
}

// handleApply handles schema application mode
func handleApply(ctx context.Context, service *schema.Service, filePath, schemaType string, dryRun bool, logger *zap.Logger) error {
	logger.Info("Applying schema file",
		zap.String("file", filePath),
		zap.String("type", schemaType),
		zap.Bool("dry_run", dryRun))

	content, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read schema file: %w", err)
	}

	result, err := service.ApplySchema(ctx, string(content), schemaType, dryRun)
	if err != nil {
		logger.Error("Schema application failed", zap.Error(err))
		return err
	}

	logger.Info("Schema application completed",
		zap.Bool("success", result.Success),
		zap.Int("changes", len(result.Changes)),
		zap.Int("errors", len(result.Errors)),
		zap.Duration("duration", result.Duration))

	if len(result.Changes) > 0 {
		logger.Info("Schema changes applied:")
		for _, change := range result.Changes {
			logger.Info("  " + change)
		}
	}

	if len(result.Errors) > 0 {
		logger.Error("Schema application errors:")
		for _, errMsg := range result.Errors {
			logger.Error("  " + errMsg)
		}
		return fmt.Errorf("schema application completed with errors")
	}

	return nil
}

// handleOnce handles one-time reconciliation mode
func handleOnce(ctx context.Context, service *schema.Service, logger *zap.Logger) error {
	logger.Info("Running one-time schema reconciliation")

	// Get current status
	status, err := service.GetStatus(ctx)
	if err != nil {
		return fmt.Errorf("failed to get schema status: %w", err)
	}

	logger.Info("Current schema status",
		zap.String("health", status.Health),
		zap.Int("total_schemas", status.TotalSchemas),
		zap.Int("successful_schemas", status.SuccessfulSchemas),
		zap.Int("failed_schemas", status.FailedSchemas))

	// List schemas
	schemas, err := service.ListSchemas(ctx)
	if err != nil {
		return fmt.Errorf("failed to list schemas: %w", err)
	}

	logger.Info("Found schemas", zap.Int("count", len(schemas)))
	for _, schema := range schemas {
		logger.Info("Schema",
			zap.String("name", schema.Name),
			zap.String("type", schema.Type),
			zap.String("status", schema.Status))
	}

	return nil
}

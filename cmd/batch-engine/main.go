package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/spf13/cobra"
	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/internal/batch"
	"github.com/gollm/core-gollmslake-go/pkg/config"
)

var (
	configFile string
	verbose    bool
	engine     *batch.Engine
	log        *zap.Logger
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "batch-engine",
		Short: "Batch Processing Engine for large-scale data processing",
		Long: `A comprehensive batch processing engine that provides an alternative to Spark jobs
for large-scale data processing, historical data analysis, and data compaction operations.

Features:
- Job scheduling and prioritization
- Worker pool management
- Progress tracking and monitoring
- Failure recovery and retry mechanisms
- Support for compaction, aggregation, and enrichment jobs`,
		PersistentPreRun: initializeEngine,
	}

	// Global flags
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "configs/batch-engine.yaml", "Configuration file path")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "Enable verbose logging")

	// Add subcommands
	rootCmd.AddCommand(
		startCmd(),
		statusCmd(),
		jobsCmd(),
		submitCmd(),
		cancelCmd(),
		metricsCmd(),
	)

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func initializeEngine(cmd *cobra.Command, args []string) {
	// Initialize logger
	var err error
	if verbose {
		log, err = zap.NewDevelopment()
	} else {
		log, err = zap.NewProduction()
	}
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}

	// Load configuration
	cfg := &config.Config{
		Batch: config.BatchConfig{
			Enabled:    true,
			MaxWorkers: 10,
			QueueSize:  100,
		},
	}

	// Create batch engine
	engine, err = batch.NewEngine(cfg, log)
	if err != nil {
		log.Fatal("Failed to create batch engine", zap.Error(err))
	}
}

func startCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "start",
		Short: "Start the batch processing engine",
		Long:  "Start the batch processing engine and begin processing jobs",
		Run: func(cmd *cobra.Command, args []string) {
			log.Info("Starting batch processing engine")

			// Start the engine
			if err := engine.Start(); err != nil {
				log.Fatal("Failed to start engine", zap.Error(err))
			}

			// Set up signal handling for graceful shutdown
			sigChan := make(chan os.Signal, 1)
			signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

			log.Info("Batch processing engine started successfully")
			log.Info("Press Ctrl+C to stop")

			// Wait for shutdown signal
			<-sigChan
			log.Info("Shutdown signal received, stopping engine...")

			// Stop the engine
			if err := engine.Stop(); err != nil {
				log.Error("Error stopping engine", zap.Error(err))
			} else {
				log.Info("Engine stopped successfully")
			}
		},
	}
}

func statusCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "status",
		Short: "Show engine status and metrics",
		Long:  "Display the current status of the batch processing engine and key metrics",
		Run: func(cmd *cobra.Command, args []string) {
			metrics := engine.GetMetrics()

			fmt.Printf("Batch Processing Engine Status\n")
			fmt.Printf("==============================\n\n")
			fmt.Printf("Jobs:\n")
			fmt.Printf("  Total Jobs:     %d\n", metrics.TotalJobs)
			fmt.Printf("  Active Jobs:    %d\n", metrics.ActiveJobs)
			fmt.Printf("  Queued Jobs:    %d\n", metrics.QueuedJobs)
			fmt.Printf("  Completed Jobs: %d\n", metrics.CompletedJobs)
			fmt.Printf("  Failed Jobs:    %d\n", metrics.FailedJobs)
			fmt.Printf("\n")
			fmt.Printf("Performance:\n")
			fmt.Printf("  Processing Rate:  %.2f jobs/min\n", metrics.ProcessingRate)
			fmt.Printf("  Average Latency:  %.2f seconds\n", metrics.AverageLatency)
			fmt.Printf("  Total Records:    %d\n", metrics.TotalRecords)
			fmt.Printf("\n")
			fmt.Printf("Timestamps:\n")
			fmt.Printf("  Last Job:         %s\n", formatTime(metrics.LastJobTime))
			fmt.Printf("  Last Completed:   %s\n", formatTime(metrics.LastCompletedJob))
		},
	}
}

func jobsCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "jobs",
		Short: "Job management commands",
		Long:  "Commands for managing and monitoring batch jobs",
	}

	// List jobs subcommand
	listCmd := &cobra.Command{
		Use:   "list",
		Short: "List jobs",
		Long:  "List all jobs with optional filtering",
		Run: func(cmd *cobra.Command, args []string) {
			status, _ := cmd.Flags().GetString("status")
			jobType, _ := cmd.Flags().GetString("type")
			limit, _ := cmd.Flags().GetInt("limit")

			filter := batch.JobFilter{
				Status: status,
				Type:   jobType,
			}

			jobs, err := engine.ListJobs(filter)
			if err != nil {
				log.Fatal("Failed to list jobs", zap.Error(err))
			}

			if len(jobs) == 0 {
				fmt.Println("No jobs found")
				return
			}

			// Limit results if specified
			if limit > 0 && len(jobs) > limit {
				jobs = jobs[:limit]
			}

			fmt.Printf("%-20s %-30s %-15s %-10s %-20s %-10s\n",
				"ID", "Name", "Type", "Status", "Created", "Duration")
			fmt.Println(strings.Repeat("-", 110))

			for _, job := range jobs {
				duration := ""
				if job.Status == "completed" || job.Status == "failed" {
					duration = job.Duration.String()
				} else if job.Status == "running" {
					duration = time.Since(job.StartedAt).String()
				}

				fmt.Printf("%-20s %-30s %-15s %-10s %-20s %-10s\n",
					truncateString(job.ID, 20),
					truncateString(job.Name, 30),
					job.Type,
					job.Status,
					job.CreatedAt.Format("2006-01-02 15:04:05"),
					duration,
				)
			}
		},
	}

	listCmd.Flags().String("status", "", "Filter by job status")
	listCmd.Flags().String("type", "", "Filter by job type")
	listCmd.Flags().Int("limit", 50, "Limit number of results")

	// Show job details subcommand
	showCmd := &cobra.Command{
		Use:   "show <job-id>",
		Short: "Show job details",
		Long:  "Show detailed information about a specific job",
		Args:  cobra.ExactArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			jobID := args[0]

			job, err := engine.GetJob(jobID)
			if err != nil {
				log.Fatal("Failed to get job", zap.String("job_id", jobID), zap.Error(err))
			}

			// Pretty print job details
			jobJSON, err := json.MarshalIndent(job, "", "  ")
			if err != nil {
				log.Fatal("Failed to marshal job", zap.Error(err))
			}

			fmt.Println(string(jobJSON))
		},
	}

	cmd.AddCommand(listCmd, showCmd)
	return cmd
}

func submitCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "submit",
		Short: "Submit a new job",
		Long:  "Submit a new batch processing job",
		Run: func(cmd *cobra.Command, args []string) {
			jobFile, _ := cmd.Flags().GetString("file")
			jobType, _ := cmd.Flags().GetString("type")
			jobName, _ := cmd.Flags().GetString("name")
			priority, _ := cmd.Flags().GetInt("priority")
			sync, _ := cmd.Flags().GetBool("sync")

			var job *batch.Job

			if jobFile != "" {
				// Load job from file
				data, err := os.ReadFile(jobFile)
				if err != nil {
					log.Fatal("Failed to read job file", zap.String("file", jobFile), zap.Error(err))
				}

				job = &batch.Job{}
				if err := json.Unmarshal(data, job); err != nil {
					log.Fatal("Failed to parse job file", zap.Error(err))
				}
			} else {
				// Create job from command line parameters
				if jobType == "" || jobName == "" {
					log.Fatal("Job type and name are required when not using a job file")
				}

				job = &batch.Job{
					Name:     jobName,
					Type:     jobType,
					Priority: priority,
					Config: batch.JobConfig{
						Parallelism: 4,
						BatchSize:   1000,
						Timeout:     1 * time.Hour,
					},
					Input: batch.JobInput{
						Type:    "kafka",
						Sources: []string{"input-topic"},
						Format:  "json",
					},
					Output: batch.JobOutput{
						Type:   "kafka",
						Target: "output-topic",
						Format: "json",
					},
				}
			}

			// Submit job
			ctx := context.Background()
			if sync {
				ctx = context.WithValue(ctx, "sync", true)
			}

			result, err := engine.SubmitJob(ctx, job)
			if err != nil {
				log.Fatal("Failed to submit job", zap.Error(err))
			}

			fmt.Printf("Job submitted successfully\n")
			fmt.Printf("Job ID: %s\n", result.JobID)
			fmt.Printf("Status: %s\n", result.Status)

			if sync && result.Status != "queued" {
				fmt.Printf("Duration: %s\n", result.Duration)
				fmt.Printf("Records Processed: %d\n", result.RecordsWritten)
			}
		},
	}

	cmd.Flags().String("file", "", "Job definition file (JSON)")
	cmd.Flags().String("type", "", "Job type (compaction, aggregation, enrichment)")
	cmd.Flags().String("name", "", "Job name")
	cmd.Flags().Int("priority", 0, "Job priority")
	cmd.Flags().Bool("sync", false, "Wait for job completion")

	return cmd
}

func cancelCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "cancel <job-id>",
		Short: "Cancel a job",
		Long:  "Cancel a running or queued job",
		Args:  cobra.ExactArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			jobID := args[0]

			if err := engine.CancelJob(jobID); err != nil {
				log.Fatal("Failed to cancel job", zap.String("job_id", jobID), zap.Error(err))
			}

			fmt.Printf("Job %s cancelled successfully\n", jobID)
		},
	}
}

func metricsCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "metrics",
		Short: "Show detailed metrics",
		Long:  "Display detailed metrics in JSON format",
		Run: func(cmd *cobra.Command, args []string) {
			metrics := engine.GetMetrics()

			metricsJSON, err := json.MarshalIndent(metrics, "", "  ")
			if err != nil {
				log.Fatal("Failed to marshal metrics", zap.Error(err))
			}

			fmt.Println(string(metricsJSON))
		},
	}
}

func formatTime(t time.Time) string {
	if t.IsZero() {
		return "Never"
	}
	return t.Format("2006-01-02 15:04:05")
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}

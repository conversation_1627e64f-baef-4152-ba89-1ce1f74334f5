package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/internal/archival"
	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/logger"
)

var (
	cfgFile string
	verbose bool
)

func main() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

var rootCmd = &cobra.Command{
	Use:   "archival-service",
	Short: "Enhanced archival service with retention policies and compliance reporting",
	Long: `The archival service provides enhanced raw log archival capabilities with:
- Retention policy management
- Compliance reporting and audit trails
- Data lifecycle management
- Policy-based archival rules
- Integration with OpenSearch ISM policies`,
	Run: runService,
}

var startCmd = &cobra.Command{
	Use:   "start",
	Short: "Start the archival service",
	Long:  "Start the archival service with retention policy management and compliance reporting",
	Run:   runService,
}

var statusCmd = &cobra.Command{
	Use:   "status",
	Short: "Show archival service status and metrics",
	Long:  "Display current status, metrics, and statistics for the archival service",
	Run:   showStatus,
}

var policiesCmd = &cobra.Command{
	Use:   "policies",
	Short: "Manage retention policies",
	Long:  "List, add, update, or delete retention policies for different data types and sources",
}

var listPoliciesCmd = &cobra.Command{
	Use:   "list",
	Short: "List all retention policies",
	Long:  "Display all configured retention policies with their details",
	Run:   listPolicies,
}

var complianceCmd = &cobra.Command{
	Use:   "compliance",
	Short: "Manage compliance reporting",
	Long:  "Generate compliance reports and view compliance events",
}

var generateReportCmd = &cobra.Command{
	Use:   "report",
	Short: "Generate compliance report",
	Long:  "Generate a compliance report for the specified time period",
	Run:   generateComplianceReport,
}

func init() {
	cobra.OnInitialize(initConfig)

	// Global flags
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is ./config.yaml)")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "verbose output")

	// Add subcommands
	rootCmd.AddCommand(startCmd)
	rootCmd.AddCommand(statusCmd)
	rootCmd.AddCommand(policiesCmd)
	rootCmd.AddCommand(complianceCmd)

	// Policy subcommands
	policiesCmd.AddCommand(listPoliciesCmd)

	// Compliance subcommands
	complianceCmd.AddCommand(generateReportCmd)
}

func initConfig() {
	if cfgFile != "" {
		viper.SetConfigFile(cfgFile)
	} else {
		viper.SetConfigName("config")
		viper.SetConfigType("yaml")
		viper.AddConfigPath(".")
		viper.AddConfigPath("./configs")
		viper.AddConfigPath("/etc/gollmslake")
	}

	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		fmt.Fprintf(os.Stderr, "Warning: Could not read config file: %v\n", err)
	}
}

func runService(cmd *cobra.Command, args []string) {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger
	logLevel := zap.InfoLevel
	if verbose {
		logLevel = zap.DebugLevel
	}

	zapLogger, err := logger.NewLogger(logger.Config{
		Level:  logLevel.String(),
		Format: "json",
	})
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer zapLogger.Sync()

	// Create archival service
	service, err := archival.NewService(cfg, zapLogger)
	if err != nil {
		zapLogger.Fatal("Failed to create archival service", zap.Error(err))
	}

	// Start service
	if err := service.Start(); err != nil {
		zapLogger.Fatal("Failed to start archival service", zap.Error(err))
	}

	zapLogger.Info("Archival service started successfully",
		zap.Bool("policy_manager_enabled", cfg.Archive.PolicyManager.Enabled),
		zap.Bool("compliance_reporting_enabled", cfg.Archive.ComplianceReport.Enabled),
		zap.Bool("lifecycle_manager_enabled", cfg.Archive.LifecycleManager.Enabled),
	)

	// Setup graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle shutdown signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start metrics reporting
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				metrics := service.GetMetrics()
				zapLogger.Info("Archival service metrics",
					zap.Int64("total_archived", metrics.TotalArchived),
					zap.Int64("total_deleted", metrics.TotalDeleted),
					zap.Int64("policy_violations", metrics.PolicyViolations),
					zap.Int64("compliance_reports", metrics.ComplianceReports),
					zap.Int("active_retention_rules", metrics.ActiveRetentionRules),
					zap.Time("last_policy_check", metrics.LastPolicyCheck),
					zap.Time("last_compliance_report", metrics.LastComplianceReport),
				)
			}
		}
	}()

	// Wait for shutdown signal
	<-sigChan
	zapLogger.Info("Received shutdown signal, stopping archival service...")

	// Stop service gracefully
	if err := service.Stop(); err != nil {
		zapLogger.Error("Error stopping archival service", zap.Error(err))
	} else {
		zapLogger.Info("Archival service stopped successfully")
	}
}

func showStatus(cmd *cobra.Command, args []string) {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger
	zapLogger, err := logger.NewLogger(logger.Config{
		Level:  "info",
		Format: "console",
	})
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer zapLogger.Sync()

	// Create archival service (without starting it)
	service, err := archival.NewService(cfg, zapLogger)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to create archival service: %v\n", err)
		os.Exit(1)
	}

	// Get metrics
	metrics := service.GetMetrics()

	// Display status
	fmt.Println("Archival Service Status")
	fmt.Println("======================")
	fmt.Printf("Total Archived: %d\n", metrics.TotalArchived)
	fmt.Printf("Total Deleted: %d\n", metrics.TotalDeleted)
	fmt.Printf("Policy Violations: %d\n", metrics.PolicyViolations)
	fmt.Printf("Compliance Reports: %d\n", metrics.ComplianceReports)
	fmt.Printf("Active Retention Rules: %d\n", metrics.ActiveRetentionRules)
	fmt.Printf("Last Policy Check: %s\n", metrics.LastPolicyCheck.Format(time.RFC3339))
	fmt.Printf("Last Compliance Report: %s\n", metrics.LastComplianceReport.Format(time.RFC3339))

	// Configuration status
	fmt.Println("\nConfiguration")
	fmt.Println("=============")
	fmt.Printf("Archive Enabled: %t\n", cfg.Archive.Enabled)
	fmt.Printf("Policy Manager Enabled: %t\n", cfg.Archive.PolicyManager.Enabled)
	fmt.Printf("Compliance Reporting Enabled: %t\n", cfg.Archive.ComplianceReport.Enabled)
	fmt.Printf("Lifecycle Manager Enabled: %t\n", cfg.Archive.LifecycleManager.Enabled)
	fmt.Printf("Buffer Size: %d\n", cfg.Archive.BufferSize)
	fmt.Printf("Flush Interval: %s\n", cfg.Archive.FlushInterval)
	fmt.Printf("Compression: %t\n", cfg.Archive.Compression)
	fmt.Printf("Default Retention: %s\n", cfg.Archive.Retention)
}

func listPolicies(cmd *cobra.Command, args []string) {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger
	zapLogger, err := logger.NewLogger(logger.Config{
		Level:  "info",
		Format: "console",
	})
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer zapLogger.Sync()

	// Create archival service
	service, err := archival.NewService(cfg, zapLogger)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to create archival service: %v\n", err)
		os.Exit(1)
	}

	// Get policies
	policies := service.GetPolicyManager().ListPolicies()

	// Display policies
	fmt.Println("Retention Policies")
	fmt.Println("==================")
	fmt.Printf("Total Policies: %d\n\n", len(policies))

	for _, policy := range policies {
		fmt.Printf("ID: %s\n", policy.ID)
		fmt.Printf("Name: %s\n", policy.Name)
		fmt.Printf("Description: %s\n", policy.Description)
		fmt.Printf("Source: %s\n", policy.Source)
		fmt.Printf("Data Type: %s\n", policy.DataType)
		fmt.Printf("Retention Period: %s\n", policy.RetentionPeriod)
		fmt.Printf("Compression: %s\n", policy.CompressionType)
		fmt.Printf("Storage Tier: %s\n", policy.StorageTier)
		fmt.Printf("Active: %t\n", policy.Active)
		fmt.Printf("Priority: %d\n", policy.Priority)
		fmt.Printf("Created: %s\n", policy.CreatedAt.Format(time.RFC3339))
		fmt.Printf("Updated: %s\n", policy.UpdatedAt.Format(time.RFC3339))

		if policy.Compliance != nil {
			fmt.Printf("Compliance:\n")
			fmt.Printf("  Regulations: %v\n", policy.Compliance.Regulations)
			fmt.Printf("  Classification: %s\n", policy.Compliance.Classification)
			fmt.Printf("  Encryption: %t\n", policy.Compliance.Encryption)
			fmt.Printf("  Audit: %t\n", policy.Compliance.Audit)
			fmt.Printf("  Immutable: %t\n", policy.Compliance.Immutable)
		}

		fmt.Println("---")
	}
}

func generateComplianceReport(cmd *cobra.Command, args []string) {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger
	zapLogger, err := logger.NewLogger(logger.Config{
		Level:  "info",
		Format: "console",
	})
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer zapLogger.Sync()

	// Create archival service
	service, err := archival.NewService(cfg, zapLogger)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to create archival service: %v\n", err)
		os.Exit(1)
	}

	// Generate compliance report
	report, err := service.GetComplianceReporter().GenerateReport()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to generate compliance report: %v\n", err)
		os.Exit(1)
	}

	// Display report
	fmt.Println("Compliance Report")
	fmt.Println("=================")
	fmt.Printf("Report ID: %s\n", report.ID)
	fmt.Printf("Generated At: %s\n", report.GeneratedAt.Format(time.RFC3339))
	fmt.Printf("Period: %s to %s (%s)\n",
		report.Period.StartTime.Format(time.RFC3339),
		report.Period.EndTime.Format(time.RFC3339),
		report.Period.Duration)

	fmt.Println("\nSummary")
	fmt.Println("-------")
	fmt.Printf("Total Events: %d\n", report.Summary.TotalEvents)
	fmt.Printf("Successful Events: %d\n", report.Summary.SuccessfulEvents)
	fmt.Printf("Failed Events: %d\n", report.Summary.FailedEvents)
	fmt.Printf("Total Violations: %d\n", report.Summary.TotalViolations)

	if len(report.Summary.EventsByType) > 0 {
		fmt.Println("\nEvents by Type:")
		for eventType, count := range report.Summary.EventsByType {
			fmt.Printf("  %s: %d\n", eventType, count)
		}
	}

	if len(report.Summary.RegulationsCovered) > 0 {
		fmt.Printf("\nRegulations Covered: %v\n", report.Summary.RegulationsCovered)
	}

	if len(report.Violations) > 0 {
		fmt.Println("\nViolations")
		fmt.Println("----------")
		for _, violation := range report.Violations {
			fmt.Printf("ID: %s\n", violation.ID)
			fmt.Printf("Type: %s\n", violation.Type)
			fmt.Printf("Severity: %s\n", violation.Severity)
			fmt.Printf("Description: %s\n", violation.Description)
			fmt.Printf("Source: %s\n", violation.Source)
			fmt.Printf("Status: %s\n", violation.Status)
			fmt.Println("---")
		}
	}

	if len(report.Recommendations) > 0 {
		fmt.Println("\nRecommendations")
		fmt.Println("---------------")
		for i, recommendation := range report.Recommendations {
			fmt.Printf("%d. %s\n", i+1, recommendation)
		}
	}
}

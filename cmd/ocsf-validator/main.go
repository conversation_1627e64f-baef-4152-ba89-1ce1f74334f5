package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"strings"

	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/internal/ocsf"
	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/logging"
)

var (
	configFile   = flag.String("config", "configs/schema-reconciler.yaml", "Configuration file path")
	schemaPath   = flag.String("schemas", "./schemas/ocsf", "OCSF schemas directory path")
	eventFile    = flag.String("event", "", "JSON file containing event to validate")
	eventJSON    = flag.String("json", "", "JSON string containing event to validate")
	batchFile    = flag.String("batch", "", "JSON file containing array of events to validate")
	listSchemas  = flag.Bool("list", false, "List all available schemas")
	schemaInfo   = flag.String("info", "", "Show information about a specific schema")
	validateMode = flag.String("mode", "strict", "Validation mode: strict, lenient, disabled")
	outputFormat = flag.String("output", "json", "Output format: json, table, summary")
	verbose      = flag.Bool("verbose", false, "Enable verbose output")
)

func main() {
	flag.Parse()

	// Load configuration
	cfg, err := config.LoadConfig(*configFile)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Override OCSF configuration from command line
	if *schemaPath != "" {
		cfg.OCSFSchemas.SchemasPath = *schemaPath
	}
	if *validateMode != "" {
		cfg.OCSFSchemas.ValidationMode = *validateMode
	}
	cfg.OCSFSchemas.Enabled = true

	// Initialize logger
	logLevel := "info"
	if *verbose {
		logLevel = "debug"
	}
	cfg.Logging.Level = logLevel

	logger, err := logging.NewLogger(cfg.Logging)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	// Create OCSF service
	ocsfService, err := ocsf.NewService(cfg.OCSFSchemas, logger)
	if err != nil {
		logger.Fatal("Failed to create OCSF service", zap.Error(err))
	}

	ctx := context.Background()

	// Handle different commands
	switch {
	case *listSchemas:
		handleListSchemas(ctx, ocsfService, logger)
	case *schemaInfo != "":
		handleSchemaInfo(ctx, ocsfService, *schemaInfo, logger)
	case *eventFile != "":
		handleValidateFile(ctx, ocsfService, *eventFile, false, logger)
	case *eventJSON != "":
		handleValidateJSON(ctx, ocsfService, *eventJSON, logger)
	case *batchFile != "":
		handleValidateFile(ctx, ocsfService, *batchFile, true, logger)
	default:
		printUsage()
		os.Exit(1)
	}
}

// handleListSchemas lists all available schemas
func handleListSchemas(ctx context.Context, service *ocsf.Service, logger *zap.Logger) {
	schemas, err := service.ListSchemas()
	if err != nil {
		logger.Fatal("Failed to list schemas", zap.Error(err))
	}

	switch *outputFormat {
	case "json":
		output, _ := json.MarshalIndent(schemas, "", "  ")
		fmt.Println(string(output))
	case "table":
		fmt.Printf("%-30s %-10s %-15s %-10s %s\n", "NAME", "VERSION", "CATEGORY", "CLASS_UID", "DESCRIPTION")
		fmt.Println(strings.Repeat("-", 100))
		for _, schema := range schemas {
			desc := schema.Description
			if len(desc) > 40 {
				desc = desc[:37] + "..."
			}
			fmt.Printf("%-30s %-10s %-15s %-10d %s\n",
				schema.Name, schema.Version, schema.Category, schema.ClassUID, desc)
		}
	case "summary":
		fmt.Printf("Total schemas: %d\n", len(schemas))
		categories := make(map[string]int)
		for _, schema := range schemas {
			categories[schema.Category]++
		}
		fmt.Println("\nBy category:")
		for category, count := range categories {
			fmt.Printf("  %s: %d\n", category, count)
		}
	}
}

// handleSchemaInfo shows information about a specific schema
func handleSchemaInfo(ctx context.Context, service *ocsf.Service, schemaName string, logger *zap.Logger) {
	info, err := service.GetSchemaInfo(schemaName)
	if err != nil {
		logger.Fatal("Failed to get schema info", zap.String("schema", schemaName), zap.Error(err))
	}

	switch *outputFormat {
	case "json":
		output, _ := json.MarshalIndent(info, "", "  ")
		fmt.Println(string(output))
	default:
		fmt.Printf("Schema: %s\n", info.Name)
		fmt.Printf("Version: %s\n", info.Version)
		fmt.Printf("Description: %s\n", info.Description)
		fmt.Printf("Category: %s\n", info.Category)
		fmt.Printf("Class UID: %d\n", info.ClassUID)
		fmt.Printf("Type: %s\n", info.Type)
		fmt.Printf("Properties: %d\n", info.Properties)
		fmt.Printf("Required fields: %s\n", strings.Join(info.Required, ", "))
		if len(info.Metadata) > 0 {
			fmt.Println("Metadata:")
			for k, v := range info.Metadata {
				fmt.Printf("  %s: %v\n", k, v)
			}
		}
	}
}

// handleValidateFile validates events from a file
func handleValidateFile(ctx context.Context, service *ocsf.Service, filename string, isBatch bool, logger *zap.Logger) {
	content, err := os.ReadFile(filename)
	if err != nil {
		logger.Fatal("Failed to read file", zap.String("file", filename), zap.Error(err))
	}

	if isBatch {
		handleValidateBatch(ctx, service, string(content), logger)
	} else {
		handleValidateJSON(ctx, service, string(content), logger)
	}
}

// handleValidateJSON validates a single event from JSON
func handleValidateJSON(ctx context.Context, service *ocsf.Service, jsonStr string, logger *zap.Logger) {
	var event map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &event); err != nil {
		logger.Fatal("Failed to parse JSON event", zap.Error(err))
	}

	result, err := service.ValidateEvent(ctx, event)
	if err != nil {
		logger.Fatal("Validation failed", zap.Error(err))
	}

	outputResult(result, logger)
}

// handleValidateBatch validates multiple events from JSON array
func handleValidateBatch(ctx context.Context, service *ocsf.Service, jsonStr string, logger *zap.Logger) {
	var events []map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &events); err != nil {
		logger.Fatal("Failed to parse JSON events array", zap.Error(err))
	}

	response, err := service.ValidateBatch(ctx, events)
	if err != nil {
		logger.Fatal("Batch validation failed", zap.Error(err))
	}

	outputBatchResults(response, logger)
}

// outputResult outputs a single validation result
func outputResult(result *ocsf.ValidationResult, logger *zap.Logger) {
	switch *outputFormat {
	case "json":
		output, _ := json.MarshalIndent(result, "", "  ")
		fmt.Println(string(output))
	case "summary":
		fmt.Printf("Validation: %s\n", getValidationStatus(result.Valid))
		fmt.Printf("Schema: %s\n", result.SchemaName)
		fmt.Printf("Class UID: %d\n", result.ClassUID)
		fmt.Printf("Errors: %d\n", len(result.Errors))
		fmt.Printf("Warnings: %d\n", len(result.Warnings))

		if len(result.Errors) > 0 {
			fmt.Println("\nErrors:")
			for _, err := range result.Errors {
				fmt.Printf("  - %s: %s\n", err.Field, err.Message)
			}
		}

		if len(result.Warnings) > 0 {
			fmt.Println("\nWarnings:")
			for _, warning := range result.Warnings {
				fmt.Printf("  - %s: %s\n", warning.Field, warning.Message)
			}
		}
	default:
		// Table format
		fmt.Printf("%-15s %-20s %-10s %-8s %-10s\n", "STATUS", "SCHEMA", "CLASS_UID", "ERRORS", "WARNINGS")
		fmt.Println(strings.Repeat("-", 70))
		fmt.Printf("%-15s %-20s %-10d %-8d %-10d\n",
			getValidationStatus(result.Valid),
			result.SchemaName,
			result.ClassUID,
			len(result.Errors),
			len(result.Warnings))

		if *verbose && len(result.Errors) > 0 {
			fmt.Println("\nErrors:")
			for _, err := range result.Errors {
				fmt.Printf("  %s: %s (value: %v)\n", err.Field, err.Message, err.Value)
			}
		}
	}
}

// outputBatchResults outputs batch validation results
func outputBatchResults(response *ocsf.BatchValidationResponse, logger *zap.Logger) {
	switch *outputFormat {
	case "json":
		output, _ := json.MarshalIndent(response, "", "  ")
		fmt.Println(string(output))
	case "summary":
		fmt.Printf("Batch Validation Summary\n")
		fmt.Printf("========================\n")
		fmt.Printf("Total events: %d\n", response.Summary.Total)
		fmt.Printf("Valid events: %d\n", response.Summary.Valid)
		fmt.Printf("Invalid events: %d\n", response.Summary.Invalid)
		fmt.Printf("Total errors: %d\n", response.Summary.Errors)
		fmt.Printf("Total warnings: %d\n", response.Summary.Warnings)
		fmt.Printf("Duration: %v\n", response.Duration)

		// Show schema distribution
		schemaCount := make(map[string]int)
		for _, result := range response.Results {
			schemaCount[result.SchemaName]++
		}

		if len(schemaCount) > 0 {
			fmt.Println("\nEvents by schema:")
			for schema, count := range schemaCount {
				fmt.Printf("  %s: %d\n", schema, count)
			}
		}
	default:
		// Table format
		fmt.Printf("Batch validation completed in %v\n", response.Duration)
		fmt.Printf("%-5s %-15s %-20s %-10s %-8s %-10s\n", "#", "STATUS", "SCHEMA", "CLASS_UID", "ERRORS", "WARNINGS")
		fmt.Println(strings.Repeat("-", 80))

		for i, result := range response.Results {
			fmt.Printf("%-5d %-15s %-20s %-10d %-8d %-10d\n",
				i+1,
				getValidationStatus(result.Valid),
				result.SchemaName,
				result.ClassUID,
				len(result.Errors),
				len(result.Warnings))
		}

		fmt.Printf("\nSummary: %d/%d valid\n", response.Summary.Valid, response.Summary.Total)
	}
}

// getValidationStatus returns a human-readable validation status
func getValidationStatus(valid bool) string {
	if valid {
		return "✓ VALID"
	}
	return "✗ INVALID"
}

// printUsage prints usage information
func printUsage() {
	fmt.Println("OCSF Validator - Validate events against OCSF schemas")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  ocsf-validator [options] <command>")
	fmt.Println()
	fmt.Println("Commands:")
	fmt.Println("  -list                    List all available schemas")
	fmt.Println("  -info <schema>           Show information about a specific schema")
	fmt.Println("  -event <file>            Validate single event from JSON file")
	fmt.Println("  -json <json>             Validate single event from JSON string")
	fmt.Println("  -batch <file>            Validate multiple events from JSON array file")
	fmt.Println()
	fmt.Println("Options:")
	fmt.Println("  -config <file>           Configuration file path (default: configs/schema-reconciler.yaml)")
	fmt.Println("  -schemas <path>          OCSF schemas directory path (default: ./schemas/ocsf)")
	fmt.Println("  -mode <mode>             Validation mode: strict, lenient, disabled (default: strict)")
	fmt.Println("  -output <format>         Output format: json, table, summary (default: json)")
	fmt.Println("  -verbose                 Enable verbose output")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  ocsf-validator -list")
	fmt.Println("  ocsf-validator -info network_activity")
	fmt.Println("  ocsf-validator -event event.json")
	fmt.Println("  ocsf-validator -json '{\"class_uid\": 4001, \"activity_id\": 1}'")
	fmt.Println("  ocsf-validator -batch events.json -output summary")
}

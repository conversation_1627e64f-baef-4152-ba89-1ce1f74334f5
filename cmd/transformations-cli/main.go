package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/cobra"
	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/internal/pipeline"
	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/logger"
)

var (
	version = "dev"
	commit  = "unknown"
)

var (
	configFile   string
	pipelineDir  string
	outputFormat string
	verbose      bool
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "transformations-cli",
		Short: "Pipeline transformation and validation CLI",
		Long: `A CLI tool for managing data transformation pipelines including:
- Pipeline validation with OCSF schema checking
- Schema inference from output examples  
- Pipeline transformation application
- Output file updates with current transformations`,
		Version: fmt.Sprintf("%s (commit: %s)", version, commit),
	}

	// Global flags
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "configs/schema-reconciler.yaml", "Configuration file path")
	rootCmd.PersistentFlags().StringVarP(&pipelineDir, "pipeline-dir", "d", ".", "Pipeline directory path")
	rootCmd.PersistentFlags().StringVarP(&outputFormat, "output", "o", "json", "Output format (json, yaml, table)")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "Enable verbose output")

	// Add subcommands
	rootCmd.AddCommand(
		newValidateCommand(),
		newInferSchemaCommand(),
		newApplyCommand(),
		newUpdateOutputsCommand(),
		newListCommand(),
	)

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func newValidateCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "validate",
		Short: "Validate pipeline configuration and examples",
		Long: `Validates a pipeline by:
- Loading the pipeline definition and transformation
- Applying transformations to input examples
- Validating outputs against OCSF schemas
- Checking custom schema compliance if specified`,
		RunE: validatePipeline,
	}
}

func newInferSchemaCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "infer-schema",
		Short: "Infer JSON schema from pipeline output examples",
		Long: `Analyzes pipeline output examples and generates a JSON schema that describes
the structure and types of the transformed data.`,
		RunE: inferSchema,
	}
}

func newApplyCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "apply [input-file]",
		Short: "Apply pipeline transformation to input data",
		Long: `Applies the pipeline transformation to the specified input file and outputs
the transformed result.`,
		Args: cobra.ExactArgs(1),
		RunE: applyPipeline,
	}

	return cmd
}

func newUpdateOutputsCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "update-outputs",
		Short: "Update pipeline output files with current transformation results",
		Long: `Regenerates all pipeline output example files by applying the current
transformation to the corresponding input files.`,
		RunE: updateOutputs,
	}
}

func newListCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "list [directory]",
		Short: "List all pipeline definitions in a directory",
		Long:  "Recursively searches for pipeline.yaml files and lists all found pipelines.",
		Args:  cobra.MaximumNArgs(1),
		RunE:  listPipelines,
	}

	return cmd
}

func initializeService() (*pipeline.Service, error) {
	// Initialize logger
	logLevel := "info"
	if verbose {
		logLevel = "debug"
	}

	loggerConfig := logger.Config{
		Level:      logLevel,
		Format:     "console",
		OutputPath: "stderr",
	}

	log, err := logger.New(loggerConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize logger: %w", err)
	}

	// Create minimal configuration for CLI
	// Resolve schemas path relative to the repository root, not the pipeline directory
	schemasPath := "./schemas/ocsf"
	if !filepath.IsAbs(schemasPath) {
		// If we're in a subdirectory, find the repository root
		currentDir, _ := os.Getwd()
		for {
			if _, err := os.Stat(filepath.Join(currentDir, "schemas", "ocsf")); err == nil {
				schemasPath = filepath.Join(currentDir, "schemas", "ocsf")
				break
			}
			parent := filepath.Dir(currentDir)
			if parent == currentDir {
				// Reached filesystem root, use relative path
				schemasPath = "./schemas/ocsf"
				break
			}
			currentDir = parent
		}
	}

	cfg := config.Config{
		OCSFSchemas: config.OCSFSchemasConfig{
			Enabled:        true,
			SchemasPath:    schemasPath,
			Version:        "1.3.0",
			ValidationMode: "lenient",
			CacheEnabled:   false,
		},
	}

	// Try to load configuration file if it exists, but don't fail if it doesn't
	if _, err := os.Stat(configFile); err == nil {
		loadedCfg, err := config.LoadConfig(configFile)
		if err != nil {
			log.Warn("Failed to load configuration file, using defaults",
				zap.String("config_file", configFile),
				zap.Error(err))
		} else {
			// Use loaded OCSF config if available
			if loadedCfg.OCSFSchemas.SchemasPath != "" {
				cfg.OCSFSchemas = loadedCfg.OCSFSchemas
			}
		}
	}

	// Create pipeline service
	service, err := pipeline.NewService(cfg, log)
	if err != nil {
		return nil, fmt.Errorf("failed to create pipeline service: %w", err)
	}

	// Set working directory
	absDir, err := filepath.Abs(pipelineDir)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve pipeline directory: %w", err)
	}
	service.SetWorkingDirectory(absDir)

	return service, nil
}

func validatePipeline(cmd *cobra.Command, args []string) error {
	service, err := initializeService()
	if err != nil {
		return err
	}

	// Load pipeline definition
	definition, err := service.LoadDefinition("pipeline.yaml")
	if err != nil {
		return fmt.Errorf("failed to load pipeline definition: %w", err)
	}

	// Validate pipeline
	result, err := service.ValidatePipeline(definition)
	if err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// Output results
	return outputData(result)
}

func inferSchema(cmd *cobra.Command, args []string) error {
	service, err := initializeService()
	if err != nil {
		return err
	}

	// Load pipeline definition
	definition, err := service.LoadDefinition("pipeline.yaml")
	if err != nil {
		return fmt.Errorf("failed to load pipeline definition: %w", err)
	}

	// Infer schema
	schema, err := service.InferSchema(definition)
	if err != nil {
		return fmt.Errorf("schema inference failed: %w", err)
	}

	// Output schema
	return outputData(schema)
}

func applyPipeline(cmd *cobra.Command, args []string) error {
	service, err := initializeService()
	if err != nil {
		return err
	}

	inputFile := args[0]

	// Load pipeline definition
	definition, err := service.LoadDefinition("pipeline.yaml")
	if err != nil {
		return fmt.Errorf("failed to load pipeline definition: %w", err)
	}

	// Read input file
	inputPath := filepath.Join(pipelineDir, inputFile)
	inputData, err := os.ReadFile(inputPath)
	if err != nil {
		return fmt.Errorf("failed to read input file: %w", err)
	}

	// Apply pipeline
	result, err := service.ApplyPipeline(definition, inputData)
	if err != nil {
		return fmt.Errorf("pipeline application failed: %w", err)
	}

	if !result.Success {
		return fmt.Errorf("transformation failed: %s", result.Error)
	}

	// Output transformed data
	var prettyOutput interface{}
	if err := json.Unmarshal(result.Output, &prettyOutput); err != nil {
		fmt.Print(string(result.Output))
	} else {
		prettyData, err := json.MarshalIndent(prettyOutput, "", "  ")
		if err != nil {
			fmt.Print(string(result.Output))
		} else {
			fmt.Print(string(prettyData))
		}
	}

	return nil
}

func updateOutputs(cmd *cobra.Command, args []string) error {
	service, err := initializeService()
	if err != nil {
		return err
	}

	// Load pipeline definition
	definition, err := service.LoadDefinition("pipeline.yaml")
	if err != nil {
		return fmt.Errorf("failed to load pipeline definition: %w", err)
	}

	// Update outputs
	result, err := service.UpdatePipelineOutputs(definition)
	if err != nil {
		return fmt.Errorf("output update failed: %w", err)
	}

	// Output results
	return outputData(result)
}

func listPipelines(cmd *cobra.Command, args []string) error {
	service, err := initializeService()
	if err != nil {
		return err
	}

	searchDir := pipelineDir
	if len(args) > 0 {
		searchDir = args[0]
	}

	// List pipelines
	pipelines, err := service.ListPipelines(searchDir)
	if err != nil {
		return fmt.Errorf("failed to list pipelines: %w", err)
	}

	// Output pipeline list
	result := map[string]interface{}{
		"pipelines": pipelines,
		"count":     len(pipelines),
		"directory": searchDir,
	}

	return outputData(result)
}

func outputData(data interface{}) error {
	switch outputFormat {
	case "json":
		encoder := json.NewEncoder(os.Stdout)
		encoder.SetIndent("", "  ")
		return encoder.Encode(data)
	case "yaml":
		// For now, output as JSON
		// In a real implementation, you'd use a YAML library
		return outputData(data)
	case "table":
		// For now, output as JSON
		// In a real implementation, you'd format as a table
		return outputData(data)
	default:
		return fmt.Errorf("unsupported output format: %s", outputFormat)
	}
}

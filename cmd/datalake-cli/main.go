package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/spf13/cobra"
	"go.uber.org/zap"

	"github.com/gollm/core-gollmslake-go/internal/datalake"
	"github.com/gollm/core-gollmslake-go/pkg/config"
	"github.com/gollm/core-gollmslake-go/pkg/logger"
)

var (
	configFile string
	verbose    bool
	output     string
	client     *datalake.Client
	log        *zap.Logger
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "datalake-cli",
		Short: "Data Lake CLI for Iceberg table operations",
		Long:  "A command-line interface for managing Iceberg tables and performing data lake operations",
		PersistentPreRun: func(cmd *cobra.Command, args []string) {
			initializeClient()
		},
		PersistentPostRun: func(cmd *cobra.Command, args []string) {
			if client != nil {
				client.Stop()
			}
		},
	}

	// Global flags
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "configs/datalake.yaml", "config file path")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "verbose output")
	rootCmd.PersistentFlags().StringVarP(&output, "output", "o", "table", "output format (table, json, yaml)")

	// Add subcommands
	rootCmd.AddCommand(
		createNamespaceCmd(),
		listNamespacesCmd(),
		dropNamespaceCmd(),
		createTableCmd(),
		listTablesCmd(),
		dropTableCmd(),
		describeTableCmd(),
		writeDataCmd(),
		readDataCmd(),
		queryCmd(),
		analyticsCmd(),
		statusCmd(),
	)

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func initializeClient() {
	// Initialize logger
	var err error
	if verbose {
		log, err = logger.NewLogger("debug")
	} else {
		log, err = logger.NewLogger("info")
	}
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}

	// Load configuration
	cfg, err := config.LoadConfig(configFile)
	if err != nil {
		log.Fatal("Failed to load configuration", zap.Error(err))
	}

	// Create data lake client
	client, err = datalake.NewClient(cfg, log)
	if err != nil {
		log.Fatal("Failed to create data lake client", zap.Error(err))
	}

	// Start client
	if err := client.Start(); err != nil {
		log.Fatal("Failed to start data lake client", zap.Error(err))
	}
}

func createNamespaceCmd() *cobra.Command {
	var properties []string

	cmd := &cobra.Command{
		Use:   "create-namespace <name>",
		Short: "Create a new namespace",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			name := args[0]

			// Parse properties
			props := make(map[string]string)
			for _, prop := range properties {
				parts := strings.SplitN(prop, "=", 2)
				if len(parts) == 2 {
					props[parts[0]] = parts[1]
				}
			}

			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			// This would need to be implemented in the client
			fmt.Printf("Creating namespace: %s\n", name)
			if len(props) > 0 {
				fmt.Printf("Properties: %v\n", props)
			}

			return nil
		},
	}

	cmd.Flags().StringSliceVarP(&properties, "property", "p", nil, "namespace properties (key=value)")

	return cmd
}

func listNamespacesCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "list-namespaces",
		Short: "List all namespaces",
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			namespaces, err := client.ListTables(ctx, "") // This would need to be ListNamespaces
			if err != nil {
				return fmt.Errorf("failed to list namespaces: %w", err)
			}

			switch output {
			case "json":
				return json.NewEncoder(os.Stdout).Encode(namespaces)
			default:
				fmt.Println("NAMESPACES")
				fmt.Println("----------")
				for _, ns := range namespaces {
					fmt.Println(ns.Namespace)
				}
			}

			return nil
		},
	}
}

func dropNamespaceCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "drop-namespace <name>",
		Short: "Drop a namespace",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			name := args[0]

			fmt.Printf("Dropping namespace: %s\n", name)
			// This would need to be implemented in the client

			return nil
		},
	}
}

func createTableCmd() *cobra.Command {
	var schemaFile string
	var location string
	var properties []string

	cmd := &cobra.Command{
		Use:   "create-table <namespace.table>",
		Short: "Create a new table",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			tableName := args[0]
			parts := strings.Split(tableName, ".")
			if len(parts) != 2 {
				return fmt.Errorf("table name must be in format namespace.table")
			}

			namespace := parts[0]
			name := parts[1]

			// Parse properties
			props := make(map[string]string)
			for _, prop := range properties {
				propParts := strings.SplitN(prop, "=", 2)
				if len(propParts) == 2 {
					props[propParts[0]] = propParts[1]
				}
			}

			ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
			defer cancel()

			// For now, create a simple schema
			// In practice, you'd load this from the schema file
			request := &datalake.CreateTableRequest{
				Namespace:  namespace,
				TableName:  name,
				Schema:     nil, // Would load from schemaFile
				Location:   location,
				Properties: props,
			}

			table, err := client.CreateTable(ctx, request)
			if err != nil {
				return fmt.Errorf("failed to create table: %w", err)
			}

			fmt.Printf("Created table: %s.%s (ID: %s)\n", table.Namespace, table.Name, table.ID)

			return nil
		},
	}

	cmd.Flags().StringVarP(&schemaFile, "schema", "s", "", "schema file path")
	cmd.Flags().StringVarP(&location, "location", "l", "", "table location")
	cmd.Flags().StringSliceVarP(&properties, "property", "p", nil, "table properties (key=value)")

	return cmd
}

func listTablesCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "list-tables [namespace]",
		Short: "List tables in a namespace",
		Args:  cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			namespace := "default"
			if len(args) > 0 {
				namespace = args[0]
			}

			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			tables, err := client.ListTables(ctx, namespace)
			if err != nil {
				return fmt.Errorf("failed to list tables: %w", err)
			}

			switch output {
			case "json":
				return json.NewEncoder(os.Stdout).Encode(tables)
			default:
				fmt.Printf("TABLES IN NAMESPACE: %s\n", namespace)
				fmt.Println("--------------------")
				for _, table := range tables {
					fmt.Printf("%s.%s\n", table.Namespace, table.Name)
				}
			}

			return nil
		},
	}
}

func dropTableCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "drop-table <namespace.table>",
		Short: "Drop a table",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			tableName := args[0]
			parts := strings.Split(tableName, ".")
			if len(parts) != 2 {
				return fmt.Errorf("table name must be in format namespace.table")
			}

			namespace := parts[0]
			name := parts[1]

			ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
			defer cancel()

			err := client.DropTable(ctx, namespace, name)
			if err != nil {
				return fmt.Errorf("failed to drop table: %w", err)
			}

			fmt.Printf("Dropped table: %s.%s\n", namespace, name)

			return nil
		},
	}
}

func describeTableCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "describe <namespace.table>",
		Short: "Describe a table",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			tableName := args[0]
			parts := strings.Split(tableName, ".")
			if len(parts) != 2 {
				return fmt.Errorf("table name must be in format namespace.table")
			}

			namespace := parts[0]
			name := parts[1]

			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			table, err := client.GetTable(ctx, namespace, name)
			if err != nil {
				return fmt.Errorf("failed to get table: %w", err)
			}

			switch output {
			case "json":
				return json.NewEncoder(os.Stdout).Encode(table)
			default:
				fmt.Printf("TABLE: %s.%s\n", table.Namespace, table.Name)
				fmt.Printf("ID: %s\n", table.ID)
				fmt.Printf("Location: %s\n", table.Location)
				fmt.Printf("Created: %s\n", table.CreatedAt.Format(time.RFC3339))
				fmt.Printf("Updated: %s\n", table.UpdatedAt.Format(time.RFC3339))
				fmt.Printf("Snapshots: %d\n", len(table.Snapshots))
			}

			return nil
		},
	}
}

func writeDataCmd() *cobra.Command {
	var dataFile string
	var writeMode string

	cmd := &cobra.Command{
		Use:   "write <namespace.table>",
		Short: "Write data to a table",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			tableName := args[0]
			parts := strings.Split(tableName, ".")
			if len(parts) != 2 {
				return fmt.Errorf("table name must be in format namespace.table")
			}

			namespace := parts[0]
			name := parts[1]

			// Load data from file or stdin
			var records []map[string]interface{}
			if dataFile != "" {
				// Load from file
				fmt.Printf("Loading data from file: %s\n", dataFile)
				// Implementation would load JSON/CSV data
			} else {
				// Sample data for demonstration
				records = []map[string]interface{}{
					{"id": 1, "name": "sample1", "timestamp": time.Now().Unix()},
					{"id": 2, "name": "sample2", "timestamp": time.Now().Unix()},
				}
			}

			ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
			defer cancel()

			request := &datalake.WriteDataRequest{
				Namespace: namespace,
				TableName: name,
				Records:   records,
				WriteMode: datalake.WriteMode(writeMode),
			}

			result, err := client.WriteData(ctx, request)
			if err != nil {
				return fmt.Errorf("failed to write data: %w", err)
			}

			fmt.Printf("Written %d records to %s.%s\n", result.RecordsWritten, namespace, name)
			fmt.Printf("Snapshot ID: %d\n", result.SnapshotID)
			fmt.Printf("Duration: %v\n", result.Duration)

			return nil
		},
	}

	cmd.Flags().StringVarP(&dataFile, "file", "f", "", "data file path")
	cmd.Flags().StringVarP(&writeMode, "mode", "m", "append", "write mode (append, overwrite, merge)")

	return cmd
}

func readDataCmd() *cobra.Command {
	var filter string
	var limit int64
	var projection []string

	cmd := &cobra.Command{
		Use:   "read <namespace.table>",
		Short: "Read data from a table",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			tableName := args[0]
			parts := strings.Split(tableName, ".")
			if len(parts) != 2 {
				return fmt.Errorf("table name must be in format namespace.table")
			}

			namespace := parts[0]
			name := parts[1]

			ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
			defer cancel()

			request := &datalake.ReadDataRequest{
				Namespace:  namespace,
				TableName:  name,
				Filter:     filter,
				Projection: projection,
			}

			if limit > 0 {
				request.Limit = &limit
			}

			result, err := client.ReadData(ctx, request)
			if err != nil {
				return fmt.Errorf("failed to read data: %w", err)
			}

			switch output {
			case "json":
				return json.NewEncoder(os.Stdout).Encode(result.Records)
			default:
				fmt.Printf("Read %d records from %s.%s\n", result.RecordsRead, namespace, name)
				fmt.Printf("Duration: %v\n", result.Duration)
				fmt.Println("\nDATA:")
				for i, record := range result.Records {
					fmt.Printf("Record %d: %v\n", i+1, record)
				}
			}

			return nil
		},
	}

	cmd.Flags().StringVarP(&filter, "filter", "f", "", "filter expression")
	cmd.Flags().Int64VarP(&limit, "limit", "l", 0, "limit number of records")
	cmd.Flags().StringSliceVarP(&projection, "select", "s", nil, "columns to select")

	return cmd
}

func queryCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "query <sql>",
		Short: "Execute a SQL query",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			sql := args[0]

			ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
			defer cancel()

			request := &datalake.QueryRequest{
				SQL: sql,
			}

			result, err := client.ExecuteQuery(ctx, request)
			if err != nil {
				return fmt.Errorf("failed to execute query: %w", err)
			}

			switch output {
			case "json":
				return json.NewEncoder(os.Stdout).Encode(result)
			default:
				fmt.Printf("Query executed in %v\n", result.Duration)
				fmt.Printf("Returned %d rows\n\n", result.RowCount)

				// Print column headers
				for i, col := range result.Columns {
					if i > 0 {
						fmt.Print("\t")
					}
					fmt.Print(col)
				}
				fmt.Println()

				// Print separator
				for i := range result.Columns {
					if i > 0 {
						fmt.Print("\t")
					}
					fmt.Print("--------")
				}
				fmt.Println()

				// Print rows
				for _, row := range result.Rows {
					for i, val := range row {
						if i > 0 {
							fmt.Print("\t")
						}
						fmt.Print(val)
					}
					fmt.Println()
				}
			}

			return nil
		},
	}
}

func analyticsCmd() *cobra.Command {
	var jobType string
	var tables []string
	var parameters []string

	cmd := &cobra.Command{
		Use:   "analytics",
		Short: "Run analytics jobs",
		RunE: func(cmd *cobra.Command, args []string) error {
			// Parse parameters
			params := make(map[string]interface{})
			for _, param := range parameters {
				parts := strings.SplitN(param, "=", 2)
				if len(parts) == 2 {
					params[parts[0]] = parts[1]
				}
			}

			ctx, cancel := context.WithTimeout(context.Background(), 600*time.Second)
			defer cancel()

			request := &datalake.AnalyticsRequest{
				JobID:      fmt.Sprintf("job-%d", time.Now().Unix()),
				JobType:    jobType,
				Tables:     tables,
				Parameters: params,
			}

			result, err := client.RunAnalytics(ctx, request)
			if err != nil {
				return fmt.Errorf("failed to run analytics: %w", err)
			}

			switch output {
			case "json":
				return json.NewEncoder(os.Stdout).Encode(result)
			default:
				fmt.Printf("Analytics job started: %s\n", result.JobID)
				fmt.Printf("Status: %s\n", result.Status)
				fmt.Printf("Duration: %v\n", result.Duration)
			}

			return nil
		},
	}

	cmd.Flags().StringVarP(&jobType, "type", "t", "table_stats", "analytics job type")
	cmd.Flags().StringSliceVar(&tables, "tables", nil, "tables to analyze")
	cmd.Flags().StringSliceVarP(&parameters, "param", "p", nil, "job parameters (key=value)")

	return cmd
}

func statusCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "status",
		Short: "Show data lake client status",
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()

			err := client.Health(ctx)
			if err != nil {
				fmt.Printf("Status: UNHEALTHY - %v\n", err)
				return nil
			}

			metrics := client.GetMetrics()

			switch output {
			case "json":
				return json.NewEncoder(os.Stdout).Encode(metrics)
			default:
				fmt.Println("Status: HEALTHY")
				fmt.Printf("Tables Created: %d\n", metrics.TablesCreated)
				fmt.Printf("Tables Dropped: %d\n", metrics.TablesDropped)
				fmt.Printf("Records Written: %d\n", metrics.RecordsWritten)
				fmt.Printf("Records Read: %d\n", metrics.RecordsRead)
				fmt.Printf("Queries Executed: %d\n", metrics.QueriesExecuted)
				fmt.Printf("Analytics Jobs: %d\n", metrics.AnalyticsJobs)
				fmt.Printf("Last Activity: %s\n", metrics.LastActivity.Format(time.RFC3339))
			}

			return nil
		},
	}
}

rules:
  - id: "high_severity_security_event"
    name: "High Severity Security Event"
    description: "Detects high severity security events that require immediate attention"
    enabled: true
    priority: 1
    category: "security"
    tags: ["security", "high-priority", "alert"]
    conditions:
      - field: "severity_id"
        operator: "gte"
        value: 4
      - field: "category_name"
        operator: "eq"
        value: "Security"
        logic_op: "and"
    actions:
      - type: "alert"
        enabled: true
        parameters:
          severity: "high"
          message: "High severity security event detected"
          notify_channels: ["security-team", "soc"]
      - type: "log"
        enabled: true
        parameters:
          level: "warn"
          message: "Security alert triggered"
    metadata:
      created_by: "security-team"
      last_updated: "2024-01-15"
      version: "1.0"

  - id: "failed_authentication_attempts"
    name: "Multiple Failed Authentication Attempts"
    description: "Detects multiple failed authentication attempts from the same source"
    enabled: true
    priority: 2
    category: "authentication"
    tags: ["authentication", "brute-force", "security"]
    conditions:
      - field: "activity_name"
        operator: "eq"
        value: "Logon"
      - field: "status_id"
        operator: "eq"
        value: 2
        logic_op: "and"
    actions:
      - type: "alert"
        enabled: true
        parameters:
          severity: "medium"
          message: "Multiple failed authentication attempts detected"
      - type: "enrich"
        enabled: true
        parameters:
          fields:
            threat_category: "brute_force"
            risk_score: 75
    aggregation:
      enabled: true
      window: "5m"
      group_by: ["src_endpoint.ip"]
      function: "count"
      threshold: 5
    suppression:
      enabled: true
      window: "15m"
      max_alerts: 3
      group_by: ["src_endpoint.ip"]

  - id: "suspicious_network_activity"
    name: "Suspicious Network Activity"
    description: "Detects suspicious network connections to known bad IPs"
    enabled: true
    priority: 2
    category: "network"
    tags: ["network", "threat-intel", "security"]
    conditions:
      - field: "class_uid"
        operator: "eq"
        value: 4001
      - field: "dst_endpoint.ip"
        operator: "regex"
        value: "^(10\\.0\\.0\\.|192\\.168\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.).*"
        negate: true
        logic_op: "and"
    actions:
      - type: "alert"
        enabled: true
        parameters:
          severity: "medium"
          message: "Suspicious network connection detected"
      - type: "enrich"
        enabled: true
        parameters:
          fields:
            threat_category: "suspicious_network"
            investigation_required: true
      - type: "forward"
        enabled: true
        parameters:
          destination: "threat-intel-queue"
    enrichment:
      enabled: true
      fields:
        enriched_timestamp: "{{current_time}}"
        rule_triggered: "suspicious_network_activity"
      sources: ["threat_intel", "geo_ip"]

  - id: "privilege_escalation_attempt"
    name: "Privilege Escalation Attempt"
    description: "Detects attempts to escalate privileges"
    enabled: true
    priority: 1
    category: "privilege"
    tags: ["privilege-escalation", "security", "critical"]
    conditions:
      - field: "activity_name"
        operator: "in"
        value: ["Privilege Use", "Account Change", "Group Change"]
      - field: "severity_id"
        operator: "gte"
        value: 3
        logic_op: "and"
    actions:
      - type: "alert"
        enabled: true
        parameters:
          severity: "critical"
          message: "Privilege escalation attempt detected"
          immediate_response: true
      - type: "block"
        enabled: false
        parameters:
          reason: "Privilege escalation attempt blocked"
      - type: "log"
        enabled: true
        parameters:
          level: "error"
          message: "Critical security event - privilege escalation"
    correlation:
      enabled: true
      window: "10m"
      rules: ["failed_authentication_attempts"]
      require_all: false
      correlate_by: ["actor.user.name", "src_endpoint.ip"]

  - id: "data_exfiltration_detection"
    name: "Data Exfiltration Detection"
    description: "Detects potential data exfiltration based on network traffic patterns"
    enabled: true
    priority: 1
    category: "data_loss"
    tags: ["data-exfiltration", "network", "security"]
    conditions:
      - field: "class_uid"
        operator: "eq"
        value: 4001
      - field: "traffic.bytes_out"
        operator: "gt"
        value: 10485760  # 10MB
        logic_op: "and"
    actions:
      - type: "alert"
        enabled: true
        parameters:
          severity: "high"
          message: "Potential data exfiltration detected"
      - type: "transform"
        enabled: true
        parameters:
          transforms:
            alert_category: "data_loss_prevention"
            requires_investigation: true
    aggregation:
      enabled: true
      window: "1h"
      group_by: ["src_endpoint.ip", "dst_endpoint.ip"]
      function: "sum"
      field: "traffic.bytes_out"
      threshold: 104857600  # 100MB
    schedule:
      enabled: true
      days_of_week: [1, 2, 3, 4, 5]  # Monday to Friday
      start_time: "09:00"
      end_time: "17:00"
      timezone: "UTC"

  - id: "malware_detection"
    name: "Malware Detection"
    description: "Detects potential malware based on file and process activity"
    enabled: true
    priority: 1
    category: "malware"
    tags: ["malware", "file", "process", "security"]
    conditions:
      - field: "class_uid"
        operator: "in"
        value: [1001, 1002, 1003]  # File, Process, Module events
      - field: "malware.name"
        operator: "ne"
        value: null
        logic_op: "and"
    actions:
      - type: "alert"
        enabled: true
        parameters:
          severity: "critical"
          message: "Malware detected"
          quarantine_required: true
      - type: "block"
        enabled: true
        parameters:
          reason: "Malware detected - blocking execution"
      - type: "enrich"
        enabled: true
        parameters:
          fields:
            threat_category: "malware"
            response_action: "quarantine"
            investigation_priority: "immediate"

rule_groups:
  - id: "critical_security_events"
    name: "Critical Security Events"
    description: "High priority security events requiring immediate attention"
    enabled: true
    priority: 1
    rules:
      - "high_severity_security_event"
      - "privilege_escalation_attempt"
      - "data_exfiltration_detection"
      - "malware_detection"
    metadata:
      escalation_required: true
      notification_channels: ["security-team", "soc", "incident-response"]

  - id: "authentication_monitoring"
    name: "Authentication Monitoring"
    description: "Monitor authentication-related security events"
    enabled: true
    priority: 2
    rules:
      - "failed_authentication_attempts"
    metadata:
      review_frequency: "daily"
      false_positive_rate: "low"

  - id: "network_security_monitoring"
    name: "Network Security Monitoring"
    description: "Monitor network-related security events"
    enabled: true
    priority: 2
    rules:
      - "suspicious_network_activity"
    metadata:
      baseline_required: true
      tuning_frequency: "weekly"
